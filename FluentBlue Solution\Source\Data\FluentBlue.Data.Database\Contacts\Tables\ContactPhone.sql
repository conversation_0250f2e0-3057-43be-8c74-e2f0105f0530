﻿CREATE TABLE [Contacts].[ContactPhone] (
    [ContactPhoneId]  UNIQUEIDENTIFIER CONSTRAINT [DF_ContactPhone_ContactPhoneId1] DEFAULT (newsequentialid()) NOT NULL,
    [ContactId]       UNIQUEIDENTIFIER NOT NULL,
    [Type]            TINYINT          NULL,
    [PhoneNumber]     NVARCHAR (25)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_ContactPhone_PhoneNumber] DEFAULT (N'') NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_ContactPhone_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_ContactPhone_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_ContactPhone_1] PRIMARY KEY CLUSTERED ([ContactPhoneId] ASC),
    CONSTRAINT [FK_ContactPhone_Contact] FOREIGN KEY ([ContactId]) REFERENCES [Contacts].[Contact] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE
);










GO



GO
CREATE NONCLUSTERED INDEX [UniquePhoneNumber]
    ON [Contacts].[ContactPhone]([ContactId] ASC, [PhoneNumber] ASC);


GO
CREATE NONCLUSTERED INDEX [ContactIdIndex]
    ON [Contacts].[ContactPhone]([ContactId] ASC);

