﻿<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly" AdditionalAssemblies="new[] { typeof(FluentBlue.UI.Main.Pages.Home).Assembly}">
        <Found Context="routeData">
            <AuthorizeView>
                <Authorizing>
                    <AuthorizeProgress></AuthorizeProgress>
                </Authorizing>
                <NotAuthorized>
                    <Login />
                </NotAuthorized>
                <Authorized>
                    <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
                </Authorized>
            </AuthorizeView>
        </Found>
        <NotFound>
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <p role="alert">Sorry, there's nothing at this address.</p>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>