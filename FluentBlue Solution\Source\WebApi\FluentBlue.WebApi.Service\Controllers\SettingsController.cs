﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Diagnostics;


namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class SettingsController : ControllerBase
    {
        private ISettingsBusiness settingsBusiness;
        private ILogger<SettingsController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public SettingsController(ISettingsBusiness settingsBusiness, ILogger<SettingsController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            try
            {
                this.settingsBusiness = settingsBusiness;
                this.logger = logger;
                this.mapper = mapper;
                this.webApiCallerInfo = webApiCallerInfo;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        [HttpPost]
        [Route("Setting/CreateOrUpdate")]
        public async Task<ApiResponse<UserSetting>> CreateOrUpdateUserSettings([FromBody] UserSetting userSetting)
        {
            try
            {
                //Validation
                //if (userSetting! == null)
                //{
                //    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                //}

                UserSettingValidator validator = new UserSettingValidator();
                ValidationResult result = validator.Validate(userSetting);

                if (result.IsValid == false)
                {
                    throw new Exception(result.Errors.ToString());
                }

                //Query
                await this.settingsBusiness.CreateOrUpdateUserSettings(userSetting);

                //Response
                UserSetting? userSettingResult = await this.settingsBusiness.GetUserSettings(userSetting.UserId);
                return new ApiResponse<UserSetting>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = userSetting };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<UserSetting>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, userSetting);
                return new ApiResponse<UserSetting>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }


    }
}
