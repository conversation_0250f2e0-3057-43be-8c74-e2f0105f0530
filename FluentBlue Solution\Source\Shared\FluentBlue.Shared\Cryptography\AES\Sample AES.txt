﻿Here's an example of encrypting and decrypting sensitive data using the implementations above:

// Encrypt sensitive data
var apiKey = "your-sensitive-api-key";
var encryptionResult = Encryptor.Encrypt(apiKey);

// Output example: DCGT9kEwPglBonWWPa7PQPbr2I+6rskJ0lSFybbicvZ+wKMTU7cbJD2s3QSF2Yu6

// Store encrypted data in database
// IV is stored with the encrypted data
SaveToDatabase(encryptionResult.EncryptedData);

// Store key in key vault
await keyVault.StoreKeyAsync("apikey_1", encryptionResult.Key);

// Later, decrypt when needed
// IV is retrieved from the encrypted data
var encryptedData = LoadFromDatabase();
var key = await keyVault.GetKeyAsync("apikey_1");

var result = new EncryptionResult
{
    EncryptedData = encryptedData,
    Key = key,
    IV = iv
};

var decrypted = Decryptor.Decrypt(result);