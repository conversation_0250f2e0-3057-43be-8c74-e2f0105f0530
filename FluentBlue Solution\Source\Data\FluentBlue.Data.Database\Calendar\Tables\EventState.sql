﻿CREATE TABLE [Calendar].[EventState] (
    [EventStateId]    UNIQUEIDENTIFIER CONSTRAINT [DF_Table_1_EventCategoryId] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]        UNIQUEIDENTIFIER NOT NULL,
    [Name]            NVARCHAR (50)    CONSTRAINT [DF_EventStatus_Title] DEFAULT ('') NOT NULL,
    [BackColor]       NVARCHAR (10)    CONSTRAINT [DF_EventStatus_BackColor] DEFAULT ('') NOT NULL,
    [SortIndex]       INT              NULL,
    [Disabled]        BIT              CONSTRAINT [DF_EventStatus_Disabled] DEFAULT ((0)) NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_EventStatus_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_EventStatus_DateModifiedUtc] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_EventState] PRIMARY KEY CLUSTERED ([EventStateId] ASC)
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueName]
    ON [Calendar].[EventState]([Name] ASC);


GO
CREATE NONCLUSTERED INDEX [TenantIdIndex]
    ON [Calendar].[EventState]([TenantId] ASC);

