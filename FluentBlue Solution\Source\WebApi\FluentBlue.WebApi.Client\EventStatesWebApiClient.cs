﻿using Azure;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Client
{
    public class EventStatesWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<EventStatesWebApiClient> logger;

        public EventStatesWebApiClient(HttpClient httpClient, ILogger<EventStatesWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.EventState>> GetAllEventStates(Guid tenantId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/EventStates/GetAll";

                //Εκτελούμε το GET request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DBOs.Calendar.EventState>>? response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DBOs.Calendar.EventState>>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        this.logger.LogError(response.ExceptionMessage, "Error in GetEventStates({@tenantId},{fiter})", tenantId);
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAllEventStates({@tenantId})", tenantId);
                throw;
            }
        }


        public async Task<EventState?> GetEventState(Guid eventStateId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/EventState/Get?EventStateId=" + eventStateId.ToString();

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<EventState> response = JsonConvert.DeserializeObject<ApiResponse<EventState>>(responseString)!;
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetEventState({@eventStateId})", eventStateId);
                throw;
            }
        }


        public async Task<EventState?> CreateOrUpdateEventState(EventState eventStateObj)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/EventState/CreateOrUpdate";

                string eventJson = System.Text.Json.JsonSerializer.Serialize(eventStateObj);  //Μετατρέπουμε το EventState object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(eventJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<EventState> response = JsonConvert.DeserializeObject<ApiResponse<EventState>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateEventState({@eventStateObj})", eventStateObj);
                throw;
            }
        }

        public async Task DeleteEventState(Guid eventStateId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/EventState/Delete?EventStateId=" + eventStateId;

                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString)!;
                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in DeleteEventState({@eventStateId})", eventStateId);
                throw;
            }
        }
    }
}
