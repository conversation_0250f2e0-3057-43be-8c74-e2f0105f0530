﻿using FluentBlue.UI.Main;
using System.Reflection;

namespace FluentBlue.UI.Web
{
    public class AppVersionHelper : IAppVersionHelper
    {
        public string CurrentVersion => GetVersion();

        private string GetVersion()
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            return assembly.GetCustomAttribute<System.Reflection.AssemblyFileVersionAttribute>()?.Version ?? "";
        }
    }
}
