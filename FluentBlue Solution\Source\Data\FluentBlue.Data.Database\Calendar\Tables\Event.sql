﻿CREATE TABLE [Calendar].[Event] (
    [EventId]              UNIQUEIDENTIFIER CONSTRAINT [DF_Event_EventId] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]             UNIQUEIDENTIFIER NOT NULL,
    [ContactId]            UNIQUEIDENTIFIER NULL,
    [EventCategoryId]      UNIQUEIDENTIFIER NULL,
    [EventStateId]         UNIQUEIDENTIFIER NULL,
    [Subject]              NVARCHAR (300)   CONSTRAINT [DF_Events_Subject] DEFAULT ('') NOT NULL,
    [Location]             NVARCHAR (300)   CONSTRAINT [DF_Events_Location] DEFAULT ('') NOT NULL,
    [StartTimeUtc]         DATETIME         NOT NULL,
    [StartTimeZone]        NVARCHAR (50)    COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
    [EndTimeUtc]           DATETIME         NOT NULL,
    [EndTimeZone]          NVARCHAR (50)    COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
    [Description]          NVARCHAR (MAX)   CONSTRAINT [DF_Events_Description] DEFAULT ('') NOT NULL,
    [AllDay]               BIT              CONSTRAINT [DF_Events_IsAllDay] DEFAULT ((0)) NOT NULL,
    [ReadOnly]             BIT              CONSTRAINT [DF_Events_ReadOnly_1] DEFAULT ((0)) NOT NULL,
    [Block]                BIT              CONSTRAINT [DF_Events_Block_1] DEFAULT ((0)) NOT NULL,
    [CustomRecurrence]     BIT              CONSTRAINT [DF_Event_CustomRecurrence] DEFAULT ((0)) NOT NULL,
    [CustomRecurrenceId]   UNIQUEIDENTIFIER NULL,
    [CustomRecurrenceRule] VARCHAR (MAX)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Event_CustomRecurrenceRule] DEFAULT ('') NOT NULL,
    [ReminderTimeUtc]      DATETIME         NULL,
    [ReminderDismissed]    BIT              NULL,
    [DateCreatedUtc]       DATETIME         CONSTRAINT [DF_Events_DateCreated] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc]      DATETIME         CONSTRAINT [DF_Events_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]           ROWVERSION       NULL,
    CONSTRAINT [PK_Event] PRIMARY KEY CLUSTERED ([EventId] ASC),
    CONSTRAINT [FK_Event_Contact] FOREIGN KEY ([ContactId]) REFERENCES [Contacts].[Contact] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_Event_EventCategory] FOREIGN KEY ([EventCategoryId]) REFERENCES [Calendar].[EventCategory] ([EventCategoryId]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_Event_EventState] FOREIGN KEY ([EventStateId]) REFERENCES [Calendar].[EventState] ([EventStateId]) ON DELETE SET NULL ON UPDATE CASCADE
);












GO
