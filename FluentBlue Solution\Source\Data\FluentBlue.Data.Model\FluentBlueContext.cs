using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public partial class FluentBlueDbContext : DbContext
    {
        private string connectionString = string.Empty;

        public FluentBlueDbContext(DbContextOptions<FluentBlueDbContext> options) : base(options)
        {
            ChangeTracker.LazyLoadingEnabled = false;
        }

        public FluentBlueDbContext(string connectionString) : base()
        {
            //base.OnConfiguring(new DbContextOptionsBuilder().UseSqlServer(connectionString));
            this.connectionString = connectionString;
            ChangeTracker.LazyLoadingEnabled = false;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                if (string.IsNullOrEmpty(connectionString))
                {
                    //optionsBuilder.UseSqlServer("Server=MAIN\\SQLEXPRESS;Database=FluentBlue;Trusted_Connection=True;TrustServerCertificate=True");
                    optionsBuilder.UseSqlServer("Name=FluentBlueConnectionString");
                }
                else
                {
                    optionsBuilder.UseSqlServer(connectionString);
                }
            }
        }

        public virtual DbSet<Tenant> Tenants { get; set; }
        public virtual DbSet<Role> Roles { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<Contact> Contacts { get; set; }
        public virtual DbSet<ContactCategory> ContactCategories { get; set; }
        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<EventCategory> EventCategories { get; set; }
        public virtual DbSet<EventState> EventStates { get; set; }
        public virtual DbSet<EventReminder> EventReminders { get; set; }
        public virtual DbSet<EventUser> EventUsers { get; set; }
        public virtual DbSet<UserSetting> UserSettings { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("Greek_CI_AI");
                        
            OnModelCreatingPartial(modelBuilder);
           
            modelBuilder.Entity<Role>().Navigation(r => r.Users).AutoInclude(false); // Prevent automatic eager loading
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
