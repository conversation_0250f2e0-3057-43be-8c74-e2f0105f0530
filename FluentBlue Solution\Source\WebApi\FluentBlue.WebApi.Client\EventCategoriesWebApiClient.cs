﻿using Azure;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Client
{
    public class EventCategoriesWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<EventCategoriesWebApiClient> logger;

        public EventCategoriesWebApiClient(HttpClient httpClient, ILogger<EventCategoriesWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.EventCategory>> GetAllEventCategories(Guid tenantId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/EventCategories/GetAll";

                //Εκτελούμε το GET request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DBOs.Calendar.EventCategory>>? response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DBOs.Calendar.EventCategory>>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        this.logger.LogError(response.ExceptionMessage, "Error in GetEventCategories({@tenantId},{fiter})", tenantId);
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAllEventCategories({@tenantId})", tenantId);
                throw;
            }
        }


        public async Task<EventCategory?> GetEventCategory(Guid eventCategoryId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/EventCategory/Get?EventCategoryId=" + eventCategoryId.ToString();

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<EventCategory> response = JsonConvert.DeserializeObject<ApiResponse<EventCategory>>(responseString)!;
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetEventCategory({@eventCategoryId})", eventCategoryId);
                throw;
            }
        }


        public async Task<EventCategory?> CreateOrUpdateEventCategory(EventCategory eventCategoryObj)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/EventCategory/CreateOrUpdate";

                string eventJson = System.Text.Json.JsonSerializer.Serialize(eventCategoryObj);  //Μετατρέπουμε το EventCategory object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(eventJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<EventCategory> response = JsonConvert.DeserializeObject<ApiResponse<EventCategory>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateEventCategory({@eventCategoryObj})", eventCategoryObj);
                throw;
            }
        }

        public async Task DeleteEventCategory(Guid eventCategoryId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/EventCategory/Delete?EventCategoryId=" + eventCategoryId;

                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString)!;
                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in DeleteEventCategory({@eventCategoryId})", eventCategoryId);
                throw;
            }
        }
    }
}
