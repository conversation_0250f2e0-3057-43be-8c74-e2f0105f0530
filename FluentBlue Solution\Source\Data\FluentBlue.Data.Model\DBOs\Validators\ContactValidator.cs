﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{


    public class ContactValidator : AbstractValidator<Contact>
    {
        public ContactValidator()
        {
            RuleFor(x => x.ContactId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Contact.ContactId));
            RuleFor(x => x.TenantId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Contact.TenantId));
            RuleFor(x => x.FirstName).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Contact.FirstName));
            RuleFor(x => x.FirstName).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength100);
            RuleFor(x => x.LastName).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Contact.LastName));
            RuleFor(x => x.LastName).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength100);
            RuleFor(x => x.Occupation).MaximumLength(200).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength200);
            RuleFor(x => x.TIN).MaximumLength(15).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength15);
            RuleFor(x => x.SSN).MaximumLength(15).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength15);
            RuleFor(x => x.Vat).LessThanOrEqualTo(100).WithMessage(Data.Model.Resources.GeneralValidationResource.InvalidData);
            RuleFor(x => x.Notes).MaximumLength(5000).WithMessage(Data.Model.Resources.GeneralValidationResource.InvalidData);
            RuleFor(x => x.DateCreatedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Contact.DateModifiedLocal));
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(Contact.DateModifiedLocal));

            RuleForEach(x => x.Emails).SetValidator(new ContactEmailValidator());
            RuleFor(x => x.Emails).Must(HaveUniqueEmail).WithMessage(Data.Model.Resources.Contacts.ContactEmailResource.EmailAlreadyExists);  //TODO: πρέπει να φτιαχτεί να βγάζει το μήνυμα σφάλματος πάνω στο πεδίο

            RuleForEach(x => x.Addresses).SetValidator(new ContactAddressValidator());
            RuleFor(x => x.Addresses).Must(HaveUniqueAddress).WithMessage(Data.Model.Resources.GeneralValidationResource.ValueAlreadyExists );  //TODO: πρέπει να φτιαχτεί να βγάζει το μήνυμα σφάλματος πάνω στο πεδίο

            RuleForEach(x => x.Phones).SetValidator(new ContactPhoneValidator());
            RuleFor(x => x.Phones).Must(HaveUniquePhone).WithMessage(Data.Model.Resources.GeneralValidationResource.ValueAlreadyExists);  //TODO: πρέπει να φτιαχτεί να βγάζει το μήνυμα σφάλματος πάνω στο πεδίο

        }

        protected override bool PreValidate(ValidationContext<Contact> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }

        private bool HaveUniqueEmail(List<ContactEmail> list)
        {
            return list.GroupBy(x => x.EmailAddress).All(g => g.Count() == 1); // Ensure each EmailAddress appears only once
        }

        private bool HaveUniquePhone(List<ContactPhone> list)
        {
            return list.GroupBy(x => x.PhoneNumber).All(g => g.Count() == 1); // Ensure each PhoneNumber appears only once
        }

        private bool HaveUniqueAddress(List<ContactAddress> list)
        {
            return list.GroupBy(x => x.Address).All(g => g.Count() == 1); // Ensure each Address appears only once
        }
    }
}
