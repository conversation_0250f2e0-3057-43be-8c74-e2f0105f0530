﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;

namespace FluentBlue.Application.Business
{
    public interface IContactsBusiness
    {
        Task<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>> GetContacts(ReadPagedContactsParameters parameters);
        Task<FluentBlue.Data.Model.DBOs.Contacts.Contact?> GetContact(Guid contactId);
        Task CreateOrUpdateContact(Contact contact);
        Task DeleteContact(Guid contactId);
        Task<List<ContactLI>> GetContactsLI(Guid tenantId);
        Task<int> GetContactsCount(Guid tenantId);
    }
}