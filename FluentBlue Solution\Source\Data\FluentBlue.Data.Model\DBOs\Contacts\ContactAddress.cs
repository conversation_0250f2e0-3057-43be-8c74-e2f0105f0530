﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using FluentBlue.Data.Model.DBOs.Calendar;

namespace FluentBlue.Data.Model.DBOs.Contacts
{
    [Table("ContactAddress", Schema = "Contacts")]
    public class ContactAddress : IObjectState, INotifyPropertyChanged
    {
        private Guid contactAddressId;
        private Guid contactId;
        private AddressType? type;
        private string address;
        private bool notifyPropertyChangedEnabled = false;

        public ContactAddress()
        {
            contactAddressId = Guid.CreateVersion7();
            type = AddressType.Home;
            address = string.Empty;
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            ObjectState = ObjectState.Unchanged;
            RowVersion = new byte[0];
        }

        internal static ContactAddress NewContactAddress()
        {
            ContactAddress contactAddress = new ContactAddress();
            contactAddress.ObjectState = ObjectState.Added;

            return contactAddress;
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactAddressResource), Name = "ContactAddressId")]
        public Guid ContactAddressId
        {
            get
            {
                return contactAddressId;
            }
            set
            {
                contactAddressId = value;
            }
        }

        public Guid ContactId
        {
            get
            {
                return contactId;
            }
            set
            {
                contactId = value;
            }
        }


        [ForeignKey("ContactId")]
        public Contact? Contact { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactAddressResource), Name = nameof(Model.Resources.Contacts.ContactAddressResource.Type))]
        public AddressType? Type
        {
            get
            {
                return type;
            }
            set
            {
                if (type != value)
                {
                    type = value;
                    NotifyPropertyChanged();
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactAddressResource), Name = nameof(Model.Resources.Contacts.ContactAddressResource.Address))]
        public string Address
        {
            get
            {
                return address;
            }
            set
            {
                if (address != value)
                {
                    address = value ?? "";
                    NotifyPropertyChanged();
                }
            }
        }


        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateCreated")]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateCreatedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        [NotMapped]
        internal bool NotifyPropertyChangedEnabled
        {
            get
            {
                return this.notifyPropertyChangedEnabled;
            }
            set
            {
                this.notifyPropertyChangedEnabled = value;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (notifyPropertyChangedEnabled == true)
            {
                //DateModifiedUtc = DateTime.UtcNow;
                if (ObjectState == ObjectState.Unchanged)
                {
                    ObjectState = ObjectState.Modified;  //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
                }

                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
            }
        }
    }
}
