﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Security.Claims;
using System.Text;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="role/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class RolesController : ControllerBase
    {
        private ILogger<RolesController> logger;
        private IRolesBusiness rolesBusiness;
        private IConfiguration configuration;
        private IWebApiCallerInfo webApiCallerInfo;
        private ISettingsBusiness settingsBusiness;
        private ILogger<SettingsController> settingsLogger;
        private IMapper mapper;

        public RolesController(IRolesBusiness rolesBusiness, ILogger<RolesController> logger, IConfiguration configuration, IWebApiCallerInfo webApiCallerInfo, ISettingsBusiness settingsBusiness, ILogger<SettingsController> settingsLogger, IMapper mapper)
        {
            try
            {
                this.rolesBusiness = rolesBusiness;
                this.settingsBusiness = settingsBusiness;
                this.logger = logger;  //TODO: να δω αν θα χρησιμοποιείσω το ILogger γενικά στην εφαρμογή.
                this.configuration = configuration;
                this.webApiCallerInfo = webApiCallerInfo;
                this.settingsLogger = settingsLogger;
                this.mapper = mapper;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        /// <summary>
        /// Retrieves contacts in paged mode.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Roles/Get")]
        public async Task<ApiResponse<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>>> GetRoles([FromBody] RequestDataParameters parameters)
        {
            try
            {
                //Map data
                ReadPagedDataParameters readPagedDataParameters = this.mapper.Map<ReadPagedDataParameters>(parameters);

                //Validation
                if (readPagedDataParameters.TenantId != this.webApiCallerInfo.TenantId)
                {
                    return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = "TenantId missmacth." };
                }

                //Query
                PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>> data = await this.rolesBusiness.GetRoles(readPagedDataParameters);

                //Response
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, parameters);
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        /// <summary>
        /// Returns all the Roles of Tenant. This list should be used for displaying in lists, dropdowns.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Roles/GetList")]
        public async Task<ApiResponse<List<FluentBlue.Data.Model.DTOs.RoleLI>>> GetRolesList()
        {
            try
            {
                //Validation

                //Query
                List<FluentBlue.Data.Model.DTOs.RoleLI> data = await this.rolesBusiness.GetRolesLI(this.webApiCallerInfo.TenantId!.Value);

                //Response
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.RoleLI>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.RoleLI>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, this.webApiCallerInfo.TenantId.Value);
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.RoleLI>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("Role/Get")]
        public async Task<ApiResponse<Role?>> GetRole([FromQuery] string roleId)
        {
            try
            {
                //Query
                Role? role = await this.rolesBusiness.GetRole(Guid.Parse(roleId));

                //Response
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = role };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "RoleId=" + roleId);
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpPost]
        [Route("Role/CreateOrUpdate")]
        public async Task<ApiResponse<Role>> CreateOrUpdateRole([FromBody] Role role)
        {
            try
            {
                #region Validation
                if (role == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Ελέγχει αν υπάρχει ήδη στη βάση δεδομένων
                Role? existingRole = await this.rolesBusiness.CheckRoleExists(this.webApiCallerInfo.TenantId!.Value, role.RoleId, role.Name);
                if (existingRole != null)
                {
                    return new ApiResponse<Role>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = FluentBlue.WebApi.Service.Resources.RolesResource.RoleAlreadyExists };
                }
                #endregion

                //Query
                await this.rolesBusiness.CreateOrUpdateRole(role);

                //Response
                Role? conctact = await this.rolesBusiness.GetRole(role.RoleId);
                return new ApiResponse<Role>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = role };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Role>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, role);
                return new ApiResponse<Role>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("Role/Delete")]
        public ApiResponse DeleteRole([FromQuery] Guid roleId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από Role με το ίδιο TenantId.

                this.rolesBusiness.DeleteRole(roleId);

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "RoleId=" + roleId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("Role/CheckExists")]
        public async Task<ApiResponse<Role?>> CheckRoleExists([FromQuery] string roleId, string name)
        {
            try
            {
                //Query
                Role? result = await this.rolesBusiness.CheckRoleExists(this.webApiCallerInfo.TenantId!.Value, Guid.Parse(roleId), name);

                //TODO: να δω αν θα προστεθεί έλεγχος ασφαλείας ότι το Role ανοίκει στο Tenant που το ζητάει.

                //Response
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = result };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "RoleId=" + roleId);
                return new ApiResponse<Role?>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}
