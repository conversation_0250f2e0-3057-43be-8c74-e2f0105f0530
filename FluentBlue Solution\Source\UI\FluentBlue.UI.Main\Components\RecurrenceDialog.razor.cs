﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Syncfusion.Blazor.Schedule;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Shared.Utilities;
using Microsoft.JSInterop;
using Microsoft.Extensions.Caching.Hybrid;

namespace FluentBlue.UI.Main.Components
{
    partial class RecurrenceDialog
    {
        [Parameter]
        public RecurrenceDialogInput? Content { get; set; }
        private string selectedRecurrenceRule = "FREQ=DAILY;INTERVAL=2;COUNT=5;";
        private List<DateTime> recurrenceDates = new List<DateTime>();

        [CascadingParameter] public FluentDialog CurrentDialog { get; set; } = default!;

        private List<EndType> endTypes = new List<EndType>() { EndType.Until, EndType.Count };
        private UserSetting? userSetting = null;
        private RenderFragment infoText = default!;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    // Reads the UserSettings from cache or database
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    this.GenerateRecurrenceDates(this.selectedRecurrenceRule);
                    this.StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task ValueChanged(string recurrenceRule)
        {
            try
            {
                GenerateRecurrenceDates(recurrenceRule);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void GenerateRecurrenceDates(string recurrenceRule)
        {
            if (recurrenceRule == null)
            {
                this.selectedRecurrenceRule = "";
            }
            else
            {
                if (this.userSetting != null)
                {
                    this.selectedRecurrenceRule = recurrenceRule;

                    int firstDayOfWeekInt = (int)((DayOfWeek)this.userSetting!.FirstDayOfWeek);

                    this.recurrenceDates = RecurrenceHelper.GetRecurrenceDateTimeCollection(this.selectedRecurrenceRule, "", firstDayOfWeekInt, this.Content!.Event!.StartTimeLocal!.Value, this.Content!.Event!.EndTimeLocal!.Value, null, null, 30).ToList();

                    if (this.recurrenceDates.Count > 0)
                    {
                        //for (int i = 0; i < this.recurrenceDates.Count; i++)
                        //{
                        //    this.infoText += recurrenceDates[i].ToLongDateString() + " " + recurrenceDates[i].ToShortTimeString() + Environment.NewLine;
                        //}
                        this.infoText = builder =>
                        {
                            if (recurrenceDates.Count > 0)
                            {
                                builder.OpenElement(0, "ul");

                                // Extract <li> elements and render them individually
                                for (int i = 0; i < this.recurrenceDates.Count; i++)
                                {
                                    builder.OpenElement(1, "li");
                                    builder.AddContent(2, new MarkupString(recurrenceDates[i].ToLongDateString() + " " + recurrenceDates[i].ToShortTimeString()));
                                    builder.CloseElement();
                                }

                                builder.CloseElement();
                            }
                        };
                    }
                    else
                    {
                        this.infoText = builder =>
                        {
                            builder.AddMarkupContent(0, "");
                        };
                        //this.infoText = "";
                    }
                }
            }
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                int firstDayOfWeekInt = (int)((DayOfWeek)this.userSetting!.FirstDayOfWeek);

                #region  Validates the Recurrence
                //If recurrences count is below zero, then there is fault in recurrence rule.
                if (this.recurrenceDates.Count < 0)
                {
                    dialogService.ShowInfo(Resources.EventDialogResource.InvalidRecurrenceRule);
                    return;
                }
                else if (this.recurrenceDates.Count > 20)  //If we have more than 20 recurrences.
                {
                    dialogService.ShowInfo(Resources.EventDialogResource.RecurrenceCountExceededRule);
                    return;
                }

                #region Checks if there are overlapping in recurrent events.
                TimeSpan duration = this.Content!.Event.EndTimeUtc - this.Content!.Event.StartTimeUtc;
                var recurrenceDateTimes = this.recurrenceDates.Select(date => (startDateTime: date, endDateTime: date.Add(duration))).ToList();   // Converts recurrenceDates to List<(startDateTime, endDateTime)> by using duration

                if (DateTimeUtilities.HasOverlappingDates(recurrenceDateTimes))
                {
                    dialogService.ShowInfo(Resources.EventDialogResource.RecurrencesOverlapping);
                    return;
                }
                #endregion
                #endregion

                RecurrenceDialogOutput result = new RecurrenceDialogOutput
                {
                    RecurrenceRule = this.selectedRecurrenceRule,
                    RecurrenceDates = this.recurrenceDates
                };

                await CurrentDialog.CloseAsync(result);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CancelBtnOnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

    }
}
