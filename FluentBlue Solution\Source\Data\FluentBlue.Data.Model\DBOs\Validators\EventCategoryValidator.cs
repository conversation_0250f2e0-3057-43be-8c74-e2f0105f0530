﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{
    public class EventCategoryValidator : AbstractValidator<EventCategory>
    {
        public EventCategoryValidator()
        {
            RuleFor(x => x.EventCategoryId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.EventCategoryId));
            RuleFor(x => x.TenantId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.TenantId));
            RuleFor(x => x.Name).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.Name));
            RuleFor(x => x.Name).MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            //To BackColor δεν το υποσηρίζουμε ακόμα στο EventStatus αφού έχουμε αυτο του EventCategory.
            //RuleFor(x => x.BackColor).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.BackColor));
            //RuleFor(x => x.BackColor).NotEqual("#000000").WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameIsInvalid, nameof(EventCategory.BackColor));
            //RuleFor(x => x.BackColor).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength10);
          

            RuleFor(x => x.DateCreatedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.DateModifiedLocal));
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.DateModifiedLocal));
            RuleFor(x => x.DateCreatedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.DateModifiedUtc));
            RuleFor(x => x.DateModifiedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventCategory.DateModifiedUtc));
        }

        protected override bool PreValidate(ValidationContext<EventCategory> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }

    }
}
