﻿using FluentBlue.Application.Business;
using FluentBlue.Shared.Authorization;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class AuthenticationController : ControllerBase
    {
        private ILogger<AuthenticationController> logger;
        private IUsersBusiness usersBusiness;
        private IConfiguration configuration;

        public AuthenticationController(IUsersBusiness usersBusiness, ILogger<AuthenticationController> logger, IConfiguration configuration)
        {
            this.usersBusiness = usersBusiness;
            this.logger = logger;  //TODO: να δω αν θα χρησιμοποιείσω το ILogger γενικά στην εφαρμογή.
            this.configuration = configuration;
        }

        [HttpGet("Login")]
        public async Task<ApiResponse<LoginResponse>> Login(string username, string password)
        {
            try
            {
                //Query
                
                Data.Model.DBOs.Tenants.User? user = await this.usersBusiness.GetUser(username, password);

                //Αν ο User υπάρχει
                if (user != null)
                {
                    UserToken token = BuildToken(user.UserId.ToString(), username, user.FullName, user.Role.Name.ToString(), user.TenantId.ToString());

                    //TODO: Λείπει κώδικας αν η συνδρομή του Tenant έχει λήξει

                    //Response
                    LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.Ok, Token = token };
                    return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
                }
                else  //Αν ο User δεν υπάρχει
                {
                    //Response
                    LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.InvalidUsernamePassword, Token = new UserToken() };
                    return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
                }
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(ex);
                return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet("RenewToken")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public ActionResult<UserToken> Renew()
        {
            try
            {
                UserToken userToken = BuildToken("9AD0538F-EAEA-4C4B-8FE7-B69CEDDCABD3", HttpContext.User.Identity.Name, "John", "Admin", "FD335871-9253-4364-9BEB-5D1238F1C4E5");

                return userToken;
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(ex);
                throw;
            }
        }

        private UserToken BuildToken(string userId, string username, string fullName, string roleName, string tenantId)
        {
            var claims = new List<Claim>()
            {
                new Claim("UserId", userId),
                new Claim("Username", username),
                new Claim(ClaimTypes.Name, fullName),
                new Claim(ClaimTypes.Role, roleName),
                new Claim("TenantId", tenantId)
            };

            //var identityUser = await _userManager.FindByEmailAsync(userinfo.Email);
            //var claimsDB = await _userManager.GetClaimsAsync(identityUser);
            //claims.AddRange(claimsDB);

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["jwt:key"]!));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var expiration = DateTime.UtcNow.AddYears(1);

            JwtSecurityToken token = new JwtSecurityToken(
               issuer: null,
               audience: null,
               claims: claims,
               expires: expiration,
               signingCredentials: creds);

            return new UserToken()
            {
                Token = new JwtSecurityTokenHandler().WriteToken(token),
                Expiration = expiration
            };
        }

    }
}
