﻿@using Microsoft.JSInterop
@using AKSoftware.Blazor.Utilities;

@implements IAsyncDisposable

@inject IJSRuntime JSRuntime

@code {
    private DotNetObjectReference<ScreenSizeTracker>? dotNetRef;
    private int screenWidth;
    public static string CurrentBreakpoint = "Unknown";

    // Event callback to notify the parent when the breakpoint changes
    [Parameter]
    public EventCallback<string> OnBreakpointChanged { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            dotNetRef = DotNetObjectReference.Create(this);
            await JSRuntime.InvokeVoidAsync("screenSizeHelper.registerResizeCallback", dotNetRef);
            screenWidth = await JSRuntime.InvokeAsync<int>("eval", "window.innerWidth"); // Initialize screen width
            UpdateBreakpoint(); // Determine initial breakpoint
        }
    }

    [JSInvokable]
    public async Task UpdateScreenWidth(int width)
    {
        if (screenWidth == width) return; // Skip if width hasn't changed
        
        screenWidth = width;
        var previousBreakpoint = CurrentBreakpoint;
        UpdateBreakpoint();

        // Only trigger callback and re-render if breakpoint changed
        if (previousBreakpoint != CurrentBreakpoint)
        {
            await OnBreakpointChanged.InvokeAsync(CurrentBreakpoint);
            MessagingCenter.Send(this, Keywords.BreakpointChanged, CurrentBreakpoint);

            StateHasChanged();
        }
    }

    public async Task<string> GetCurrentBreakpoint()
    {
        int width = await JSRuntime.InvokeAsync<int>("eval", "window.innerWidth"); // Initialize screen width

        if (width <= 600)
        {
            return "xs";
        }
        else if (width >= 601 && width <= 960)
        {
            return "sm";
        }
        else if (width >= 961 && width <= 1280)
        {
            return "md";
        }
        else if (width >= 1281 && width <= 1920)
        {
            return "lg";
        }
        else if (width >= 1921 && width <= 2560)
        {
            return "xl";
        }
        else if (width >= 2561)
        {
            return "xxl";
        }
        else { return "xxl"; }
    }

    private void UpdateBreakpoint()
    {
        if (screenWidth <= 600)
        {
            CurrentBreakpoint = "xs";
        }
        else if (screenWidth >= 601 && screenWidth <= 960)
        {
            CurrentBreakpoint = "sm";
        }
        else if (screenWidth >= 961 && screenWidth <= 1280)
        {
            CurrentBreakpoint = "md";
        }
        else if (screenWidth >= 1281 && screenWidth <= 1920)
        {
            CurrentBreakpoint = "lg";
        }
        else if (screenWidth >= 1921 && screenWidth <= 2560)
        {
            CurrentBreakpoint = "xl";
        }
        else if (screenWidth >= 2561)
        {
            CurrentBreakpoint = "xxl";
        }
        else { CurrentBreakpoint = "xxl"; }
    }

    public async ValueTask DisposeAsync()
    {
        if (dotNetRef != null)
        {
            await JSRuntime.InvokeVoidAsync("screenSizeHelper.unregisterResizeCallback");
            dotNetRef.Dispose();
        }
    }

    protected override bool ShouldRender()
    {
        return true; // Only render when breakpoint changes (handled in UpdateScreenWidth)
    }
}
