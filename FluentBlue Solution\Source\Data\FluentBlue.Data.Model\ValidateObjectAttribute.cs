﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public class ValidateObjectAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success;
            }

            var results = new List<ValidationResult>();
            var context = new ValidationContext(value, null, null);
            
            //Αν είναι τύπου List<T>
            if (value.GetType().IsGenericType && (value.GetType().GetGenericTypeDefinition() == typeof(List<>)))
            {
                var list = value as IEnumerable;
                
                bool isValid = true;

                foreach (var item in list!)
                {
                    var listValidationContext = new ValidationContext(item);
                    var isItemValid = Validator.TryValidateObject(item, listValidationContext, results, true);
                    isValid &= isItemValid;
                }
            }
            else  //Αλλιώς για τα υπόλοιπα objects.
            {
                Validator.TryValidateObject(value, context, results, true);
            }

            if (results.Count != 0)
            {
                var compositeResults = new CompositeValidationResult(String.Format("Validation for {0} failed!", validationContext.DisplayName));  //TODO: Εδώ πρεπει να φτιαχτεί ώστε τα validation μηνύματα που επιστρέφει να είνα κατανοητά.
                results.ForEach(compositeResults.AddResult);

                return compositeResults;
            }

            return ValidationResult.Success;
        }
    }

    public class CompositeValidationResult : ValidationResult
    {
        private readonly List<ValidationResult> _results = new List<ValidationResult>();

        public IEnumerable<ValidationResult> Results
        {
            get
            {
                return _results;
            }
        }

        public CompositeValidationResult(string errorMessage) : base(errorMessage) { }
        public CompositeValidationResult(string errorMessage, IEnumerable<string> memberNames) : base(errorMessage, memberNames) { }
        protected CompositeValidationResult(ValidationResult validationResult) : base(validationResult) { }

        public void AddResult(ValidationResult validationResult)
        {
            _results.Add(validationResult);
        }
    }
}
