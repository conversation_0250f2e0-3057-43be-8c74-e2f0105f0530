<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ZeroMinReminder" xml:space="preserve">
    <value>Την ώρα του συμβάντος</value>
  </data>
  <data name="FifteenMinReminder" xml:space="preserve">
    <value>15 λεπτά πριν</value>
  </data>
  <data name="FiveMinReminder" xml:space="preserve">
    <value>5 λεπτά πριν</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Ολοήμερο</value>
  </data>
  <data name="CancelBtn.Text" xml:space="preserve">
    <value>Κλείσιμο</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Κατηγορία</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Επαφή</value>
  </data>
  <data name="ConvertToRecurrent" xml:space="preserve">
    <value>Μετατροπή σε περιοδικό</value>
  </data>
  <data name="DeleteBtn.Text" xml:space="preserve">
    <value>Διαγραφή</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Περιγραφή</value>
  </data>
  <data name="Dismiss" xml:space="preserve">
    <value>Απόρριψη</value>
  </data>
  <data name="EditingAllRecurrentEventsMessage" xml:space="preserve">
    <value>Επεξεργάζεστε όλα τα συμβάντα της επανάληψης.</value>
  </data>
  <data name="EditingCurrentAndFollowingRecurrentEventsMessage" xml:space="preserve">
    <value>Επεξεργάζεστε αυτό και όλα τα επόμενα συμβάντα της επανάληψης.</value>
  </data>
  <data name="EditingSingleRecurrentEventMessage" xml:space="preserve">
    <value>Επεξεργάζεστε ένα συμβάν από την επανάληψη.</value>
  </data>
  <data name="EditRecurrence" xml:space="preserve">
    <value>Επεξεργασία περιοδικότητας</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Λήξη</value>
  </data>
  <data name="InvalidRecurrenceRule" xml:space="preserve">
    <value>Λανθασμένες ρυθμίσεις στην επανάληψη. Παρακαλώ προσπαθήστε ξανά.</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Τοποθεσία</value>
  </data>
  <data name="MaximumUsersSelected" xml:space="preserve">
    <value>Επιλέχθηκε ο μέγιστος αριθμός χρηστών.</value>
  </data>
  <data name="NewContactBtn.Text" xml:space="preserve">
    <value>Νέα Επαφή</value>
  </data>
  <data name="NoUsersFound" xml:space="preserve">
    <value>Δεν βρέθηκαν χρήστες</value>
  </data>
  <data name="Recurrence" xml:space="preserve">
    <value>Επανάληψη</value>
  </data>
  <data name="RecurrenceCountExceededRule" xml:space="preserve">
    <value>Οι επαναλήψεις δεν μπορεί να είναι περισσότερες από 20.</value>
  </data>
  <data name="RecurrencesOverlapping" xml:space="preserve">
    <value>Η επιλεγμένη επανάληψη προκαλει επικαλυπτόμενα συμβαντα. Επιλέξτε κάποια άλλη επανάληψη.</value>
  </data>
  <data name="Reminder" xml:space="preserve">
    <value>Υπενθύμιση</value>
  </data>
  <data name="SaveBtn.Text" xml:space="preserve">
    <value>Αποθήκευση</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Έναρξη</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Κατάσταση</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Θέμα</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Συμβάν</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Χρήστης</value>
  </data>
  <data name="NoReminder" xml:space="preserve">
    <value>Χωρίς υπενθύμιση</value>
  </data>
  <data name="ThirtyMinReminder" xml:space="preserve">
    <value>30 λεπτά πριν</value>
  </data>
  <data name="OneHourReminder" xml:space="preserve">
    <value>1 ώρα πριν</value>
  </data>
  <data name="TwoHoursReminder" xml:space="preserve">
    <value>2 ώρες πριν</value>
  </data>
</root>