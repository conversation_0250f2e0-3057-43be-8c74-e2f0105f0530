﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Utilities
{
    public class Colors
    {
        public static string GetColorFromInitials(string initials)
        {
            if (string.IsNullOrEmpty(initials))
                return "#808080"; // Default gray for empty input

            int hash = initials.GetHashCode(); // Simple hash function
            int r = (hash & 0xFF0000) >> 16; // Extract red
            int g = (hash & 0x00FF00) >> 8;  // Extract green
            int b = (hash & 0x0000FF);       // Extract blue

            // Adjust brightness to ensure contrast with white text
            r = 50 + (r % 156); // Avoid too dark/light (range: 50-205)
            g = 50 + (g % 156);
            b = 50 + (b % 156);

            return $"#{r:X2}{g:X2}{b:X2}";
        }
    }
}
