﻿using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Utilities
{
    public static class ValidationErrorsToBulletsConverter
    {
        public static RenderFragment ConvertValidationErrorsToBullets(string preText, string[] validationErrors, string postText)
        {
            string errors = preText + "<ul>" + string.Join("", validationErrors.Select(er => $"<li>{er}</li>")) + "</ul>" + postText;
            RenderFragment errorRF = new RenderFragment(builder =>
            {
                builder.AddMarkupContent(0, errors);
            });

            return errorRF;
        }
    }
}
