﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DBOs.Tenants
{
    [Table("Tenant", Schema = "Tenants")]
    public class Tenant
    {
        public Tenant()
        {
            this.Companies = new List<Company>();
        }


        [Key]
        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.TenantDisplayResource), Name = "TenantId")]
        public Guid TenantId { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.TenantDisplayResource), Name = "Name")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource), AllowEmptyStrings = false)]
        [DefaultValue("")]
        public string Name { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.TenantDisplayResource), Name = "Email")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource), AllowEmptyStrings = false)]
        [DefaultValue("")]
        public string Email { get; set; } = string.Empty;

        public List<Company> Companies { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; } = new byte[0];


        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;
    }
}
