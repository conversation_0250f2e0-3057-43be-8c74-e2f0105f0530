﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;

namespace FluentBlue.Data.Model.DBOs.Validators
{
    public class ContactCategoryValidator : AbstractValidator<ContactCategory>
    {
        public ContactCategoryValidator()
        {
            RuleFor(x => x.ContactCategoryId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.ContactCategoryId));
            RuleFor(x => x.TenantId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.TenantId));
            RuleFor(x => x.Name).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.Name));
            RuleFor(x => x.Name).MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            RuleFor(x => x.Color).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.Color));
            RuleFor(x => x.Color).NotEqual("#000000").WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameIsInvalid, nameof(ContactCategory.Color));
            RuleFor(x => x.Color).MaximumLength(10).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength10);
          

            RuleFor(x => x.DateCreatedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.DateModifiedLocal));
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.DateModifiedLocal));
            RuleFor(x => x.DateCreatedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.DateModifiedUtc));
            RuleFor(x => x.DateModifiedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactCategory.DateModifiedUtc));
        }

        protected override bool PreValidate(ValidationContext<ContactCategory> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }

    }
}
