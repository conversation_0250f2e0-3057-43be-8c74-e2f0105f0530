﻿using FluentBlue.Shared.Utilities;

namespace FluentBlue.Ui.Devices
{
    public class FormFactor : IFormFactor
    {
        public string GetDeviceIdiom()
        {
            return DeviceInfo.Idiom.ToString();
        }
        public string GetPlatform()
        {
            return DeviceInfo.Platform.ToString();
        }

        public string GetDeviceType()
        {
            return DeviceInfo.DeviceType.ToString();
        }
    }
}