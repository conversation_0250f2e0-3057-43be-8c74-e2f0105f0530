﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Utilities
{
    public class TimeZone
    {
        public int LatitudeSeconds { get; set; }
        public int LongitudeSeconds { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string CountryName { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string ZoneId { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;

        public TimeZone(int latitudeSeconds, int longitudeSeconds, double latitude, double longitude, string countryName, string countryCode, string zoneId, string comment)
        {
            LatitudeSeconds = latitudeSeconds;
            LongitudeSeconds = longitudeSeconds;
            Latitude = latitude;
            Longitude = longitude;
            CountryName = countryName;
            CountryCode = countryCode;
            ZoneId = zoneId;
            Comment = comment;
        }

        public static List<TimeZone> AllTimeZones
        {
            get
            {
                //Explain: Data below has been found from TzdbZoneLocation object in NodaTime library, by executing the following code:
                //@TzdbDateTimeZoneSource.Default.ZoneLocations!.Select(x=> (TzdbZoneLocation)x)

                List<TimeZone> timeZones = new List<TimeZone>();

                // Add rows
                timeZones.Add(new TimeZone(153000, 5460, 42.5, 1.51666666666666e+16, "Andorra", "AD", "Europe/Andorra", ""));
                timeZones.Add(new TimeZone(91080, 199080, 25.3, 55.3, "United Arab Emirates", "AE", "Asia/Dubai", ""));
                timeZones.Add(new TimeZone(124260, 249120, 3.45166666666666e+16, 69.2, "Afghanistan", "AF", "Asia/Kabul", ""));
                timeZones.Add(new TimeZone(61380, -222480, 17.05, -61.8, "Antigua & Barbuda", "AG", "America/Antigua", ""));
                timeZones.Add(new TimeZone(65520, -227040, 18.2, -6306666666666660.0, "Anguilla", "AI", "America/Anguilla", ""));
                timeZones.Add(new TimeZone(148800, 71400, 4.13333333333333e+16, 1.98333333333333e+16, "Albania", "AL", "Europe/Tirane", ""));
                timeZones.Add(new TimeZone(144660, 160200, 4018333333333330.0, 44.5, "Armenia", "AM", "Asia/Yerevan", ""));
                timeZones.Add(new TimeZone(-31680, 47640, -8.8, 1.32333333333333e+16, "Angola", "AO", "Africa/Luanda", ""));
                timeZones.Add(new TimeZone(-280200, 599760, -7783333333333330.0, 166.6, "Antarctica", "AQ", "Antarctica/McMurdo", "New Zealand time - McMurdo, South Pole"));
                timeZones.Add(new TimeZone(-238620, 397860, -6628333333333330.0, 1.10516666666666e+16, "Antarctica", "AQ", "Antarctica/Casey", "Casey"));
                timeZones.Add(new TimeZone(-246900, 280680, -6858333333333330.0, 7796666666666660.0, "Antarctica", "AQ", "Antarctica/Davis", "Davis"));
                timeZones.Add(new TimeZone(-240000, 504060, -6666666666666660.0, 1.40016666666666e+16, "Antarctica", "AQ", "Antarctica/DumontDUrville", "Dumont-d'Urville"));
                timeZones.Add(new TimeZone(-243360, 226380, -67.6, 6288333333333330.0, "Antarctica", "AQ", "Antarctica/Mawson", "Mawson"));
                timeZones.Add(new TimeZone(-233280, -230760, -64.8, -64.1, "Antarctica", "AQ", "Antarctica/Palmer", "Palmer"));
                timeZones.Add(new TimeZone(-243240, -245280, -6756666666666660.0, -6813333333333330.0, "Antarctica", "AQ", "Antarctica/Rothera", "Rothera"));
                timeZones.Add(new TimeZone(-248422, 142524, -6900611111111110.0, 39.59, "Antarctica", "AQ", "Antarctica/Syowa", "Syowa"));
                timeZones.Add(new TimeZone(-259241, 9126, -7201138888888880.0, 2535.0, "Antarctica", "AQ", "Antarctica/Troll", "Troll"));
                timeZones.Add(new TimeZone(-282240, 384840, -78.4, 106.9, "Antarctica", "AQ", "Antarctica/Vostok", "Vostok"));
                timeZones.Add(new TimeZone(-124560, -210420, -34.6, -58.45, "Argentina", "AR", "America/Argentina/Buenos_Aires", "Buenos Aires (BA, CF)"));
                timeZones.Add(new TimeZone(-113040, -231060, -31.4, -6418333333333330.0, "Argentina", "AR", "America/Argentina/Cordoba", "Argentina (most areas: CB, CC, CN, ER, FM, MN, SE, SF)"));
                timeZones.Add(new TimeZone(-89220, -235500, -2.47833333333333e+16, -6541666666666660.0, "Argentina", "AR", "America/Argentina/Salta", "Salta (SA, LP, NQ, RN)"));
                timeZones.Add(new TimeZone(-87060, -235080, -2.41833333333333e+16, -65.3, "Argentina", "AR", "America/Argentina/Jujuy", "Jujuy (JY)"));
                timeZones.Add(new TimeZone(-96540, -234780, -2.68166666666666e+16, -6521666666666660.0, "Argentina", "AR", "America/Argentina/Tucuman", "Tucuman (TM)"));
                timeZones.Add(new TimeZone(-102480, -236820, -2.84666666666666e+16, -6578333333333330.0, "Argentina", "AR", "America/Argentina/Catamarca", "Catamarca (CT), Chubut (CH)"));
                timeZones.Add(new TimeZone(-105960, -240660, -2.94333333333333e+16, -66.85, "Argentina", "AR", "America/Argentina/La_Rioja", "La Rioja (LR)"));
                timeZones.Add(new TimeZone(-113520, -246660, -3.15333333333333e+16, -6851666666666660.0, "Argentina", "AR", "America/Argentina/San_Juan", "San Juan (SJ)"));
                timeZones.Add(new TimeZone(-118380, -247740, -3288333333333330.0, -6881666666666660.0, "Argentina", "AR", "America/Argentina/Mendoza", "Mendoza (MZ)"));
                timeZones.Add(new TimeZone(-119940, -238860, -3331666666666660.0, -66.35, "Argentina", "AR", "America/Argentina/San_Luis", "San Luis (SL)"));
                timeZones.Add(new TimeZone(-185880, -249180, -5163333333333330.0, -6921666666666660.0, "Argentina", "AR", "America/Argentina/Rio_Gallegos", "Santa Cruz (SC)"));
                timeZones.Add(new TimeZone(-197280, -245880, -54.8, -68.3, "Argentina", "AR", "America/Argentina/Ushuaia", "Tierra del Fuego (TF)"));
                timeZones.Add(new TimeZone(-51360, -614520, -1.42666666666666e+16, -170.7, "Samoa (American)", "AS", "Pacific/Pago_Pago", ""));
                timeZones.Add(new TimeZone(173580, 58800, 4821666666666660.0, 1.63333333333333e+16, "Austria", "AT", "Europe/Vienna", ""));
                timeZones.Add(new TimeZone(-113580, 572700, -31.55, 1.59083333333333e+16, "Australia", "AU", "Australia/Lord_Howe", "Lord Howe Island"));
                timeZones.Add(new TimeZone(-196200, 572220, -54.5, 158.95, "Australia", "AU", "Antarctica/Macquarie", "Macquarie Island"));
                timeZones.Add(new TimeZone(-154380, 530340, -4288333333333330.0, 1.47316666666666e+16, "Australia", "AU", "Australia/Hobart", "Tasmania"));
                timeZones.Add(new TimeZone(-136140, 521880, -3781666666666660.0, 1.44966666666666e+16, "Australia", "AU", "Australia/Melbourne", "Victoria"));
                timeZones.Add(new TimeZone(-121920, 544380, -3386666666666660.0, 1.51216666666666e+16, "Australia", "AU", "Australia/Sydney", "New South Wales (most areas)"));
                timeZones.Add(new TimeZone(-115020, 509220, -31.95, 141.45, "Australia", "AU", "Australia/Broken_Hill", "New South Wales (Yancowinna)"));
                timeZones.Add(new TimeZone(-98880, 550920, -2.74666666666666e+16, 1.53033333333333e+16, "Australia", "AU", "Australia/Brisbane", "Queensland (most areas)"));
                timeZones.Add(new TimeZone(-72960, 536400, -2.02666666666666e+16, 149.0, "Australia", "AU", "Australia/Lindeman", "Queensland (Whitsunday Islands)"));
                timeZones.Add(new TimeZone(-125700, 498900, -3.49166666666666e+16, 1.38583333333333e+16, "Australia", "AU", "Australia/Adelaide", "South Australia"));
                timeZones.Add(new TimeZone(-44880, 471000, -1.24666666666666e+16, 1.30833333333333e+16, "Australia", "AU", "Australia/Darwin", "Northern Territory"));
                timeZones.Add(new TimeZone(-115020, 417060, -31.95, 115.85, "Australia", "AU", "Australia/Perth", "Western Australia (most areas)"));
                timeZones.Add(new TimeZone(-114180, 463920, -3.17166666666666e+16, 1.28866666666666e+16, "Australia", "AU", "Australia/Eucla", "Western Australia (Eucla)"));
                timeZones.Add(new TimeZone(45000, -251880, 12.5, -6996666666666660.0, "Aruba", "AW", "America/Aruba", ""));
                timeZones.Add(new TimeZone(216360, 71820, 60.1, 19.95, "Åland Islands", "AX", "Europe/Mariehamn", ""));
                timeZones.Add(new TimeZone(145380, 179460, 4038333333333330.0, 49.85, "Azerbaijan", "AZ", "Asia/Baku", ""));
                timeZones.Add(new TimeZone(157920, 66300, 4386666666666660.0, 1.84166666666666e+16, "Bosnia & Herzegovina", "BA", "Europe/Sarajevo", ""));
                timeZones.Add(new TimeZone(47160, -214620, 13.1, -5961666666666660.0, "Barbados", "BB", "America/Barbados", ""));
                timeZones.Add(new TimeZone(85380, 325500, 2.37166666666666e+16, 9041666666666660.0, "Bangladesh", "BD", "Asia/Dhaka", ""));
                timeZones.Add(new TimeZone(183000, 15600, 5.08333333333333e+16, 4333333333333330.0, "Belgium", "BE", "Europe/Brussels", ""));
                timeZones.Add(new TimeZone(44520, -5460, 1.23666666666666e+16, -1.51666666666666e+16, "Burkina Faso", "BF", "Africa/Ouagadougou", ""));
                timeZones.Add(new TimeZone(153660, 83940, 4268333333333330.0, 2.33166666666666e+16, "Bulgaria", "BG", "Europe/Sofia", ""));
                timeZones.Add(new TimeZone(94980, 182100, 2.63833333333333e+16, 5.05833333333333e+16, "Bahrain", "BH", "Asia/Bahrain", ""));
                timeZones.Add(new TimeZone(-12180, 105720, -3.38333333333333e+16, 2.93666666666666e+16, "Burundi", "BI", "Africa/Bujumbura", ""));
                timeZones.Add(new TimeZone(23340, 9420, 6483333333333330.0, 2.61666666666666e+16, "Benin", "BJ", "Africa/Porto-Novo", ""));
                timeZones.Add(new TimeZone(64380, -226260, 1.78833333333333e+16, -62.85, "St Barthelemy", "BL", "America/St_Barthelemy", ""));
                timeZones.Add(new TimeZone(116220, -233160, 3228333333333330.0, -6476666666666660.0, "Bermuda", "BM", "Atlantic/Bermuda", ""));
                timeZones.Add(new TimeZone(17760, 413700, 4933333333333330.0, 1.14916666666666e+16, "Brunei", "BN", "Asia/Brunei", ""));
                timeZones.Add(new TimeZone(-59400, -245340, -16.5, -68.15, "Bolivia", "BO", "America/La_Paz", ""));
                timeZones.Add(new TimeZone(43743, -245796, 1.21508333333333e+16, -6827666666666660.0, "Caribbean NL", "BQ", "America/Kralendijk", ""));
                timeZones.Add(new TimeZone(-13860, -116700, -3.85, -3.24166666666666e+16, "Brazil", "BR", "America/Noronha", "Atlantic islands"));
                timeZones.Add(new TimeZone(-5220, -174540, -1.45, -4.84833333333333e+16, "Brazil", "BR", "America/Belem", "Para (east), Amapa"));
                timeZones.Add(new TimeZone(-13380, -138600, -3716666666666660.0, -38.5, "Brazil", "BR", "America/Fortaleza", "Brazil (northeast: MA, PI, CE, RN, PB)"));
                timeZones.Add(new TimeZone(-28980, -125640, -8.05, -34.9, "Brazil", "BR", "America/Recife", "Pernambuco"));
                timeZones.Add(new TimeZone(-25920, -173520, -7.2, -48.2, "Brazil", "BR", "America/Araguaina", "Tocantins"));
                timeZones.Add(new TimeZone(-34800, -128580, -9666666666666660.0, -3571666666666660.0, "Brazil", "BR", "America/Maceio", "Alagoas, Sergipe"));
                timeZones.Add(new TimeZone(-46740, -138660, -1.29833333333333e+16, -3.85166666666666e+16, "Brazil", "BR", "America/Bahia", "Bahia"));
                timeZones.Add(new TimeZone(-84720, -167820, -2.35333333333333e+16, -4661666666666660.0, "Brazil", "BR", "America/Sao_Paulo", "Brazil (southeast: GO, DF, MG, ES, RJ, SP, PR, SC, RS)"));
                timeZones.Add(new TimeZone(-73620, -196620, -20.45, -5461666666666660.0, "Brazil", "BR", "America/Campo_Grande", "Mato Grosso do Sul"));
                timeZones.Add(new TimeZone(-56100, -201900, -1.55833333333333e+16, -5.60833333333333e+16, "Brazil", "BR", "America/Cuiaba", "Mato Grosso"));
                timeZones.Add(new TimeZone(-8760, -197520, -2433333333333330.0, -5486666666666660.0, "Brazil", "BR", "America/Santarem", "Para (west)"));
                timeZones.Add(new TimeZone(-31560, -230040, -8766666666666660.0, -63.9, "Brazil", "BR", "America/Porto_Velho", "Rondonia"));
                timeZones.Add(new TimeZone(10140, -218400, 2816666666666660.0, -6.06666666666666e+16, "Brazil", "BR", "America/Boa_Vista", "Roraima"));
                timeZones.Add(new TimeZone(-11280, -216060, -3.13333333333333e+16, -6.00166666666666e+16, "Brazil", "BR", "America/Manaus", "Amazonas (east)"));
                timeZones.Add(new TimeZone(-24000, -251520, -6666666666666660.0, -6986666666666660.0, "Brazil", "BR", "America/Eirunepe", "Amazonas (west)"));
                timeZones.Add(new TimeZone(-35880, -244080, -9966666666666660.0, -67.8, "Brazil", "BR", "America/Rio_Branco", "Acre"));
                timeZones.Add(new TimeZone(90300, -278460, 2.50833333333333e+16, -77.35, "Bahamas", "BS", "America/Nassau", ""));
                timeZones.Add(new TimeZone(98880, 322740, 2.74666666666666e+16, 89.65, "Bhutan", "BT", "Asia/Thimphu", ""));
                timeZones.Add(new TimeZone(-88740, 93300, -24.65, 2.59166666666666e+16, "Botswana", "BW", "Africa/Gaborone", ""));
                timeZones.Add(new TimeZone(194040, 99240, 53.9, 2.75666666666666e+16, "Belarus", "BY", "Europe/Minsk", ""));
                timeZones.Add(new TimeZone(63000, -317520, 17.5, -88.2, "Belize", "BZ", "America/Belize", ""));
                timeZones.Add(new TimeZone(171240, -189780, 4756666666666660.0, -5271666666666660.0, "Canada", "CA", "America/St_Johns", "Newfoundland, Labrador (SE)"));
                timeZones.Add(new TimeZone(160740, -228960, 44.65, -63.6, "Canada", "CA", "America/Halifax", "Atlantic - NS (most areas), PE"));
                timeZones.Add(new TimeZone(166320, -215820, 46.2, -59.95, "Canada", "CA", "America/Glace_Bay", "Atlantic - NS (Cape Breton)"));
                timeZones.Add(new TimeZone(165960, -233220, 46.1, -6478333333333330.0, "Canada", "CA", "America/Moncton", "Atlantic - New Brunswick"));
                timeZones.Add(new TimeZone(192000, -217500, 5.33333333333333e+16, -6.04166666666666e+16, "Canada", "CA", "America/Goose_Bay", "Atlantic - Labrador (most areas)"));
                timeZones.Add(new TimeZone(185100, -205620, 5.14166666666666e+16, -5711666666666660.0, "Canada", "CA", "America/Blanc-Sablon", "AST - QC (Lower North Shore)"));
                timeZones.Add(new TimeZone(157140, -285780, 43.65, -7938333333333330.0, "Canada", "CA", "America/Toronto", "Eastern - ON & QC (most areas)"));
                timeZones.Add(new TimeZone(229440, -246480, 6.37333333333333e+16, -6846666666666660.0, "Canada", "CA", "America/Iqaluit", "Eastern - NU (most areas)"));
                timeZones.Add(new TimeZone(175531, -329838, 4875861111111110.0, -9162166666666660.0, "Canada", "CA", "America/Atikokan", "EST - ON (Atikokan), NU (Coral H)"));
                timeZones.Add(new TimeZone(179580, -349740, 4988333333333330.0, -97.15, "Canada", "CA", "America/Winnipeg", "Central - ON (west), Manitoba"));
                timeZones.Add(new TimeZone(268904, -341385, 7469555555555550.0, -9482916666666660.0, "Canada", "CA", "America/Resolute", "Central - NU (Resolute)"));
                timeZones.Add(new TimeZone(226140, -331499, 6281666666666660.0, -9208305555555550.0, "Canada", "CA", "America/Rankin_Inlet", "Central - NU (central)"));
                timeZones.Add(new TimeZone(181440, -376740, 50.4, -104.65, "Canada", "CA", "America/Regina", "CST - SK (most areas)"));
                timeZones.Add(new TimeZone(181020, -388200, 5028333333333330.0, -1.07833333333333e+16, "Canada", "CA", "America/Swift_Current", "CST - SK (midwest)"));
                timeZones.Add(new TimeZone(192780, -408480, 53.55, -1.13466666666666e+16, "Canada", "CA", "America/Edmonton", "Mountain - AB, BC(E), NT(E), SK(W)"));
                timeZones.Add(new TimeZone(248810, -378190, 691138888888889.0, -1.05052777777777e+16, "Canada", "CA", "America/Cambridge_Bay", "Mountain - NU (west)"));
                timeZones.Add(new TimeZone(246059, -481380, 6834972222222220.0, -1.33716666666666e+16, "Canada", "CA", "America/Inuvik", "Mountain - NT (west)"));
                timeZones.Add(new TimeZone(176760, -419460, 49.1, -1.16516666666666e+16, "Canada", "CA", "America/Creston", "MST - BC (Creston)"));
                timeZones.Add(new TimeZone(200760, -432840, 5.57666666666666e+16, -1.20233333333333e+16, "Canada", "CA", "America/Dawson_Creek", "MST - BC (Dawson Cr, Ft St John)"));
                timeZones.Add(new TimeZone(211680, -441720, 58.8, -122.7, "Canada", "CA", "America/Fort_Nelson", "MST - BC (Ft Nelson)"));
                timeZones.Add(new TimeZone(218580, -486180, 6071666666666660.0, -135.05, "Canada", "CA", "America/Whitehorse", "MST - Yukon (east)"));
                timeZones.Add(new TimeZone(230640, -501900, 6406666666666660.0, -1.39416666666666e+16, "Canada", "CA", "America/Dawson", "MST - Yukon (west)"));
                timeZones.Add(new TimeZone(177360, -443220, 4.92666666666666e+16, -1.23116666666666e+16, "Canada", "CA", "America/Vancouver", "Pacific - BC (most areas)"));
                timeZones.Add(new TimeZone(-43800, 348900, -1.21666666666666e+16, 9691666666666660.0, "Cocos (Keeling) Islands", "CC", "Indian/Cocos", ""));
                timeZones.Add(new TimeZone(-15480, 55080, -4.3, 15.3, "Congo (Dem. Rep.)", "CD", "Africa/Kinshasa", "Dem. Rep. of Congo (west)"));
                timeZones.Add(new TimeZone(-42000, 98880, -1.16666666666666e+16, 2.74666666666666e+16, "Congo (Dem. Rep.)", "CD", "Africa/Lubumbashi", "Dem. Rep. of Congo (east)"));
                timeZones.Add(new TimeZone(15720, 66900, 4366666666666660.0, 1.85833333333333e+16, "Central African Rep.", "CF", "Africa/Bangui", ""));
                timeZones.Add(new TimeZone(-15360, 55020, -4266666666666660.0, 1.52833333333333e+16, "Congo (Rep.)", "CG", "Africa/Brazzaville", ""));
                timeZones.Add(new TimeZone(170580, 30720, 4738333333333330.0, 8533333333333330.0, "Switzerland", "CH", "Europe/Zurich", ""));
                timeZones.Add(new TimeZone(19140, -14520, 5316666666666660.0, -4033333333333330.0, "Côte d'Ivoire", "CI", "Africa/Abidjan", ""));
                timeZones.Add(new TimeZone(-76440, -575160, -2.12333333333333e+16, -1.59766666666666e+16, "Cook Islands", "CK", "Pacific/Rarotonga", ""));
                timeZones.Add(new TimeZone(-120420, -254400, -33.45, -7066666666666660.0, "Chile", "CL", "America/Santiago", "most of Chile"));
                timeZones.Add(new TimeZone(-191340, -255300, -53.15, -7091666666666660.0, "Chile", "CL", "America/Punta_Arenas", "Region of Magallanes"));
                timeZones.Add(new TimeZone(-97740, -393960, -27.15, -1.09433333333333e+16, "Chile", "CL", "Pacific/Easter", "Easter Island"));
                timeZones.Add(new TimeZone(14580, 34920, 4.05, 9.7, "Cameroon", "CM", "Africa/Douala", ""));
                timeZones.Add(new TimeZone(112440, 437280, 3.12333333333333e+16, 1.21466666666666e+16, "China", "CN", "Asia/Shanghai", "Beijing Time"));
                timeZones.Add(new TimeZone(157680, 315300, 43.8, 8758333333333330.0, "China", "CN", "Asia/Urumqi", "Xinjiang Time"));
                timeZones.Add(new TimeZone(16560, -266700, 4.6, -7408333333333330.0, "Colombia", "CO", "America/Bogota", ""));
                timeZones.Add(new TimeZone(35760, -302700, 9933333333333330.0, -8408333333333330.0, "Costa Rica", "CR", "America/Costa_Rica", ""));
                timeZones.Add(new TimeZone(83280, -296520, 2.31333333333333e+16, -8236666666666660.0, "Cuba", "CU", "America/Havana", ""));
                timeZones.Add(new TimeZone(53700, -84660, 1.49166666666666e+16, -2.35166666666666e+16, "Cape Verde", "CV", "Atlantic/Cape_Verde", ""));
                timeZones.Add(new TimeZone(43860, -248400, 1.21833333333333e+16, -69.0, "Curaçao", "CW", "America/Curacao", ""));
                timeZones.Add(new TimeZone(-37500, 380580, -1.04166666666666e+16, 1.05716666666666e+16, "Christmas Island", "CX", "Indian/Christmas", ""));
                timeZones.Add(new TimeZone(126600, 120120, 3.51666666666666e+16, 3336666666666660.0, "Cyprus", "CY", "Asia/Nicosia", "most of Cyprus"));
                timeZones.Add(new TimeZone(126420, 122220, 3511666666666660.0, 33.95, "Cyprus", "CY", "Asia/Famagusta", "Northern Cyprus"));
                timeZones.Add(new TimeZone(180300, 51960, 5.00833333333333e+16, 1.44333333333333e+16, "Czech Republic", "CZ", "Europe/Prague", ""));
                timeZones.Add(new TimeZone(189000, 48120, 52.5, 1.33666666666666e+16, "Germany", "DE", "Europe/Berlin", "most of Germany"));
                timeZones.Add(new TimeZone(171720, 31260, 47.7, 8683333333333330.0, "Germany", "DE", "Europe/Busingen", "Busingen"));
                timeZones.Add(new TimeZone(41760, 155340, 11.6, 43.15, "Djibouti", "DJ", "Africa/Djibouti", ""));
                timeZones.Add(new TimeZone(200400, 45300, 5.56666666666666e+16, 1.25833333333333e+16, "Denmark", "DK", "Europe/Copenhagen", ""));
                timeZones.Add(new TimeZone(55080, -221040, 15.3, -61.4, "Dominica", "DM", "America/Dominica", ""));
                timeZones.Add(new TimeZone(66480, -251640, 1.84666666666666e+16, -69.9, "Dominican Republic", "DO", "America/Santo_Domingo", ""));
                timeZones.Add(new TimeZone(132420, 10980, 3678333333333330.0, 3.05, "Algeria", "DZ", "Africa/Algiers", ""));
                timeZones.Add(new TimeZone(-7800, -287400, -2.16666666666666e+16, -7983333333333330.0, "Ecuador", "EC", "America/Guayaquil", "Ecuador (mainland)"));
                timeZones.Add(new TimeZone(-3240, -322560, -0.9, -89.6, "Ecuador", "EC", "Pacific/Galapagos", "Galapagos Islands"));
                timeZones.Add(new TimeZone(213900, 89100, 5.94166666666666e+16, 24.75, "Estonia", "EE", "Europe/Tallinn", ""));
                timeZones.Add(new TimeZone(108180, 112500, 30.05, 31.25, "Egypt", "EG", "Africa/Cairo", ""));
                timeZones.Add(new TimeZone(97740, -47520, 27.15, -13.2, "Western Sahara", "EH", "Africa/El_Aaiun", ""));
                timeZones.Add(new TimeZone(55200, 139980, 1.53333333333333e+16, 3888333333333330.0, "Eritrea", "ER", "Africa/Asmara", ""));
                timeZones.Add(new TimeZone(145440, -13260, 40.4, -3683333333333330.0, "Spain", "ES", "Europe/Madrid", "Spain (mainland)"));
                timeZones.Add(new TimeZone(129180, -19140, 3588333333333330.0, -5316666666666660.0, "Spain", "ES", "Africa/Ceuta", "Ceuta, Melilla"));
                timeZones.Add(new TimeZone(101160, -55440, 28.1, -15.4, "Spain", "ES", "Atlantic/Canary", "Canary Islands"));
                timeZones.Add(new TimeZone(32520, 139320, 9033333333333330.0, 38.7, "Ethiopia", "ET", "Africa/Addis_Ababa", ""));
                timeZones.Add(new TimeZone(216600, 89880, 6.01666666666666e+16, 2.49666666666666e+16, "Finland", "FI", "Europe/Helsinki", ""));
                timeZones.Add(new TimeZone(-65280, 642300, -1.81333333333333e+16, 1.78416666666666e+16, "Fiji", "FJ", "Pacific/Fiji", ""));
                timeZones.Add(new TimeZone(-186120, -208260, -51.7, -57.85, "Falkland Islands", "FK", "Atlantic/Stanley", ""));
                timeZones.Add(new TimeZone(26700, 546420, 7416666666666660.0, 1.51783333333333e+16, "Micronesia", "FM", "Pacific/Chuuk", "Chuuk/Truk, Yap"));
                timeZones.Add(new TimeZone(25080, 569580, 6966666666666660.0, 1.58216666666666e+16, "Micronesia", "FM", "Pacific/Pohnpei", "Pohnpei/Ponape"));
                timeZones.Add(new TimeZone(19140, 586740, 5316666666666660.0, 1.62983333333333e+16, "Micronesia", "FM", "Pacific/Kosrae", "Kosrae"));
                timeZones.Add(new TimeZone(223260, -24360, 6.20166666666666e+16, -6766666666666660.0, "Faroe Islands", "FO", "Atlantic/Faroe", ""));
                timeZones.Add(new TimeZone(175920, 8400, 4886666666666660.0, 2.33333333333333e+16, "France", "FR", "Europe/Paris", ""));
                timeZones.Add(new TimeZone(1380, 34020, 0.3833333333333333, 9.45, "Gabon", "GA", "Africa/Libreville", ""));
                timeZones.Add(new TimeZone(185430, -451, 5150833333333330.0, -0.1252777777777777, "Britain (UK)", "GB", "Europe/London", ""));
                timeZones.Add(new TimeZone(43380, -222300, 12.05, -61.75, "Grenada", "GD", "America/Grenada", ""));
                timeZones.Add(new TimeZone(150180, 161340, 4171666666666660.0, 4481666666666660.0, "Georgia", "GE", "Asia/Tbilisi", ""));
                timeZones.Add(new TimeZone(17760, -188400, 4933333333333330.0, -5.23333333333333e+16, "French Guiana", "GF", "America/Cayenne", ""));
                timeZones.Add(new TimeZone(178037, -9130, 4945472222222220.0, -2536111111111110.0, "Guernsey", "GG", "Europe/Guernsey", ""));
                timeZones.Add(new TimeZone(19980, -780, 5.55, -0.2166666666666666, "Ghana", "GH", "Africa/Accra", ""));
                timeZones.Add(new TimeZone(130080, -19260, 3613333333333330.0, -5.35, "Gibraltar", "GI", "Europe/Gibraltar", ""));
                timeZones.Add(new TimeZone(231060, -186240, 6418333333333330.0, -5.17333333333333e+16, "Greenland", "GL", "America/Nuuk", "most of Greenland"));
                timeZones.Add(new TimeZone(276360, -67200, 7676666666666660.0, -1.86666666666666e+16, "Greenland", "GL", "America/Danmarkshavn", "National Park (east coast)"));
                timeZones.Add(new TimeZone(253740, -79080, 7048333333333330.0, -2.19666666666666e+16, "Greenland", "GL", "America/Scoresbysund", "Scoresbysund/Ittoqqortoormiit"));
                timeZones.Add(new TimeZone(275640, -247620, 7656666666666660.0, -6878333333333330.0, "Greenland", "GL", "America/Thule", "Thule/Pituffik"));
                timeZones.Add(new TimeZone(48480, -59940, 1.34666666666666e+16, -16.65, "Gambia", "GM", "Africa/Banjul", ""));
                timeZones.Add(new TimeZone(34260, -49380, 9516666666666660.0, -1.37166666666666e+16, "Guinea", "GN", "Africa/Conakry", ""));
                timeZones.Add(new TimeZone(58440, -221520, 1.62333333333333e+16, -6153333333333330.0, "Guadeloupe", "GP", "America/Guadeloupe", ""));
                timeZones.Add(new TimeZone(13500, 31620, 3.75, 8783333333333330.0, "Equatorial Guinea", "GQ", "Africa/Malabo", ""));
                timeZones.Add(new TimeZone(136680, 85380, 3796666666666660.0, 2.37166666666666e+16, "Greece", "GR", "Europe/Athens", ""));
                timeZones.Add(new TimeZone(-195360, -131520, -5.42666666666666e+16, -3653333333333330.0, "South Georgia & the South Sandwich Islands", "GS", "Atlantic/South_Georgia", ""));
                timeZones.Add(new TimeZone(52680, -325860, 1.46333333333333e+16, -9051666666666660.0, "Guatemala", "GT", "America/Guatemala", ""));
                timeZones.Add(new TimeZone(48480, 521100, 1.34666666666666e+16, 144.75, "Guam", "GU", "Pacific/Guam", ""));
                timeZones.Add(new TimeZone(42660, -56100, 11.85, -1.55833333333333e+16, "Guinea-Bissau", "GW", "Africa/Bissau", ""));
                timeZones.Add(new TimeZone(24480, -209400, 6.8, -5.81666666666666e+16, "Guyana", "GY", "America/Guyana", ""));
                timeZones.Add(new TimeZone(80220, 410940, 2.22833333333333e+16, 114.15, "Hong Kong", "HK", "Asia/Hong_Kong", ""));
                timeZones.Add(new TimeZone(50760, -313980, 14.1, -8721666666666660.0, "Honduras", "HN", "America/Tegucigalpa", ""));
                timeZones.Add(new TimeZone(164880, 57480, 45.8, 1.59666666666666e+16, "Croatia", "HR", "Europe/Zagreb", ""));
                timeZones.Add(new TimeZone(66720, -260400, 1.85333333333333e+16, -7233333333333330.0, "Haiti", "HT", "America/Port-au-Prince", ""));
                timeZones.Add(new TimeZone(171000, 68700, 47.5, 1.90833333333333e+16, "Hungary", "HU", "Europe/Budapest", ""));
                timeZones.Add(new TimeZone(-22200, 384480, -6166666666666660.0, 106.8, "Indonesia", "ID", "Asia/Jakarta", "Java, Sumatra"));
                timeZones.Add(new TimeZone(-120, 393600, -0.0333333333333333, 1.09333333333333e+16, "Indonesia", "ID", "Asia/Pontianak", "Borneo (west, central)"));
                timeZones.Add(new TimeZone(-18420, 429840, -5116666666666660.0, 119.4, "Indonesia", "ID", "Asia/Makassar", "Borneo (east, south), Sulawesi/Celebes, Bali, Nusa Tengarra, Timor (west)"));
                timeZones.Add(new TimeZone(-9120, 506520, -2533333333333330.0, 140.7, "Indonesia", "ID", "Asia/Jayapura", "New Guinea (West Papua / Irian Jaya), Malukus/Moluccas"));
                timeZones.Add(new TimeZone(192000, -22500, 5.33333333333333e+16, -6.25, "Ireland", "IE", "Europe/Dublin", ""));
                timeZones.Add(new TimeZone(114410, 126806, 3.17805555555555e+16, 3522388888888880.0, "Israel", "IL", "Asia/Jerusalem", ""));
                timeZones.Add(new TimeZone(194940, -16080, 54.15, -4466666666666660.0, "Isle of Man", "IM", "Europe/Isle_of_Man", ""));
                timeZones.Add(new TimeZone(81120, 318120, 2.25333333333333e+16, 8836666666666660.0, "India", "IN", "Asia/Kolkata", ""));
                timeZones.Add(new TimeZone(-26400, 260700, -****************.0, 7241666666666660.0, "British Indian Ocean Territory", "IO", "Indian/Chagos", ""));
                timeZones.Add(new TimeZone(120060, 159900, 33.35, 4.44166666666666e+16, "Iraq", "IQ", "Asia/Baghdad", ""));
                timeZones.Add(new TimeZone(128400, 185160, 3.56666666666666e+16, 5143333333333330.0, "Iran", "IR", "Asia/Tehran", ""));
                timeZones.Add(new TimeZone(230940, -78660, 64.15, -21.85, "Iceland", "IS", "Atlantic/Reykjavik", ""));
                timeZones.Add(new TimeZone(150840, 44940, 41.9, 1.24833333333333e+16, "Italy", "IT", "Europe/Rome", ""));
                timeZones.Add(new TimeZone(177061, -7584, 4918361111111110.0, -2.10666666666666e+16, "Jersey", "JE", "Europe/Jersey", ""));
                timeZones.Add(new TimeZone(64685, -276456, 1.79680555555555e+16, -7679333333333330.0, "Jamaica", "JM", "America/Jamaica", ""));
                timeZones.Add(new TimeZone(115020, 129360, 31.95, 3593333333333330.0, "Jordan", "JO", "Asia/Amman", ""));
                timeZones.Add(new TimeZone(128356, 503081, 3.56544444444444e+16, 1397447222222220.0, "Japan", "JP", "Asia/Tokyo", ""));
                timeZones.Add(new TimeZone(-4620, 132540, -1.28333333333333e+16, 3681666666666660.0, "Kenya", "KE", "Africa/Nairobi", ""));
                timeZones.Add(new TimeZone(154440, 268560, 42.9, 74.6, "Kyrgyzstan", "KG", "Asia/Bishkek", ""));
                timeZones.Add(new TimeZone(41580, 377700, 11.55, 1.04916666666666e+16, "Cambodia", "KH", "Asia/Phnom_Penh", ""));
                timeZones.Add(new TimeZone(5100, 622800, 1.41666666666666e+16, 173.0, "Kiribati", "KI", "Pacific/Tarawa", "Gilbert Islands"));
                timeZones.Add(new TimeZone(-10020, -618180, -2783333333333330.0, -1.71716666666666e+16, "Kiribati", "KI", "Pacific/Kanton", "Phoenix Islands"));
                timeZones.Add(new TimeZone(6720, -566400, 1.86666666666666e+16, -1.57333333333333e+16, "Kiribati", "KI", "Pacific/Kiritimati", "Line Islands"));
                timeZones.Add(new TimeZone(-42060, 155760, -1.16833333333333e+16, 4.32666666666666e+16, "Comoros", "KM", "Indian/Comoro", ""));
                timeZones.Add(new TimeZone(62280, -225780, 17.3, -6271666666666660.0, "St Kitts & Nevis", "KN", "America/St_Kitts", ""));
                timeZones.Add(new TimeZone(140460, 452700, 3.90166666666666e+16, 125.75, "Korea (North)", "KP", "Asia/Pyongyang", ""));
                timeZones.Add(new TimeZone(135180, 457080, 37.55, 1.26966666666666e+16, "Korea (South)", "KR", "Asia/Seoul", ""));
                timeZones.Add(new TimeZone(105600, 172740, 2.93333333333333e+16, 4.79833333333333e+16, "Kuwait", "KW", "Asia/Kuwait", ""));
                timeZones.Add(new TimeZone(69480, -292980, 19.3, -8138333333333330.0, "Cayman Islands", "KY", "America/Cayman", ""));
                timeZones.Add(new TimeZone(155700, 277020, 43.25, 76.95, "Kazakhstan", "KZ", "Asia/Almaty", "most of Kazakhstan"));
                timeZones.Add(new TimeZone(161280, 235680, 44.8, 6546666666666660.0, "Kazakhstan", "KZ", "Asia/Qyzylorda", "Qyzylorda/Kyzylorda/Kzyl-Orda"));
                timeZones.Add(new TimeZone(191520, 229020, 53.2, 6361666666666660.0, "Kazakhstan", "KZ", "Asia/Qostanay", "Qostanay/Kostanay/Kustanay"));
                timeZones.Add(new TimeZone(181020, 205800, 5028333333333330.0, 5.71666666666666e+16, "Kazakhstan", "KZ", "Asia/Aqtobe", "Aqtobe/Aktobe"));
                timeZones.Add(new TimeZone(160260, 180960, 4.45166666666666e+16, 5.02666666666666e+16, "Kazakhstan", "KZ", "Asia/Aqtau", "Mangghystau/Mankistau"));
                timeZones.Add(new TimeZone(169620, 186960, 4711666666666660.0, 5193333333333330.0, "Kazakhstan", "KZ", "Asia/Atyrau", "Atyrau/Atirau/Gur'yev"));
                timeZones.Add(new TimeZone(184380, 184860, 5121666666666660.0, 51.35, "Kazakhstan", "KZ", "Asia/Oral", "West Kazakhstan"));
                timeZones.Add(new TimeZone(64680, 369360, 1.79666666666666e+16, 102.6, "Laos", "LA", "Asia/Vientiane", ""));
                timeZones.Add(new TimeZone(121980, 127800, 3388333333333330.0, 35.5, "Lebanon", "LB", "Asia/Beirut", ""));
                timeZones.Add(new TimeZone(50460, -219600, 1.40166666666666e+16, -61.0, "St Lucia", "LC", "America/St_Lucia", ""));
                timeZones.Add(new TimeZone(169740, 34260, 47.15, 9516666666666660.0, "Liechtenstein", "LI", "Europe/Vaduz", ""));
                timeZones.Add(new TimeZone(24960, 287460, 6933333333333330.0, 79.85, "Sri Lanka", "LK", "Asia/Colombo", ""));
                timeZones.Add(new TimeZone(22680, -38820, 6.3, -1.07833333333333e+16, "Liberia", "LR", "Africa/Monrovia", ""));
                timeZones.Add(new TimeZone(-106080, 99000, -2.94666666666666e+16, 27.5, "Lesotho", "LS", "Africa/Maseru", ""));
                timeZones.Add(new TimeZone(196860, 91140, 5468333333333330.0, 2.53166666666666e+16, "Lithuania", "LT", "Europe/Vilnius", ""));
                timeZones.Add(new TimeZone(178560, 22140, 49.6, 6.15, "Luxembourg", "LU", "Europe/Luxembourg", ""));
                timeZones.Add(new TimeZone(205020, 86760, 56.95, 24.1, "Latvia", "LV", "Europe/Riga", ""));
                timeZones.Add(new TimeZone(118440, 47460, 32.9, 1.31833333333333e+16, "Libya", "LY", "Africa/Tripoli", ""));
                timeZones.Add(new TimeZone(121140, -27300, 33.65, -7583333333333330.0, "Morocco", "MA", "Africa/Casablanca", ""));
                timeZones.Add(new TimeZone(157320, 26580, 43.7, 7383333333333330.0, "Monaco", "MC", "Europe/Monaco", ""));
                timeZones.Add(new TimeZone(169200, 103800, 47.0, 2.88333333333333e+16, "Moldova", "MD", "Europe/Chisinau", ""));
                timeZones.Add(new TimeZone(152760, 69360, 4243333333333330.0, 1.92666666666666e+16, "Montenegro", "ME", "Europe/Podgorica", ""));
                timeZones.Add(new TimeZone(65040, -227100, 1.80666666666666e+16, -6.30833333333333e+16, "St Martin (French)", "MF", "America/Marigot", ""));
                timeZones.Add(new TimeZone(-68100, 171060, -1.89166666666666e+16, 4.75166666666666e+16, "Madagascar", "MG", "Indian/Antananarivo", ""));
                timeZones.Add(new TimeZone(25740, 616320, 7.15, 171.2, "Marshall Islands", "MH", "Pacific/Majuro", "most of Marshall Islands"));
                timeZones.Add(new TimeZone(32700, 602400, 9083333333333330.0, 1.67333333333333e+16, "Marshall Islands", "MH", "Pacific/Kwajalein", "Kwajalein"));
                timeZones.Add(new TimeZone(151140, 77160, 4.19833333333333e+16, 2.14333333333333e+16, "North Macedonia", "MK", "Europe/Skopje", ""));
                timeZones.Add(new TimeZone(45540, -28800, 12.65, -8.0, "Mali", "ML", "Africa/Bamako", ""));
                timeZones.Add(new TimeZone(60420, 346200, 1.67833333333333e+16, 9616666666666660.0, "Myanmar (Burma)", "MM", "Asia/Yangon", ""));
                timeZones.Add(new TimeZone(172500, 384780, 4.79166666666666e+16, 1.06883333333333e+16, "Mongolia", "MN", "Asia/Ulaanbaatar", "most of Mongolia"));
                timeZones.Add(new TimeZone(172860, 329940, 4.80166666666666e+16, 91.65, "Mongolia", "MN", "Asia/Hovd", "Bayan-Olgii, Hovd, Uvs"));
                timeZones.Add(new TimeZone(79910, 408750, 2.21972222222222e+16, 1.13541666666666e+16, "Macau", "MO", "Asia/Macau", ""));
                timeZones.Add(new TimeZone(54720, 524700, 15.2, 145.75, "Northern Mariana Islands", "MP", "Pacific/Saipan", ""));
                timeZones.Add(new TimeZone(52560, -219900, 14.6, -6.10833333333333e+16, "Martinique", "MQ", "America/Martinique", ""));
                timeZones.Add(new TimeZone(65160, -57420, 18.1, -15.95, "Mauritania", "MR", "Africa/Nouakchott", ""));
                timeZones.Add(new TimeZone(60180, -223980, 1.67166666666666e+16, -6221666666666660.0, "Montserrat", "MS", "America/Montserrat", ""));
                timeZones.Add(new TimeZone(129240, 52260, 35.9, 1.45166666666666e+16, "Malta", "MT", "Europe/Malta", ""));
                timeZones.Add(new TimeZone(-72600, 207000, -2.01666666666666e+16, 57.5, "Mauritius", "MU", "Indian/Mauritius", ""));
                timeZones.Add(new TimeZone(15000, 264600, 4166666666666660.0, 73.5, "Maldives", "MV", "Indian/Maldives", ""));
                timeZones.Add(new TimeZone(-56820, 126000, -1.57833333333333e+16, 35.0, "Malawi", "MW", "Africa/Blantyre", ""));
                timeZones.Add(new TimeZone(69840, -356940, 19.4, -99.15, "Mexico", "MX", "America/Mexico_City", "Central Mexico"));
                timeZones.Add(new TimeZone(75900, -312360, 2.10833333333333e+16, -8676666666666660.0, "Mexico", "MX", "America/Cancun", "Quintana Roo"));
                timeZones.Add(new TimeZone(75480, -322620, 2.09666666666666e+16, -8961666666666660.0, "Mexico", "MX", "America/Merida", "Campeche, Yucatan"));
                timeZones.Add(new TimeZone(92400, -361140, 2.56666666666666e+16, -1.00316666666666e+16, "Mexico", "MX", "America/Monterrey", "Durango; Coahuila, Nuevo Leon, Tamaulipas (most areas)"));
                timeZones.Add(new TimeZone(93000, -351000, 2.58333333333333e+16, -97.5, "Mexico", "MX", "America/Matamoros", "Coahuila, Nuevo Leon, Tamaulipas (US border)"));
                timeZones.Add(new TimeZone(103080, -381900, 2.86333333333333e+16, -1.06083333333333e+16, "Mexico", "MX", "America/Chihuahua", "Chihuahua (most areas)"));
                timeZones.Add(new TimeZone(114240, -383340, 3.17333333333333e+16, -1.06483333333333e+16, "Mexico", "MX", "America/Ciudad_Juarez", "Chihuahua (US border - west)"));
                timeZones.Add(new TimeZone(106440, -375900, 2.95666666666666e+16, -1.04416666666666e+16, "Mexico", "MX", "America/Ojinaga", "Chihuahua (US border - east)"));
                timeZones.Add(new TimeZone(83580, -383100, 2.32166666666666e+16, -1.06416666666666e+16, "Mexico", "MX", "America/Mazatlan", "Baja California Sur, Nayarit (most areas), Sinaloa"));
                timeZones.Add(new TimeZone(74880, -378900, 20.8, -105.25, "Mexico", "MX", "America/Bahia_Banderas", "Bahia de Banderas"));
                timeZones.Add(new TimeZone(104640, -399480, 2.90666666666666e+16, -1.10966666666666e+16, "Mexico", "MX", "America/Hermosillo", "Sonora"));
                timeZones.Add(new TimeZone(117120, -421260, 3253333333333330.0, -1.17016666666666e+16, "Mexico", "MX", "America/Tijuana", "Baja California"));
                timeZones.Add(new TimeZone(11400, 366120, 3.16666666666666e+16, 101.7, "Malaysia", "MY", "Asia/Kuala_Lumpur", "Malaysia (peninsula)"));
                timeZones.Add(new TimeZone(5580, 397200, 1.55, 1.10333333333333e+16, "Malaysia", "MY", "Asia/Kuching", "Sabah, Sarawak"));
                timeZones.Add(new TimeZone(-93480, 117300, -2.59666666666666e+16, 3.25833333333333e+16, "Mozambique", "MZ", "Africa/Maputo", ""));
                timeZones.Add(new TimeZone(-81240, 61560, -2.25666666666666e+16, 17.1, "Namibia", "NA", "Africa/Windhoek", ""));
                timeZones.Add(new TimeZone(-80160, 599220, -2.22666666666666e+16, 166.45, "New Caledonia", "NC", "Pacific/Noumea", ""));
                timeZones.Add(new TimeZone(48660, 7620, 1.35166666666666e+16, 2.11666666666666e+16, "Niger", "NE", "Africa/Niamey", ""));
                timeZones.Add(new TimeZone(-104580, 604680, -29.05, 1.67966666666666e+16, "Norfolk Island", "NF", "Pacific/Norfolk", ""));
                timeZones.Add(new TimeZone(23220, 12240, 6.45, 3.4, "Nigeria", "NG", "Africa/Lagos", ""));
                timeZones.Add(new TimeZone(43740, -310620, 12.15, -8628333333333330.0, "Nicaragua", "NI", "America/Managua", ""));
                timeZones.Add(new TimeZone(188520, 17640, 5236666666666660.0, 4.9, "Netherlands", "NL", "Europe/Amsterdam", ""));
                timeZones.Add(new TimeZone(215700, 38700, 5.99166666666666e+16, 10.75, "Norway", "NO", "Europe/Oslo", ""));
                timeZones.Add(new TimeZone(99780, 307140, 2.77166666666666e+16, 8531666666666660.0, "Nepal", "NP", "Asia/Kathmandu", ""));
                timeZones.Add(new TimeZone(-1860, 600900, -0.5166666666666667, 1.66916666666666e+16, "Nauru", "NR", "Pacific/Nauru", ""));
                timeZones.Add(new TimeZone(-68460, -611700, -1.90166666666666e+16, -1.69916666666666e+16, "Niue", "NU", "Pacific/Niue", ""));
                timeZones.Add(new TimeZone(-132720, 629160, -3686666666666660.0, 1.74766666666666e+16, "New Zealand", "NZ", "Pacific/Auckland", "most of New Zealand"));
                timeZones.Add(new TimeZone(-158220, -635580, -43.95, -176.55, "New Zealand", "NZ", "Pacific/Chatham", "Chatham Islands"));
                timeZones.Add(new TimeZone(84960, 210900, 23.6, 5.85833333333333e+16, "Oman", "OM", "Asia/Muscat", ""));
                timeZones.Add(new TimeZone(32280, -286320, 8966666666666660.0, -7953333333333330.0, "Panama", "PA", "America/Panama", ""));
                timeZones.Add(new TimeZone(-43380, -277380, -12.05, -77.05, "Peru", "PE", "America/Lima", ""));
                timeZones.Add(new TimeZone(-63120, -538440, -1.75333333333333e+16, -1.49566666666666e+16, "French Polynesia", "PF", "Pacific/Tahiti", "Society Islands"));
                timeZones.Add(new TimeZone(-32400, -502200, -9.0, -139.5, "French Polynesia", "PF", "Pacific/Marquesas", "Marquesas Islands"));
                timeZones.Add(new TimeZone(-83280, -485820, -2.31333333333333e+16, -134.95, "French Polynesia", "PF", "Pacific/Gambier", "Gambier Islands"));
                timeZones.Add(new TimeZone(-34200, 529800, -9.5, 1.47166666666666e+16, "Papua New Guinea", "PG", "Pacific/Port_Moresby", "most of Papua New Guinea"));
                timeZones.Add(new TimeZone(-22380, 560040, -6216666666666660.0, 1.55566666666666e+16, "Papua New Guinea", "PG", "Pacific/Bougainville", "Bougainville"));
                timeZones.Add(new TimeZone(52512, 435484, 1.45866666666666e+16, 1.20967777777777e+16, "Philippines", "PH", "Asia/Manila", ""));
                timeZones.Add(new TimeZone(89520, 241380, 2.48666666666666e+16, 67.05, "Pakistan", "PK", "Asia/Karachi", ""));
                timeZones.Add(new TimeZone(188100, 75600, 52.25, 21.0, "Poland", "PL", "Europe/Warsaw", ""));
                timeZones.Add(new TimeZone(169380, -202800, 47.05, -5.63333333333333e+16, "St Pierre & Miquelon", "PM", "America/Miquelon", ""));
                timeZones.Add(new TimeZone(-90240, -468300, -2.50666666666666e+16, -1.30083333333333e+16, "Pitcairn", "PN", "Pacific/Pitcairn", ""));
                timeZones.Add(new TimeZone(66486, -237982, 1.84683333333333e+16, -***************.0, "Puerto Rico", "PR", "America/Puerto_Rico", ""));
                timeZones.Add(new TimeZone(113400, 124080, 31.5, ****************.0, "Palestine", "PS", "Asia/Gaza", "Gaza Strip"));
                timeZones.Add(new TimeZone(113520, 126342, 3.15333333333333e+16, 35095.0, "Palestine", "PS", "Asia/Hebron", "West Bank"));
                timeZones.Add(new TimeZone(139380, -32880, ****************.0, -****************.0, "Portugal", "PT", "Europe/Lisbon", "Portugal (mainland)"));
                timeZones.Add(new TimeZone(117480, -60840, ****************.0, -16.9, "Portugal", "PT", "Atlantic/Madeira", "Madeira Islands"));
                timeZones.Add(new TimeZone(135840, -92400, 3.77333333333333e+16, -2.56666666666666e+16, "Portugal", "PT", "Atlantic/Azores", "Azores"));
                timeZones.Add(new TimeZone(26400, 484140, ****************.0, 1.34483333333333e+16, "Palau", "PW", "Pacific/Palau", ""));
                timeZones.Add(new TimeZone(-90960, -207600, -2.52666666666666e+16, -5.76666666666666e+16, "Paraguay", "PY", "America/Asuncion", ""));
                timeZones.Add(new TimeZone(91020, 185520, 2.52833333333333e+16, ****************.0, "Qatar", "QA", "Asia/Qatar", ""));
                timeZones.Add(new TimeZone(-75120, 199680, -2.08666666666666e+16, ****************.0, "Réunion", "RE", "Indian/Reunion", ""));
                timeZones.Add(new TimeZone(159960, 93960, 4443333333333330.0, 26.1, "Romania", "RO", "Europe/Bucharest", ""));
                timeZones.Add(new TimeZone(161400, 73800, 4.48333333333333e+16, 20.5, "Serbia", "RS", "Europe/Belgrade", ""));
                timeZones.Add(new TimeZone(196980, 73800, 5471666666666660.0, 20.5, "Russia", "RU", "Europe/Kaliningrad", "MSK-01 - Kaliningrad"));
                timeZones.Add(new TimeZone(200721, 135424, 5.57558333333333e+16, 3.76177777777777e+16, "Russia", "RU", "Europe/Moscow", "MSK+00 - Moscow area"));
                timeZones.Add(new TimeZone(161820, 122760, 44.95, 34.1, "Ukraine", "UA", "Europe/Simferopol", "Crimea"));
                timeZones.Add(new TimeZone(210960, 178740, 58.6, 49.65, "Russia", "RU", "Europe/Kirov", "MSK+00 - Kirov"));
                timeZones.Add(new TimeZone(175440, 159900, 4.87333333333333e+16, 4.44166666666666e+16, "Russia", "RU", "Europe/Volgograd", "MSK+00 - Volgograd"));
                timeZones.Add(new TimeZone(166860, 172980, 46.35, 48.05, "Russia", "RU", "Europe/Astrakhan", "MSK+01 - Astrakhan"));
                timeZones.Add(new TimeZone(185640, 165720, 5156666666666660.0, 4603333333333330.0, "Russia", "RU", "Europe/Saratov", "MSK+01 - Saratov"));
                timeZones.Add(new TimeZone(195600, 174240, 5.43333333333333e+16, 48.4, "Russia", "RU", "Europe/Ulyanovsk", "MSK+01 - Ulyanovsk"));
                timeZones.Add(new TimeZone(191520, 180540, 53.2, 50.15, "Russia", "RU", "Europe/Samara", "MSK+01 - Samara, Udmurtia"));
                timeZones.Add(new TimeZone(204660, 218160, 56.85, 60.6, "Russia", "RU", "Asia/Yekaterinburg", "MSK+02 - Urals"));
                timeZones.Add(new TimeZone(198000, 264240, 55.0, 73.4, "Russia", "RU", "Asia/Omsk", "MSK+03 - Omsk"));
                timeZones.Add(new TimeZone(198120, 298500, 5503333333333330.0, 8291666666666660.0, "Russia", "RU", "Asia/Novosibirsk", "MSK+04 - Novosibirsk"));
                timeZones.Add(new TimeZone(192120, 301500, 5336666666666660.0, 83.75, "Russia", "RU", "Asia/Barnaul", "MSK+04 - Altai"));
                timeZones.Add(new TimeZone(203400, 305880, 56.5, 8496666666666660.0, "Russia", "RU", "Asia/Tomsk", "MSK+04 - Tomsk"));
                timeZones.Add(new TimeZone(193500, 313620, 53.75, 8711666666666660.0, "Russia", "RU", "Asia/Novokuznetsk", "MSK+04 - Kemerovo"));
                timeZones.Add(new TimeZone(201660, 334200, 5.60166666666666e+16, 9283333333333330.0, "Russia", "RU", "Asia/Krasnoyarsk", "MSK+04 - Krasnoyarsk area"));
                timeZones.Add(new TimeZone(188160, 375600, 5.22666666666666e+16, 1.04333333333333e+16, "Russia", "RU", "Asia/Irkutsk", "MSK+05 - Irkutsk, Buryatia"));
                timeZones.Add(new TimeZone(187380, 408480, 52.05, 1.13466666666666e+16, "Russia", "RU", "Asia/Chita", "MSK+06 - Zabaykalsky"));
                timeZones.Add(new TimeZone(223200, 466800, 62.0, 1.29666666666666e+16, "Russia", "RU", "Asia/Yakutsk", "MSK+06 - Lena River"));
                timeZones.Add(new TimeZone(225563, 487994, 6265638888888880.0, 1.35553888888888e+16, "Russia", "RU", "Asia/Khandyga", "MSK+06 - Tomponsky, Ust-Maysky"));
                timeZones.Add(new TimeZone(155400, 474960, 4.31666666666666e+16, 1.31933333333333e+16, "Russia", "RU", "Asia/Vladivostok", "MSK+07 - Amur River"));
                timeZones.Add(new TimeZone(232417, 515616, 6456027777777770.0, 1.43226666666666e+16, "Russia", "RU", "Asia/Ust-Nera", "MSK+07 - Oymyakonsky"));
                timeZones.Add(new TimeZone(214440, 542880, 5956666666666660.0, 150.8, "Russia", "RU", "Asia/Magadan", "MSK+08 - Magadan"));
                timeZones.Add(new TimeZone(169080, 513720, 4696666666666660.0, 142.7, "Russia", "RU", "Asia/Sakhalin", "MSK+08 - Sakhalin Island"));
                timeZones.Add(new TimeZone(242880, 553380, 6746666666666660.0, 1.53716666666666e+16, "Russia", "RU", "Asia/Srednekolymsk", "MSK+08 - Sakha (E), N Kuril Is"));
                timeZones.Add(new TimeZone(190860, 571140, 5.30166666666666e+16, 158.65, "Russia", "RU", "Asia/Kamchatka", "MSK+09 - Kamchatka"));
                timeZones.Add(new TimeZone(233100, 638940, 64.75, 1.77483333333333e+16, "Russia", "RU", "Asia/Anadyr", "MSK+09 - Bering Sea"));
                timeZones.Add(new TimeZone(-7020, 108240, -1.95, 3.00666666666666e+16, "Rwanda", "RW", "Africa/Kigali", ""));
                timeZones.Add(new TimeZone(88680, 168180, 2.46333333333333e+16, 4671666666666660.0, "Saudi Arabia", "SA", "Asia/Riyadh", ""));
                timeZones.Add(new TimeZone(-34320, 576720, -9533333333333330.0, 160.2, "Solomon Islands", "SB", "Pacific/Guadalcanal", ""));
                timeZones.Add(new TimeZone(-16800, 199680, -4666666666666660.0, ****************.0, "Seychelles", "SC", "Indian/Mahe", ""));
                timeZones.Add(new TimeZone(56160, 117120, 15.6, 3253333333333330.0, "Sudan", "SD", "Africa/Khartoum", ""));
                timeZones.Add(new TimeZone(213600, 64980, 5.93333333333333e+16, 18.05, "Sweden", "SE", "Europe/Stockholm", ""));
                timeZones.Add(new TimeZone(4620, 373860, 1.28333333333333e+16, 103.85, "Singapore", "SG", "Asia/Singapore", ""));
                timeZones.Add(new TimeZone(-57300, -20520, -1.59166666666666e+16, -5.7, "St Helena", "SH", "Atlantic/St_Helena", ""));
                timeZones.Add(new TimeZone(165780, 52260, 46.05, 1.45166666666666e+16, "Slovenia", "SI", "Europe/Ljubljana", ""));
                timeZones.Add(new TimeZone(280800, 57600, 78.0, 16.0, "Svalbard & Jan Mayen", "SJ", "Arctic/Longyearbyen", ""));
                timeZones.Add(new TimeZone(173340, 61620, 48.15, 1.71166666666666e+16, "Slovakia", "SK", "Europe/Bratislava", ""));
                timeZones.Add(new TimeZone(30600, -47700, 8.5, -13.25, "Sierra Leone", "SL", "Africa/Freetown", ""));
                timeZones.Add(new TimeZone(158100, 44880, 4.39166666666666e+16, 1.24666666666666e+16, "San Marino", "SM", "Europe/San_Marino", ""));
                timeZones.Add(new TimeZone(52800, -62760, 1.46666666666666e+16, -1.74333333333333e+16, "Senegal", "SN", "Africa/Dakar", ""));
                timeZones.Add(new TimeZone(7440, 163320, 2066666666666660.0, 4536666666666660.0, "Somalia", "SO", "Africa/Mogadishu", ""));
                timeZones.Add(new TimeZone(21000, -198600, 5833333333333330.0, -5.51666666666666e+16, "Suriname", "SR", "America/Paramaribo", ""));
                timeZones.Add(new TimeZone(17460, 113820, 4.85, 3.16166666666666e+16, "South Sudan", "SS", "Africa/Juba", ""));
                timeZones.Add(new TimeZone(1200, 24240, 0.3333333333333333, 6733333333333330.0, "Sao Tome & Principe", "ST", "Africa/Sao_Tome", ""));
                timeZones.Add(new TimeZone(49320, -321120, 13.7, -89.2, "El Salvador", "SV", "America/El_Salvador", ""));
                timeZones.Add(new TimeZone(64985, -226970, 1.80513888888888e+16, -6.30472222222222e+16, "St Maarten (Dutch)", "SX", "America/Lower_Princes", ""));
                timeZones.Add(new TimeZone(120600, 130680, 33.5, 36.3, "Syria", "SY", "Asia/Damascus", ""));
                timeZones.Add(new TimeZone(-94680, 111960, -26.3, 31.1, "Eswatini (Swaziland)", "SZ", "Africa/Mbabane", ""));
                timeZones.Add(new TimeZone(77280, -256080, 2.14666666666666e+16, -7113333333333330.0, "Turks & Caicos Is", "TC", "America/Grand_Turk", ""));
                timeZones.Add(new TimeZone(43620, 54180, 1.21166666666666e+16, 15.05, "Chad", "TD", "Africa/Ndjamena", ""));
                timeZones.Add(new TimeZone(-177670, 252783, -4.93527777777777e+16, 702175.0, "French S. Terr.", "TF", "Indian/Kerguelen", ""));
                timeZones.Add(new TimeZone(22080, 4380, 6133333333333330.0, 1.21666666666666e+16, "Togo", "TG", "Africa/Lome", ""));
                timeZones.Add(new TimeZone(49500, 361860, 13.75, 1.00516666666666e+16, "Thailand", "TH", "Asia/Bangkok", ""));
                timeZones.Add(new TimeZone(138900, 247680, 3.85833333333333e+16, 68.8, "Tajikistan", "TJ", "Asia/Dushanbe", ""));
                timeZones.Add(new TimeZone(-33720, -616440, -9366666666666660.0, -1.71233333333333e+16, "Tokelau", "TK", "Pacific/Fakaofo", ""));
                timeZones.Add(new TimeZone(-30780, 452100, -8.55, 1.25583333333333e+16, "East Timor", "TL", "Asia/Dili", ""));
                timeZones.Add(new TimeZone(136620, 210180, 37.95, 5838333333333330.0, "Turkmenistan", "TM", "Asia/Ashgabat", ""));
                timeZones.Add(new TimeZone(132480, 36660, 36.8, 1.01833333333333e+16, "Tunisia", "TN", "Africa/Tunis", ""));
                timeZones.Add(new TimeZone(-76080, -630720, -2.11333333333333e+16, -175.2, "Tonga", "TO", "Pacific/Tongatapu", ""));
                timeZones.Add(new TimeZone(147660, 104280, 4.10166666666666e+16, 2.89666666666666e+16, "Turkey", "TR", "Europe/Istanbul", ""));
                timeZones.Add(new TimeZone(38340, -221460, 10.65, -6.15166666666666e+16, "Trinidad & Tobago", "TT", "America/Port_of_Spain", ""));
                timeZones.Add(new TimeZone(-30660, 645180, -8516666666666660.0, 1.79216666666666e+16, "Tuvalu", "TV", "Pacific/Funafuti", ""));
                timeZones.Add(new TimeZone(90180, 437400, 25.05, 121.5, "Taiwan", "TW", "Asia/Taipei", ""));
                timeZones.Add(new TimeZone(-24480, 141420, -6.8, 3928333333333330.0, "Tanzania", "TZ", "Africa/Dar_es_Salaam", ""));
                timeZones.Add(new TimeZone(181560, 109860, 5043333333333330.0, 3.05166666666666e+16, "Ukraine", "UA", "Europe/Kyiv", "most of Ukraine"));
                timeZones.Add(new TimeZone(1140, 116700, 0.3166666666666666, 3.24166666666666e+16, "Uganda", "UG", "Africa/Kampala", ""));
                timeZones.Add(new TimeZone(101580, -638520, 2.82166666666666e+16, -1.77366666666666e+16, "US minor outlying islands", "UM", "Pacific/Midway", "Midway Islands"));
                timeZones.Add(new TimeZone(69420, 599820, 1.92833333333333e+16, 1.66616666666666e+16, "US minor outlying islands", "UM", "Pacific/Wake", "Wake Island"));
                timeZones.Add(new TimeZone(146571, -266423, 4.07141666666666e+16, -7400638888888880.0, "United States", "US", "America/New_York", "Eastern (most areas)"));
                timeZones.Add(new TimeZone(152393, -298965, 4233138888888880.0, -8304583333333330.0, "United States", "US", "America/Detroit", "Eastern - MI (most areas)"));
                timeZones.Add(new TimeZone(137715, -308734, 3825416666666660.0, -8575944444444440.0, "United States", "US", "America/Kentucky/Louisville", "Eastern - KY (Louisville area)"));
                timeZones.Add(new TimeZone(132587, -305457, 3682972222222220.0, -8484916666666660.0, "United States", "US", "America/Kentucky/Monticello", "Eastern - KY (Wayne)"));
                timeZones.Add(new TimeZone(143166, -310169, 3976833333333330.0, -8615805555555550.0, "United States", "US", "America/Indiana/Indianapolis", "Eastern - IN (most areas)"));
                timeZones.Add(new TimeZone(139238, -315103, 3867722222222220.0, -875286111111111.0, "United States", "US", "America/Indiana/Vincennes", "Eastern - IN (Da, Du, K, Mn)"));
                timeZones.Add(new TimeZone(147785, -311771, 4105138888888880.0, -8660305555555550.0, "United States", "US", "America/Indiana/Winamac", "Eastern - IN (Pulaski)"));
                timeZones.Add(new TimeZone(138152, -310841, 3837555555555550.0, -8634472222222220.0, "United States", "US", "America/Indiana/Marengo", "Eastern - IN (Crawford)"));
                timeZones.Add(new TimeZone(138571, -314203, 3849194444444440.0, -872786111111111.0, "United States", "US", "America/Indiana/Petersburg", "Eastern - IN (Pike)"));
                timeZones.Add(new TimeZone(139492, -306242, 3874777777777770.0, -8506722222222220.0, "United States", "US", "America/Indiana/Vevay", "Eastern - IN (Switzerland)"));
                timeZones.Add(new TimeZone(150660, -315540, 41.85, -87.65, "United States", "US", "America/Chicago", "Central (most areas)"));
                timeZones.Add(new TimeZone(136631, -312341, 3795305555555550.0, -8676138888888880.0, "United States", "US", "America/Indiana/Tell_City", "Central - IN (Perry)"));
                timeZones.Add(new TimeZone(148665, -311850, 4.12958333333333e+16, -86625.0, "United States", "US", "America/Indiana/Knox", "Central - IN (Starke)"));
                timeZones.Add(new TimeZone(162388, -315411, 4510777777777770.0, -8761416666666660.0, "United States", "US", "America/Menominee", "Central - MI (Wisconsin border)"));
                timeZones.Add(new TimeZone(169619, -364677, 4711638888888880.0, -1.01299166666666e+16, "United States", "US", "America/North_Dakota/Center", "Central - ND (Oliver)"));
                timeZones.Add(new TimeZone(168642, -365079, 46845.0, -1.01410833333333e+16, "United States", "US", "America/North_Dakota/New_Salem", "Central - ND (Morton rural)"));
                timeZones.Add(new TimeZone(170151, -366400, 4726416666666660.0, -1.01777777777777e+16, "United States", "US", "America/North_Dakota/Beulah", "Central - ND (Mercer)"));
                timeZones.Add(new TimeZone(143061, -377943, 3973916666666660.0, -1.04984166666666e+16, "United States", "US", "America/Denver", "Mountain (most areas)"));
                timeZones.Add(new TimeZone(157009, -418329, 4361361111111110.0, -1162025.0, "United States", "US", "America/Boise", "Mountain - ID (south), OR (east)"));
                timeZones.Add(new TimeZone(120414, -403464, 3344833333333330.0, -1.12073333333333e+16, "United States", "US", "America/Phoenix", "MST - AZ (except Navajo)"));
                timeZones.Add(new TimeZone(122588, -425674, 3405222222222220.0, -1.18242777777777e+16, "United States", "US", "America/Los_Angeles", "Pacific"));
                timeZones.Add(new TimeZone(220385, -539641, 6121805555555550.0, -1.49900277777777e+16, "United States", "US", "America/Anchorage", "Alaska (most areas)"));
                timeZones.Add(new TimeZone(209887, -483911, 5.83019444444444e+16, -1.34419722222222e+16, "United States", "US", "America/Juneau", "Alaska - Juneau area"));
                timeZones.Add(new TimeZone(205835, -487087, 5717638888888880.0, -1.35301944444444e+16, "United States", "US", "America/Sitka", "Alaska - Sitka area"));
                timeZones.Add(new TimeZone(198457, -473675, 5512694444444440.0, -1.31576388888888e+16, "United States", "US", "America/Metlakatla", "Alaska - Annette Island"));
                timeZones.Add(new TimeZone(214369, -503018, 5954694444444440.0, -1.39727222222222e+16, "United States", "US", "America/Yakutat", "Alaska - Yakutat"));
                timeZones.Add(new TimeZone(232204, -595463, 6450111111111110.0, -1654063888888880.0, "United States", "US", "America/Nome", "Alaska (west)"));
                timeZones.Add(new TimeZone(186768, -635969, 51.88, -1.76658055555555e+16, "United States", "US", "America/Adak", "Alaska - western Aleutians"));
                timeZones.Add(new TimeZone(76705, -568290, 2.13069444444444e+16, -1.57858333333333e+16, "United States", "US", "Pacific/Honolulu", "Hawaii"));
                timeZones.Add(new TimeZone(-125673, -202365, -3.49091666666666e+16, -562125.0, "Uruguay", "UY", "America/Montevideo", ""));
                timeZones.Add(new TimeZone(142800, 240480, 3.96666666666666e+16, 66.8, "Uzbekistan", "UZ", "Asia/Samarkand", "Uzbekistan (west)"));
                timeZones.Add(new TimeZone(148800, 249480, 4.13333333333333e+16, 69.3, "Uzbekistan", "UZ", "Asia/Tashkent", "Uzbekistan (east)"));
                timeZones.Add(new TimeZone(150848, 44831, 4190222222222220.0, 1.24530555555555e+16, "Vatican City", "VA", "Europe/Vatican", ""));
                timeZones.Add(new TimeZone(47340, -220440, 13.15, -6.12333333333333e+16, "St Vincent", "VC", "America/St_Vincent", ""));
                timeZones.Add(new TimeZone(37800, -240960, 10.5, -6693333333333330.0, "Venezuela", "VE", "America/Caracas", ""));
                timeZones.Add(new TimeZone(66420, -232620, 18.45, -6461666666666660.0, "Virgin Islands (UK)", "VG", "America/Tortola", ""));
                timeZones.Add(new TimeZone(66060, -233760, 18.35, -6493333333333330.0, "Virgin Islands (US)", "VI", "America/St_Thomas", ""));
                timeZones.Add(new TimeZone(38700, 384000, 10.75, 1.06666666666666e+16, "Vietnam", "VN", "Asia/Ho_Chi_Minh", ""));
                timeZones.Add(new TimeZone(-63600, 606300, -1.76666666666666e+16, 1.68416666666666e+16, "Vanuatu", "VU", "Pacific/Efate", ""));
                timeZones.Add(new TimeZone(-47880, -634200, -13.3, -1.76166666666666e+16, "Wallis & Futuna", "WF", "Pacific/Wallis", ""));
                timeZones.Add(new TimeZone(-49800, -618240, -1.38333333333333e+16, -1.71733333333333e+16, "Samoa (western)", "WS", "Pacific/Apia", ""));
                timeZones.Add(new TimeZone(45900, 162720, 12.75, 45.2, "Yemen", "YE", "Asia/Aden", ""));
                timeZones.Add(new TimeZone(-46020, 162840, -1.27833333333333e+16, 4.52333333333333e+16, "Mayotte", "YT", "Indian/Mayotte", ""));
                timeZones.Add(new TimeZone(-94500, 100800, -26.25, 28.0, "South Africa", "ZA", "Africa/Johannesburg", ""));
                timeZones.Add(new TimeZone(-55500, 101820, -1.54166666666666e+16, 2.82833333333333e+16, "Zambia", "ZM", "Africa/Lusaka", ""));
                timeZones.Add(new TimeZone(-64200, 111780, -1.78333333333333e+16, 31.05, "Zimbabwe", "ZW", "Africa/Harare", ""));

                return timeZones;
            }
        }
    }
}
