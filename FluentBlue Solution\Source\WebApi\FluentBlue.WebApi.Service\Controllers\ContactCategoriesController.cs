﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Shared;
using FluentBlue.WebApi.Service.Resources;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class ContactCategoriesController : ControllerBase
    {
        private IContactCategoriesBusiness contactCategoriesBusiness;
        private ILogger<ContactCategoriesController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public ContactCategoriesController(IContactCategoriesBusiness contactCategoriesBusiness, ILogger<ContactCategoriesController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            try
            {
                this.contactCategoriesBusiness = contactCategoriesBusiness;
                this.logger = logger;
                this.mapper = mapper;
                this.webApiCallerInfo = webApiCallerInfo;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        /// <summary>
        /// Retrieves all ContactCategories.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("ContactCategories/GetAll")]
        public async Task<ApiResponse<List<Data.Model.DBOs.Contacts.ContactCategory>>> GetAllContactCategories()
        {
            try
            {
                //Validation

                //Query
                List<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory> data = await this.contactCategoriesBusiness.GetContactCategories(this.webApiCallerInfo.TenantId!.Value);
                //Thread.Sleep(14000);
                //Response
                return new ApiResponse<List<Data.Model.DBOs.Contacts.ContactCategory>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<Data.Model.DBOs.Contacts.ContactCategory>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(ex);
                return new ApiResponse<List<Data.Model.DBOs.Contacts.ContactCategory>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }


        [HttpPost]
        [Route("ContactCategory/CreateOrUpdate")]
        public async Task<ApiResponse<ContactCategory>> CreateOrUpdateContact([FromBody] ContactCategory contactObj)
        {
            try
            {
                //Validation
                if (contactObj == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Query
                await this.contactCategoriesBusiness.CreateOrUpdateContactCategory(contactObj);

                //Response
                ContactCategory? contactCategory = await this.contactCategoriesBusiness.GetContactCategory(contactObj.ContactCategoryId);
                return new ApiResponse<ContactCategory>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = contactCategory };
            }
            catch (ApplicationException ex)
            {
                this.logger.LogError(ex, ex.Message, contactObj);  //TODO: Κανονικά τα ApplicationExceptions δεν πρέπει να γίνονται log γιατί είναι αναμενόμενα από το πρόγραμμα για το χρήστη.
                return new ApiResponse<ContactCategory>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, contactObj);
                return new ApiResponse<ContactCategory>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("ContactCategory/Delete")]
        public async Task<ApiResponse> DeleteContactCategory([FromQuery] Guid contactCategoryId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                await this.contactCategoriesBusiness.DeleteContactCategory(contactCategoryId);

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<ContactCategory>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "ContactCategoryId=" + contactCategoryId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}