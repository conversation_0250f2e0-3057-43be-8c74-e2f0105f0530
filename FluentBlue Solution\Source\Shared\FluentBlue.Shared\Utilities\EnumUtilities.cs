﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Utilities
{
    public static class Extensions
    {
        /// <summary>
        /// Returns the description for a given enum, bazed on DescriptionAttribute.
        /// </summary>
        /// <param name="enumValue"></param>
        /// <returns></returns>
        static public string GetEnumDescription(this Enum enumValue)
        {
            var field = enumValue.GetType().GetField(enumValue.ToString());
            if (field == null)
                return enumValue.ToString();

            var attributes = field.GetCustomAttributes(typeof(DescriptionAttribute), false);
            if (Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) is DescriptionAttribute attribute)
            {
                return attribute.Description;
            }

            return enumValue.ToString();
        }


        /// <summary>
        /// Returns the localized text for a given enum, bazed on DisplayAttribute.
        /// </summary>
        public static string GetDisplayText(this Enum enumValue)
        {
            var fi = enumValue.GetType().GetField(enumValue.ToString());

            var attributes = (DisplayAttribute[])fi.GetCustomAttributes(typeof(DisplayAttribute), false);

            string name = attributes[0].Name;
            string resType = attributes[0].ResourceType.ToString();
            ResourceManager resources = new ResourceManager(resType, Assembly.GetAssembly(enumValue.GetType()));
            string? text = resources.GetString(name, CultureInfo.CurrentCulture);

            return text;
        }

    }
}
