﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.CompilerServices;
using Microsoft.Extensions.Options;
using FluentBlue.Data.Model.DBOs.Calendar;

namespace FluentBlue.Data.Model.DBOs.Contacts
{
    [Table("ContactEmail", Schema = "Contacts")]
    public class ContactEmail : IObjectState, INotifyPropertyChanged
    {
        private Guid contactEmailId;
        private EmailType? type;
        private string emailAddress = string.Empty;
        private bool notifyPropertyChangedEnabled = false;

        public ContactEmail()
        {
            contactEmailId = Guid.CreateVersion7();
            type = EmailType.Personal;
            EmailAddress = string.Empty;
            RowVersion = new byte[0];
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactEmailResource), Name = "ContactEmailId")]
        public Guid ContactEmailId
        {
            get
            {
                return contactEmailId;
            }
            set
            {
                contactEmailId = value;
            }
        }

        //[Required]
        public Guid ContactId { get; set; }

        [ForeignKey("ContactId")]
        public Contact? Contact { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactEmailResource), Name = "Type")]
        [EnumDataType(typeof(EmailType))]
        public EmailType? Type
        {
            get
            {
                return type;
            }
            set
            {
                if (type != value)
                {
                    type = value;
                    NotifyPropertyChanged();
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactEmailResource), Name = "EmailAddress")]
        public string EmailAddress
        {
            get
            {
                return emailAddress;
            }
            set
            {
                if (emailAddress != value)
                {
                    emailAddress = value ?? "";
                    NotifyPropertyChanged();
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateCreated")]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateCreatedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        //[Column(TypeName = "datetime2")]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateModified")]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        //[NotMapped]
        //public string? TypeStr
        //{
        //    get
        //    {
        //        return type.ToString();
        //    }
        //    set
        //    {
        //        List<string> types = Enum.GetValues(typeof(Data.Model.EmailType)).Cast<Data.Model.EmailType?>().Select(x => x!.Value.ToString()).ToList();
        //        if (types.Contains(value ?? ""))
        //        {
        //            Type = (Data.Model.EmailType)Enum.Parse(typeof(Data.Model.EmailType), value ?? "");
        //        }
        //        else
        //        {
        //            throw new Exception("Wrong enum string in ContactEmail.TypeStr");
        //        }
        //    }
        //}

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        [NotMapped]
        internal bool NotifyPropertyChangedEnabled
        {
            get
            {
                return this.notifyPropertyChangedEnabled;
            }
            set
            {
                this.notifyPropertyChangedEnabled = value;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (notifyPropertyChangedEnabled == true)
            {
                //DateModifiedUtc = DateTime.UtcNow;
                if (ObjectState == ObjectState.Unchanged)
                {
                    ObjectState = ObjectState.Modified;  //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
                }

                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
            }
        }
    }
}
