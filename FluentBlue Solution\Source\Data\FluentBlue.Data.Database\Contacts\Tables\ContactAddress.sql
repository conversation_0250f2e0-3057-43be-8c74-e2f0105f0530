﻿CREATE TABLE [Contacts].[ContactAddress] (
    [ContactAddressId] UNIQUEIDENTIFIER CONSTRAINT [DF_ContactAddress_ContactAddressId] DEFAULT (newsequentialid()) NOT NULL,
    [ContactId]        UNIQUEIDENTIFIER NOT NULL,
    [Type]             TINYINT          NULL,
    [Address]          NVARCHAR (250)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_ContactAddress_Address] DEFAULT (N'') NOT NULL,
    [DateCreatedUtc]   DATETIME         CONSTRAINT [DF_ContactAddress_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc]  DATETIME         CONSTRAINT [DF_ContactAddress_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]       ROWVERSION       NULL,
    CONSTRAINT [PK_ContactAddress] PRIMARY KEY CLUSTERED ([ContactAddressId] ASC),
    CONSTRAINT [FK_ContactAddress_Contact] FOREIGN KEY ([ContactId]) REFERENCES [Contacts].[Contact] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE
);












GO



GO
CREATE NONCLUSTERED INDEX [ContactIdIndex]
    ON [Contacts].[ContactAddress]([ContactId] ASC);

