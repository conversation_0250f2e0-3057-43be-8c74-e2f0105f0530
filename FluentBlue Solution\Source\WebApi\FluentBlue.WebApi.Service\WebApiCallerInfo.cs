﻿using FluentBlue.Application.Business;
using FluentBlue.Data.Model.DBOs.Tenants;

namespace FluentBlue.WebApi.Service
{
    public class WebApiCallerInfo : IWebApiCallerInfo
    {
        private readonly ITenantsBusiness tenantsBusiness;
        private readonly IUsersBusiness usersBusiness;
        public Guid? TenantId { get; private set; }
        public Guid? UserId { get; private set; }
        public Guid? ApplicationId { get; set; }

        public WebApiCallerInfo(ITenantsBusiness tenantsBusiness, IUsersBusiness usersBusiness)
        {
            this.tenantsBusiness = tenantsBusiness;
            this.usersBusiness = usersBusiness;
        }

        public async Task<bool> ValidateCallerInfo(Guid tenantId, Guid userId)
        {
            User? user = await this.usersBusiness.GetUser(userId);  //Αν ο χρήστης υπάρχει
            //TODO: να μπει εδώ έλεγχος αν η συνδρομή έχει λήξει, επειδή μπορεί το token να κρατάει πολύ καιρό.
            //TODO: να μπει HybridCaching
            if (user != null && user.TenantId == tenantId)
            {
                TenantId = user.TenantId;
                UserId = user.UserId;
                return true;
            }
            else
            {
                throw new Exception("Tenant invalid");
            }
        }
    }
}
