﻿CREATE TABLE [Tenants].[User] (
    [UserId]                UNIQUEIDENTIFIER CONSTRAINT [DF_User_UserId1] DEFAULT (newsequentialid()) NOT NULL,
    [RoleId]                UNIQUEIDENTIFIER NOT NULL,
    [TenantId]              UNIQUEIDENTIFIER NOT NULL,
    [FirstName]             NVARCHAR (100)   CONSTRAINT [DF_User_FirstName] DEFAULT (N'') NOT NULL,
    [LastName]              NVARCHAR (100)   CONSTRAINT [DF_User_LastName] DEFAULT (N'') NOT NULL,
    [MiddleName]            NVARCHAR (100)   CONSTRAINT [DF_User_MiddleName] DEFAULT (N'') NOT NULL,
    [Email]                 NVARCHAR (50)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_User_FullName1] DEFAULT (N'') NOT NULL,
    [Username]              NVARCHAR (30)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_User_Username] DEFAULT (N'') NOT NULL,
    [Password]              NVARCHAR (30)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_User_Password] DEFAULT (N'') NOT NULL,
    [Phone]                 NVARCHAR (25)    CONSTRAINT [DF_User_Phone] DEFAULT (N'') NOT NULL,
    [Mobile]                NVARCHAR (25)    CONSTRAINT [DF_User_Mobile] DEFAULT (N'') NOT NULL,
    [Image]                 NVARCHAR (MAX)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_User_Image] DEFAULT (N'') NOT NULL,
    [AvailableInEventUsers] BIT              CONSTRAINT [DF_User_AvailableInEventUsers] DEFAULT ((1)) NOT NULL,
    [DateCreatedUtc]        DATETIME         CONSTRAINT [DF_User_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc]       DATETIME         CONSTRAINT [DF_User_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]            ROWVERSION       NULL,
    CONSTRAINT [PK_User_1] PRIMARY KEY CLUSTERED ([UserId] ASC),
    CONSTRAINT [FK_User_Role] FOREIGN KEY ([RoleId]) REFERENCES [Tenants].[Role] ([RoleId]) ON DELETE CASCADE ON UPDATE CASCADE
);








GO



GO
CREATE NONCLUSTERED INDEX [UsernamePasswordIndex]
    ON [Tenants].[User]([Username] ASC, [Password] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueUsernameIndex]
    ON [Tenants].[User]([Username] ASC);


GO
CREATE NONCLUSTERED INDEX [UniqueUser]
    ON [Tenants].[User]([TenantId] ASC, [Email] ASC);

