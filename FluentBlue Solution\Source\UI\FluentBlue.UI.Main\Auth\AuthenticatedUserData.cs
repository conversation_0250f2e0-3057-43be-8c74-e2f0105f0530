﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Auth
{
    internal static class AuthenticatedUserData
    {
        public static Guid UserId { get; set; }
        public static string UserName { get; set; } = string.Empty;
        public static string FullName { get; set; } = string.Empty;
        public static string Email { get; set; } = string.Empty;
        public static string RoleName { get; set; } = string.Empty;
        public static Guid TenantId { get; set; }

        internal static void RetrieveDataFromClaims(Dictionary<string, Claim> claims)
        {
            AuthenticatedUserData.UserId = Guid.Parse(claims.Where(kv => kv.Key.Contains("UserId")).First().Value.Value);  //await this.localStorage.SetItemAsStringAsync(this.userIdKey, claimsDictionary.Where(kv => kv.Key.Contains("UserId")).First().Value.Value.ToString());
            AuthenticatedUserData.UserName = claims.Where(kv => kv.Key.Contains("Username")).First().Value.Value;  //await this.localStorage.SetItemAsStringAsync(this.userUsernameKey, claimsDictionary.Where(kv => kv.Key.Contains("Username")).First().Value.Value.ToString());
            AuthenticatedUserData.FullName = claims.Where(kv => kv.Key.Contains("/name")).First().Value.Value;  //await this.localStorage.SetItemAsStringAsync(this.userFullNameKey, claimsDictionary.Where(kv => kv.Key.Contains("/name")).First().Value.Value.ToString()); 
            AuthenticatedUserData.RoleName = claims.Where(kv => kv.Key.Contains("/role")).First().Value.Value;  //await this.localStorage.SetItemAsStringAsync(this.roleName, claimsDictionary.Where(kv => kv.Key.Contains("/role")).First().Value.Value.ToString());
            AuthenticatedUserData.TenantId = Guid.Parse(claims.Where(kv => kv.Key.Contains("TenantId")).First().Value.Value);  //await this.localStorage.SetItemAsStringAsync(this.tenantIdKey, claimsDictionary.Where(kv => kv.Key.Contains("TenantId")).First().Value.Value.ToString());  
        }
    }
}
