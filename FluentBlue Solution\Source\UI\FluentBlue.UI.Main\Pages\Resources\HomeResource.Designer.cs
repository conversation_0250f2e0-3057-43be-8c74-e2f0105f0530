﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Pages.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class HomeResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal HomeResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Pages.Resources.HomeResource", typeof(HomeResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string ContactsCount {
            get {
                return ResourceManager.GetString("ContactsCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total contacts.
        /// </summary>
        internal static string ContactsCountDescription {
            get {
                return ResourceManager.GetString("ContactsCountDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Future Events.
        /// </summary>
        internal static string FutureEventsCount {
            get {
                return ResourceManager.GetString("FutureEventsCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upcoming future events.
        /// </summary>
        internal static string FutureEventsCountDescription {
            get {
                return ResourceManager.GetString("FutureEventsCountDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events Today.
        /// </summary>
        internal static string TodayEventsCount {
            get {
                return ResourceManager.GetString("TodayEventsCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events that happen today.
        /// </summary>
        internal static string TodayEventsCountDescription {
            get {
                return ResourceManager.GetString("TodayEventsCountDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events Tomorrow.
        /// </summary>
        internal static string TomorrowEventsCount {
            get {
                return ResourceManager.GetString("TomorrowEventsCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events that will happen tomorrow.
        /// </summary>
        internal static string TomorrowEventsCountDescription {
            get {
                return ResourceManager.GetString("TomorrowEventsCountDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        internal static string UsersCount {
            get {
                return ResourceManager.GetString("UsersCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total users.
        /// </summary>
        internal static string UsersCountDescription {
            get {
                return ResourceManager.GetString("UsersCountDescription", resourceCulture);
            }
        }
    }
}
