﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllDay" xml:space="preserve">
    <value>Ολοήμερο</value>
  </data>
  <data name="Block" xml:space="preserve">
    <value>Εμποδίζει άλλα συμβάντα</value>
  </data>
  <data name="ContactId" xml:space="preserve">
    <value>Επαφή</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Δημιουργήθηκε</value>
  </data>
  <data name="DateModified" xml:space="preserve">
    <value>Τροποποιήθηκε</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Περιγραφή</value>
  </data>
  <data name="DescriptionMaxLengthError" xml:space="preserve">
    <value>Πολλοί χαρακτήρες (όριο 2000 χαρακτήρων)</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>Λήξη</value>
  </data>
  <data name="EndTimeMustBeAfterStartTime" xml:space="preserve">
    <value>Η λήξη πρέπει να είναι μετά την έναρξη.</value>
  </data>
  <data name="EndTimeZone" xml:space="preserve">
    <value>Ζώνη Ώρας Λήξης</value>
  </data>
  <data name="EventCategoryId" xml:space="preserve">
    <value>Κατηγορία</value>
  </data>
  <data name="EventId" xml:space="preserve">
    <value>Κωδικός</value>
  </data>
  <data name="EventStateId" xml:space="preserve">
    <value>Κατάσταση</value>
  </data>
  <data name="EventUsers" xml:space="preserve">
    <value>Χρήστης</value>
  </data>
  <data name="FifteenMinReminder" xml:space="preserve">
    <value>15 λεπτά πριν</value>
  </data>
  <data name="FiveMinReminder" xml:space="preserve">
    <value>5 λεπτά πριν</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Τοποθεσία</value>
  </data>
  <data name="NoReminder" xml:space="preserve">
    <value>Χωρίς υπενθύμιση</value>
  </data>
  <data name="OneHourReminder" xml:space="preserve">
    <value>1 ώρα πριν</value>
  </data>
  <data name="PlainDescription" xml:space="preserve">
    <value>Περιγραφή</value>
  </data>
  <data name="ReadOnly" xml:space="preserve">
    <value>Μόνο για Ανάγνωση</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Έναρξη</value>
  </data>
  <data name="StartTimeZone" xml:space="preserve">
    <value>Ζώνη Ώρας Έναρξης</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Θέμα</value>
  </data>
  <data name="SubjectMaxLengthError" xml:space="preserve">
    <value>Πολλοί χαρακτήρες (όριο 300 χαρακτήρων)</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Περίληψη</value>
  </data>
  <data name="TenMinReminder" xml:space="preserve">
    <value>10 λεπτά πριν</value>
  </data>
  <data name="ThirtyMinReminder" xml:space="preserve">
    <value>30 λεπτά πριν</value>
  </data>
  <data name="ZeroMinReminder" xml:space="preserve">
    <value>Την ώρα του συμβάντος</value>
  </data>
</root>