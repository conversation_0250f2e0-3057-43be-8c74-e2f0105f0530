﻿CREATE TABLE [Calendar].[EventCategory] (
    [EventCategoryId] UNIQUEIDENTIFIER CONSTRAINT [DF_EventCategory_EventCategoryId] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]        UNIQUEIDENTIFIER NOT NULL,
    [Name]            NVARCHAR (50)    CONSTRAINT [DF_EventCategories_Title] DEFAULT ('') NOT NULL,
    [BackColor]       NVARCHAR (10)    CONSTRAINT [DF_EventCategories_BackgroundColor] DEFAULT ('') NOT NULL,
    [SortIndex]       INT              NULL,
    [Disabled]        BIT              CONSTRAINT [DF_EventCategory_SoftDeleted] DEFAULT ((0)) NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_EventCategories_DateCreated] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_EventCategories_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_EventCategory] PRIMARY KEY CLUSTERED ([EventCategoryId] ASC),
    CONSTRAINT [FK_EventCategory_Tenant] FOREIGN KEY ([TenantId]) REFERENCES [Tenants].[Tenant] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);








GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueName]
    ON [Calendar].[EventCategory]([TenantId] ASC, [Name] ASC);


GO
CREATE NONCLUSTERED INDEX [TenantIdIndex]
    ON [Calendar].[EventCategory]([TenantId] ASC);

