//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources.Settings {
    using System;
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class GeneralSettingResource_el {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal GeneralSettingResource_el() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.Settings.GeneralSettingResource.el", typeof(GeneralSettingResource_el).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ατζέντα.
        /// </summary>
        public static string CalendarAgendaView {
            get {
                return ResourceManager.GetString("CalendarAgendaView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ημέρα.
        /// </summary>
        public static string CalendarDayView {
            get {
                return ResourceManager.GetString("CalendarDayView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Προεπιλεγμένη προβολή.
        /// </summary>
        public static string CalendarDefaultView {
            get {
                return ResourceManager.GetString("CalendarDefaultView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Προεπιλεγμένη προβολή σε κινητά.
        /// </summary>
        public static string CalendarDefaultViewOnMobiles {
            get {
                return ResourceManager.GetString("CalendarDefaultViewOnMobiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μήνας.
        /// </summary>
        public static string CalendarMonthView {
            get {
                return ResourceManager.GetString("CalendarMonthView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 08:00.
        /// </summary>
        public static string CalendarScrollToEight {
            get {
                return ResourceManager.GetString("CalendarScrollToEight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 05:00.
        /// </summary>
        public static string CalendarScrollToFive {
            get {
                return ResourceManager.GetString("CalendarScrollToFive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 04:00.
        /// </summary>
        public static string CalendarScrollToFour {
            get {
                return ResourceManager.GetString("CalendarScrollToFour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 09:00.
        /// </summary>
        public static string CalendarScrollToNine {
            get {
                return ResourceManager.GetString("CalendarScrollToNine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Χωρίς κύλιση.
        /// </summary>
        public static string CalendarScrollToNone {
            get {
                return ResourceManager.GetString("CalendarScrollToNone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 01:00.
        /// </summary>
        public static string CalendarScrollToOne {
            get {
                return ResourceManager.GetString("CalendarScrollToOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 07:00.
        /// </summary>
        public static string CalendarScrollToSeven {
            get {
                return ResourceManager.GetString("CalendarScrollToSeven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 06:00.
        /// </summary>
        public static string CalendarScrollToSix {
            get {
                return ResourceManager.GetString("CalendarScrollToSix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10:00.
        /// </summary>
        public static string CalendarScrollToTen {
            get {
                return ResourceManager.GetString("CalendarScrollToTen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 03:00.
        /// </summary>
        public static string CalendarScrollToThree {
            get {
                return ResourceManager.GetString("CalendarScrollToThree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κύλιση στην ώρα.
        /// </summary>
        public static string CalendarScrollToTime {
            get {
                return ResourceManager.GetString("CalendarScrollToTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 02:00.
        /// </summary>
        public static string CalendarScrollToTwo {
            get {
                return ResourceManager.GetString("CalendarScrollToTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Χρονικό διάστημα.
        /// </summary>
        public static string CalendarTimeScaleInterval {
            get {
                return ResourceManager.GetString("CalendarTimeScaleInterval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εβδομάδα.
        /// </summary>
        public static string CalendarWeekView {
            get {
                return ResourceManager.GetString("CalendarWeekView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εργάσιμες ημέρες.
        /// </summary>
        public static string CalendarWorkDays {
            get {
                return ResourceManager.GetString("CalendarWorkDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ώρα λήξης.
        /// </summary>
        public static string CalendarWorkEnd {
            get {
                return ResourceManager.GetString("CalendarWorkEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ώρα έναρξης.
        /// </summary>
        public static string CalendarWorkStart {
            get {
                return ResourceManager.GetString("CalendarWorkStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εργάσιμη εβδομάδα.
        /// </summary>
        public static string CalendarWorkWeekView {
            get {
                return ResourceManager.GetString("CalendarWorkWeekView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Χρώμα.
        /// </summary>
        public static string Color {
            get {
                return ResourceManager.GetString("Color", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Λειτουργία χρώματος.
        /// </summary>
        public static string ColorMode {
            get {
                return ResourceManager.GetString("ColorMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τοπικές Ρυθμίσεις.
        /// </summary>
        public static string CultureField {
            get {
                return ResourceManager.GetString("CultureField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μορφή ημερομηνίας.
        /// </summary>
        public static string DateFormat {
            get {
                return ResourceManager.GetString("DateFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15 λεπτά.
        /// </summary>
        public static string FifteenMinutes {
            get {
                return ResourceManager.GetString("FifteenMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πρώτη ημέρα της εβδομάδας.
        /// </summary>
        public static string FirstDayOfWeek {
            get {
                return ResourceManager.GetString("FirstDayOfWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5 λεπτά.
        /// </summary>
        public static string FiveMinutes {
            get {
                return ResourceManager.GetString("FiveMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Γλώσσα.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 60 λεπτά.
        /// </summary>
        public static string SixtyMinutes {
            get {
                return ResourceManager.GetString("SixtyMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10 λεπτά.
        /// </summary>
        public static string TenMinutes {
            get {
                return ResourceManager.GetString("TenMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 λεπτά.
        /// </summary>
        public static string ThirtyMinutes {
            get {
                return ResourceManager.GetString("ThirtyMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μορφή ώρας.
        /// </summary>
        public static string TimeFormat {
            get {
                return ResourceManager.GetString("TimeFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ζώνη ώρας.
        /// </summary>
        public static string TimeZone {
            get {
                return ResourceManager.GetString("TimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 20 λεπτά.
        /// </summary>
        public static string TwentyMinutes {
            get {
                return ResourceManager.GetString("TwentyMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αναγνωριστικό Ρυθμίσεων Χρήστη.
        /// </summary>
        public static string UserSettingId {
            get {
                return ResourceManager.GetString("UserSettingId", resourceCulture);
            }
        }
    }
} 