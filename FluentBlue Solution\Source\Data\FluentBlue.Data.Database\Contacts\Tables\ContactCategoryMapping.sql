﻿CREATE TABLE [Contacts].[ContactCategoryMapping] (
    [ContactCategoryMappingId] UNIQUEIDENTIFIER CONSTRAINT [DF_Table_1_ContactId1] DEFAULT (newsequentialid()) NOT NULL,
    [ContactId]                UNIQUEIDENTIFIER NOT NULL,
    [ContactCategoryId]        UNIQUEIDENTIFIER NOT NULL,
    [DateCreatedUtc]           DATETIME         CONSTRAINT [DF_ContactCategoryMapping_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    CONSTRAINT [PK_ContactCategoryMapping] PRIMARY KEY CLUSTERED ([ContactCategoryMappingId] ASC),
    CONSTRAINT [FK_ContactCategoryMapping_Contact] FOREIGN KEY ([ContactId]) REFERENCES [Contacts].[Contact] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT [FK_ContactCategoryMapping_ContactCategory] FOREIGN KEY ([ContactCategoryId]) REFERENCES [Contacts].[ContactCategory] ([ContactCategoryId]) ON DELETE CASCADE ON UPDATE CASCADE
);


GO
CREATE NONCLUSTERED INDEX [ContactIdIndex]
    ON [Contacts].[ContactCategoryMapping]([ContactId] ASC);

