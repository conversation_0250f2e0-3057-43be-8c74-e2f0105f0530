﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AutoComplete_ActionFailureTemplate" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="AutoComplete_NoRecordsTemplate" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="Calendar_Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="ComboBox_ActionFailureTemplate" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="ComboBox_NoRecordsTemplate" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="DatePicker_Placeholder" xml:space="preserve">
    <value>Choose a date</value>
  </data>
  <data name="DatePicker_Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="DateRangePicker_ApplyText" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="DateRangePicker_CancelText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DateRangePicker_CustomRange" xml:space="preserve">
    <value>Custom Range</value>
  </data>
  <data name="DateRangePicker_Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="DateRangePicker_EndLabel" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="DateRangePicker_Placeholder" xml:space="preserve">
    <value>Choose a date range</value>
  </data>
  <data name="DateRangePicker_SelectedDays" xml:space="preserve">
    <value>Selected Days</value>
  </data>
  <data name="DateRangePicker_StartLabel" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="DateTimePicker_Placeholder" xml:space="preserve">
    <value>Choose a date and time</value>
  </data>
  <data name="DateTimePicker_Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="Date_Day" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="Date_Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="Date_Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="Time_Hour" xml:space="preserve">
    <value>Hour</value>
  </data>
  <data name="Time_Minute" xml:space="preserve">
    <value>Minute</value>
  </data>
  <data name="Time_Second" xml:space="preserve">
    <value>Second</value>
  </data>
  <data name="Date_DayOfWeek" xml:space="preserve">
    <value>DayOfWeek</value>
  </data>
  <data name="DropDownList_ActionFailureTemplate" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="DropDownList_NoRecordsTemplate" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="DropDownTree_NoRecords" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="DropDownTree_RequestFailed" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="DropDownTree_SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="DropDownTree_UnSelectAll" xml:space="preserve">
    <value>Unselect All</value>
  </data>
  <data name="Mention_NoRecordsTemplate" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="MultiSelect_ActionFailureTemplate" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="MultiSelect_NoRecordsTemplate" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="MultiSelect_OverflowCountTemplate" xml:space="preserve">
    <value>+${count} more..</value>
  </data>
  <data name="MultiSelect_SelectAllText" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="MultiSelect_TotalCountTemplate" xml:space="preserve">
    <value>${count} selected</value>
  </data>
  <data name="MultiSelect_UnSelectAllText" xml:space="preserve">
    <value>Unselect All</value>
  </data>
  <data name="NumericTextBox_DecrementTitle" xml:space="preserve">
    <value>Decrement value</value>
  </data>
  <data name="NumericTextBox_IncrementTitle" xml:space="preserve">
    <value>Increment value</value>
  </data>
  <data name="TimePicker_Placeholder" xml:space="preserve">
    <value>Choose a time</value>
  </data>
  <data name="Uploader_Abort" xml:space="preserve">
    <value>Abort</value>
  </data>
  <data name="Uploader_Browse" xml:space="preserve">
    <value>Browse...</value>
  </data>
  <data name="Uploader_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Uploader_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Uploader_Delete" xml:space="preserve">
    <value>Delete file</value>
  </data>
  <data name="Uploader_DropFilesHint" xml:space="preserve">
    <value>Or drop files here</value>
  </data>
  <data name="Uploader_FileUploadCancel" xml:space="preserve">
    <value>File upload canceled</value>
  </data>
  <data name="Uploader_InProgress" xml:space="preserve">
    <value>Uploading</value>
  </data>
  <data name="Uploader_InvalidFileType" xml:space="preserve">
    <value>File type is not allowed</value>
  </data>
  <data name="Uploader_InvalidFileName" xml:space="preserve">
    <value>File name is not allowed</value>
  </data>
  <data name="Uploader_InvalidMaxFileSize" xml:space="preserve">
    <value>File size is too large</value>
  </data>
  <data name="Uploader_InvalidMinFileSize" xml:space="preserve">
    <value>File size is too small</value>
  </data>
  <data name="Uploader_Pause" xml:space="preserve">
    <value>Pause</value>
  </data>
  <data name="Uploader_PauseUpload" xml:space="preserve">
    <value>File upload paused</value>
  </data>
  <data name="Uploader_ReadyToUploadMessage" xml:space="preserve">
    <value>Ready to upload</value>
  </data>
  <data name="Uploader_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="Uploader_RemovedFailedMessage" xml:space="preserve">
    <value>Unable to remove file</value>
  </data>
  <data name="Uploader_RemovedSuccessMessage" xml:space="preserve">
    <value>File removed successfully</value>
  </data>
  <data name="Uploader_Resume" xml:space="preserve">
    <value>Resume</value>
  </data>
  <data name="Uploader_Retry" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="Uploader_Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="Uploader_UploadFailedMessage" xml:space="preserve">
    <value>File failed to upload</value>
  </data>
  <data name="Uploader_UploadSuccessMessage" xml:space="preserve">
    <value>File uploaded successfully</value>
  </data>
  <data name="RecurrenceEditor_Count" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="RecurrenceEditor_Daily" xml:space="preserve">
    <value>Daily</value>
  </data>
  <data name="RecurrenceEditor_Days" xml:space="preserve">
    <value>Day(s)</value>
  </data>
  <data name="RecurrenceEditor_End" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="RecurrenceEditor_Every" xml:space="preserve">
    <value>every</value>
  </data>
  <data name="RecurrenceEditor_First" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="RecurrenceEditor_Fourth" xml:space="preserve">
    <value>Fourth</value>
  </data>
  <data name="RecurrenceEditor_Last" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="RecurrenceEditor_Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="RecurrenceEditor_MonthExpander" xml:space="preserve">
    <value>Month Expander</value>
  </data>
  <data name="RecurrenceEditor_Monthly" xml:space="preserve">
    <value>Monthly</value>
  </data>
  <data name="RecurrenceEditor_MonthPosition" xml:space="preserve">
    <value>Month Position</value>
  </data>
  <data name="RecurrenceEditor_Months" xml:space="preserve">
    <value>Month(s)</value>
  </data>
  <data name="RecurrenceEditor_MonthWeek" xml:space="preserve">
    <value>Month Week</value>
  </data>
  <data name="RecurrenceEditor_Never" xml:space="preserve">
    <value>Never</value>
  </data>
  <data name="RecurrenceEditor_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="RecurrenceEditor_On" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="RecurrenceEditor_OnDay" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="RecurrenceEditor_Repeat" xml:space="preserve">
    <value>Repeat</value>
  </data>
  <data name="RecurrenceEditor_RepeatEvery" xml:space="preserve">
    <value>Repeat every</value>
  </data>
  <data name="RecurrenceEditor_RepeatInterval" xml:space="preserve">
    <value>Repeat Interval</value>
  </data>
  <data name="RecurrenceEditor_Second" xml:space="preserve">
    <value>Second</value>
  </data>
  <data name="RecurrenceEditor_SummaryDay" xml:space="preserve">
    <value>day(s)</value>
  </data>
  <data name="RecurrenceEditor_SummaryMonth" xml:space="preserve">
    <value>month(s)</value>
  </data>
  <data name="RecurrenceEditor_SummaryOn" xml:space="preserve">
    <value>on</value>
  </data>
  <data name="RecurrenceEditor_SummaryRepeat" xml:space="preserve">
    <value>Repeats</value>
  </data>
  <data name="RecurrenceEditor_SummaryTimes" xml:space="preserve">
    <value>time(s)</value>
  </data>
  <data name="RecurrenceEditor_SummaryUntil" xml:space="preserve">
    <value>until</value>
  </data>
  <data name="RecurrenceEditor_SummaryWeek" xml:space="preserve">
    <value>week(s)</value>
  </data>
  <data name="RecurrenceEditor_SummaryYear" xml:space="preserve">
    <value>year(s)</value>
  </data>
  <data name="RecurrenceEditor_Third" xml:space="preserve">
    <value>Third</value>
  </data>
  <data name="RecurrenceEditor_Until" xml:space="preserve">
    <value>Until</value>
  </data>
  <data name="RecurrenceEditor_Weekly" xml:space="preserve">
    <value>Weekly</value>
  </data>
  <data name="RecurrenceEditor_Weeks" xml:space="preserve">
    <value>Week(s)</value>
  </data>
  <data name="RecurrenceEditor_YearExpander" xml:space="preserve">
    <value>Year Expander</value>
  </data>
  <data name="RecurrenceEditor_Yearly" xml:space="preserve">
    <value>Yearly</value>
  </data>
  <data name="RecurrenceEditor_Years" xml:space="preserve">
    <value>Year(s)</value>
  </data>
  <data name="Schedule_AddTitle" xml:space="preserve">
    <value>Add title</value>
  </data>
  <data name="Schedule_Agenda" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <data name="Schedule_Alert" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="Schedule_AllDay" xml:space="preserve">
    <value>All day</value>
  </data>
  <data name="Schedule_BeginFrom" xml:space="preserve">
    <value>Begin From</value>
  </data>
  <data name="Schedule_BlockAlert" xml:space="preserve">
    <value>Events cannot be scheduled within the blocked time range.</value>
  </data>
  <data name="Schedule_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Schedule_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Schedule_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Schedule_CreateError" xml:space="preserve">
    <value>The duration of the event must be shorter than how frequently it occurs. Shorten the duration, or change the recurrence pattern in the recurrence event editor.</value>
  </data>
  <data name="Schedule_CreateEvent" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Schedule_Day" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="Schedule_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Schedule_DeleteButton" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Schedule_DeleteContent" xml:space="preserve">
    <value>Are you sure you want to delete this event?</value>
  </data>
  <data name="Schedule_DeleteEvent" xml:space="preserve">
    <value>Delete Event</value>
  </data>
  <data name="Schedule_DeleteMultipleContent" xml:space="preserve">
    <value>Are you sure you want to delete the selected events?</value>
  </data>
  <data name="Schedule_DeleteMultipleEvent" xml:space="preserve">
    <value>Delete Multiple Events</value>
  </data>
  <data name="Schedule_DeleteSeries" xml:space="preserve">
    <value>Entire Series</value>
  </data>
  <data name="Schedule_DeleteTitle" xml:space="preserve">
    <value>Delete Event</value>
  </data>
  <data name="Schedule_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Schedule_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Schedule_EditContent" xml:space="preserve">
    <value>How would you like to change the appointment in the series?</value>
  </data>
  <data name="Schedule_EditEvent" xml:space="preserve">
    <value>Edit Event</value>
  </data>
  <data name="Schedule_EditFollowingEvent" xml:space="preserve">
    <value>Following Events</value>
  </data>
  <data name="Schedule_EditRecurrence" xml:space="preserve">
    <value>Edit Recurrence</value>
  </data>
  <data name="Schedule_EditSeries" xml:space="preserve">
    <value>Entire Series</value>
  </data>
  <data name="Schedule_EditTitle" xml:space="preserve">
    <value>Edit Event</value>
  </data>
  <data name="Schedule_EmptyContainer" xml:space="preserve">
    <value>There are no events scheduled on this day.</value>
  </data>
  <data name="Schedule_End" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="Schedule_EndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="Schedule_EndAt" xml:space="preserve">
    <value>Ends At</value>
  </data>
  <data name="Schedule_EndTimezone" xml:space="preserve">
    <value>End Time zone</value>
  </data>
  <data name="Schedule_InvalidDateError" xml:space="preserve">
    <value>The entered date value is invalid.</value>
  </data>
  <data name="Schedule_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Schedule_LookForMore" xml:space="preserve">
    <value>Look for more</value>
  </data>
  <data name="Schedule_Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="Schedule_MonthAgenda" xml:space="preserve">
    <value>Month Agenda</value>
  </data>
  <data name="Schedule_More" xml:space="preserve">
    <value>more</value>
  </data>
  <data name="Schedule_MoreDetails" xml:space="preserve">
    <value>More Details</value>
  </data>
  <data name="Schedule_MoreEvents" xml:space="preserve">
    <value>More Events</value>
  </data>
  <data name="Schedule_NewEvent" xml:space="preserve">
    <value>New Event</value>
  </data>
  <data name="Schedule_next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Schedule_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Schedule_NoEvents" xml:space="preserve">
    <value>No events</value>
  </data>
  <data name="Schedule_NoRecords" xml:space="preserve">
    <value>No Records</value>
  </data>
  <data name="Schedule_NoTitle" xml:space="preserve">
    <value>(No Title)</value>
  </data>
  <data name="Schedule_Occurrence" xml:space="preserve">
    <value>Occurrence</value>
  </data>
  <data name="Schedule_Ok" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="Schedule_Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Schedule_Recurrence" xml:space="preserve">
    <value>Recurrence</value>
  </data>
  <data name="Schedule_RecurringEvent" xml:space="preserve">
    <value>Recurring Event</value>
  </data>
  <data name="Schedule_Repeat" xml:space="preserve">
    <value>Repeat</value>
  </data>
  <data name="Schedule_Repeats" xml:space="preserve">
    <value>Repeats</value>
  </data>
  <data name="Schedule_SameDayAlert" xml:space="preserve">
    <value>Two occurrences of the same event cannot occur on the same day.</value>
  </data>
  <data name="Schedule_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Schedule_SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Schedule_SelectedItems" xml:space="preserve">
    <value>Items selected</value>
  </data>
  <data name="Schedule_SearchTimezone" xml:space="preserve">
    <value>Search Time zone</value>
  </data>
  <data name="Schedule_Series" xml:space="preserve">
    <value>Series</value>
  </data>
  <data name="Schedule_SeriesChangeAlert" xml:space="preserve">
    <value>Do you want to cancel the changes made to specific instances of this series and match it to the whole series again?</value>
  </data>
  <data name="Schedule_ShowingEventsUntil" xml:space="preserve">
    <value>Showing events until</value>
  </data>
  <data name="Schedule_Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="Schedule_StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="Schedule_StartEndError" xml:space="preserve">
    <value>The selected end date occurs before the start date.</value>
  </data>
  <data name="Schedule_StartTimezone" xml:space="preserve">
    <value>Start Time zone</value>
  </data>
  <data name="Schedule_Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Schedule_TimelineDay" xml:space="preserve">
    <value>Timeline Day</value>
  </data>
  <data name="Schedule_TimelineMonth" xml:space="preserve">
    <value>Timeline Month</value>
  </data>
  <data name="Schedule_TimelineWeek" xml:space="preserve">
    <value>Timeline Week</value>
  </data>
  <data name="Schedule_TimelineWorkWeek" xml:space="preserve">
    <value>Timeline Work Week</value>
  </data>
  <data name="Schedule_TimelineYear" xml:space="preserve">
    <value>Timeline Year</value>
  </data>
  <data name="Schedule_Timezone" xml:space="preserve">
    <value>Time zone</value>
  </data>
  <data name="Schedule_Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Schedule_Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="Schedule_Week" xml:space="preserve">
    <value>Week</value>
  </data>
  <data name="Schedule_WeekAgenda" xml:space="preserve">
    <value>Week Agenda</value>
  </data>
  <data name="Schedule_WorkWeek" xml:space="preserve">
    <value>Work Week</value>
  </data>
  <data name="Schedule_WorkWeekAgenda" xml:space="preserve">
    <value>Work Week Agenda</value>
  </data>
  <data name="Schedule_WrongPattern" xml:space="preserve">
    <value>The recurrence pattern is not valid.</value>
  </data>
  <data name="Schedule_Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Schedule_Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="Slider_DecrementTitle" xml:space="preserve">
    <value>Decrease</value>
  </data>
  <data name="Slider_IncrementTitle" xml:space="preserve">
    <value>Increase</value>
  </data>
  <data name="Stepper_Optional" xml:space="preserve">
    <value>Optional</value>
  </data>
  <data name="Tab_CloseButtonTitle" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Carousel_PreviousSlide" xml:space="preserve">
    <value>Previous Slide</value>
  </data>
  <data name="Carousel_NextSlide" xml:space="preserve">
    <value>Next Slide</value>
  </data>
  <data name="Carousel_Slide" xml:space="preserve">
    <value>Slide</value>
  </data>
  <data name="Carousel_Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="Carousel_PlaySlide" xml:space="preserve">
    <value>Stop auto animation</value>
  </data>
  <data name="Carousel_PauseSlide" xml:space="preserve">
    <value>Start auto animation</value>
  </data>
  <data name="Carousel_SlideShow" xml:space="preserve">
    <value>Slide show</value>
  </data>
  <data name="DocumentEditor_Table" xml:space="preserve">
    <value>Table</value>
  </data>
  <data name="DocumentEditor_Row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="DocumentEditor_Cell" xml:space="preserve">
    <value>Cell</value>
  </data>
  <data name="DocumentEditor_Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="DocumentEditor_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DocumentEditor_Size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="DocumentEditor_PreferredWidth" xml:space="preserve">
    <value>Preferred width</value>
  </data>
  <data name="DocumentEditor_Points" xml:space="preserve">
    <value>Points</value>
  </data>
  <data name="DocumentEditor_Percent" xml:space="preserve">
    <value>Percent</value>
  </data>
  <data name="DocumentEditor_MeasureIn" xml:space="preserve">
    <value>Measure in</value>
  </data>
  <data name="DocumentEditor_Alignment" xml:space="preserve">
    <value>Alignment</value>
  </data>
  <data name="DocumentEditor_Left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="DocumentEditor_Center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="DocumentEditor_Right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="DocumentEditor_Justify" xml:space="preserve">
    <value>Justify</value>
  </data>
  <data name="DocumentEditor_OutlineLevel" xml:space="preserve">
    <value>Outline Level</value>
  </data>
  <data name="DocumentEditor_BodyText" xml:space="preserve">
    <value>Body Text</value>
  </data>
  <data name="DocumentEditor_Level1" xml:space="preserve">
    <value>Level 1</value>
  </data>
  <data name="DocumentEditor_Level2" xml:space="preserve">
    <value>Level 2</value>
  </data>
  <data name="DocumentEditor_Level3" xml:space="preserve">
    <value>Level 3</value>
  </data>
  <data name="DocumentEditor_Level4" xml:space="preserve">
    <value>Level 4</value>
  </data>
  <data name="DocumentEditor_Level5" xml:space="preserve">
    <value>Level 5</value>
  </data>
  <data name="DocumentEditor_Level6" xml:space="preserve">
    <value>Level 6</value>
  </data>
  <data name="DocumentEditor_Level7" xml:space="preserve">
    <value>Level 7</value>
  </data>
  <data name="DocumentEditor_Level8" xml:space="preserve">
    <value>Level 8</value>
  </data>
  <data name="DocumentEditor_Level9" xml:space="preserve">
    <value>Level 9</value>
  </data>
  <data name="DocumentEditor_IndentFromLeft" xml:space="preserve">
    <value>Indent from left</value>
  </data>
  <data name="DocumentEditor_BordersAndShading" xml:space="preserve">
    <value>Borders and Shading</value>
  </data>
  <data name="DocumentEditor_Options" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="DocumentEditor_SpecifyHeight" xml:space="preserve">
    <value>Specify height</value>
  </data>
  <data name="DocumentEditor_AtLeast" xml:space="preserve">
    <value>At least</value>
  </data>
  <data name="DocumentEditor_Exactly" xml:space="preserve">
    <value>Exactly</value>
  </data>
  <data name="DocumentEditor_RowHeightIs" xml:space="preserve">
    <value>Row height is</value>
  </data>
  <data name="DocumentEditor_AllowRowToBreakAcrossPages" xml:space="preserve">
    <value>Allow row to break across pages</value>
  </data>
  <data name="DocumentEditor_RepeatAsHeaderRowAtTheTopOfEachPage" xml:space="preserve">
    <value>Repeat as header row at the top of each page</value>
  </data>
  <data name="DocumentEditor_VerticalAlignment" xml:space="preserve">
    <value>Vertical alignment</value>
  </data>
  <data name="DocumentEditor_Top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="DocumentEditor_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="DocumentEditor_DefaultCellMargins" xml:space="preserve">
    <value>Default cell margins</value>
  </data>
  <data name="DocumentEditor_DefaultCellSpacing" xml:space="preserve">
    <value>Default cell spacing</value>
  </data>
  <data name="DocumentEditor_AllowSpacingBetweenCells" xml:space="preserve">
    <value>Allow spacing between cells</value>
  </data>
  <data name="DocumentEditor_CellMargins" xml:space="preserve">
    <value>Cell margins</value>
  </data>
  <data name="DocumentEditor_SameAsTheWholeTable" xml:space="preserve">
    <value>Same as the whole table</value>
  </data>
  <data name="DocumentEditor_Borders" xml:space="preserve">
    <value>Borders</value>
  </data>
  <data name="DocumentEditor_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="DocumentEditor_Style" xml:space="preserve">
    <value>Style</value>
  </data>
  <data name="DocumentEditor_Width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="DocumentEditor_Height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="DocumentEditor_Letter" xml:space="preserve">
    <value>Letter</value>
  </data>
  <data name="DocumentEditor_Tabloid" xml:space="preserve">
    <value>Tabloid</value>
  </data>
  <data name="DocumentEditor_Legal" xml:space="preserve">
    <value>Legal</value>
  </data>
  <data name="DocumentEditor_Statement" xml:space="preserve">
    <value>Statement</value>
  </data>
  <data name="DocumentEditor_Executive" xml:space="preserve">
    <value>Executive</value>
  </data>
  <data name="DocumentEditor_A3" xml:space="preserve">
    <value>A3</value>
  </data>
  <data name="DocumentEditor_A4" xml:space="preserve">
    <value>A4</value>
  </data>
  <data name="DocumentEditor_A5" xml:space="preserve">
    <value>A5</value>
  </data>
  <data name="DocumentEditor_B4" xml:space="preserve">
    <value>B4</value>
  </data>
  <data name="DocumentEditor_B5" xml:space="preserve">
    <value>B5</value>
  </data>
  <data name="DocumentEditor_CustomSize" xml:space="preserve">
    <value>Custom size</value>
  </data>
  <data name="DocumentEditor_DifferentOddAndEven" xml:space="preserve">
    <value>Different odd and even</value>
  </data>
  <data name="DocumentEditor_DifferentFirstPage" xml:space="preserve">
    <value>Different first page</value>
  </data>
  <data name="DocumentEditor_FromEdge" xml:space="preserve">
    <value>From edge</value>
  </data>
  <data name="DocumentEditor_Header" xml:space="preserve">
    <value>Header</value>
  </data>
  <data name="DocumentEditor_Footer" xml:space="preserve">
    <value>Footer</value>
  </data>
  <data name="DocumentEditor_Margin" xml:space="preserve">
    <value>Margins</value>
  </data>
  <data name="DocumentEditor_Paper" xml:space="preserve">
    <value>Paper</value>
  </data>
  <data name="DocumentEditor_Layout" xml:space="preserve">
    <value>Layout</value>
  </data>
  <data name="DocumentEditor_Orientation" xml:space="preserve">
    <value>Orientation</value>
  </data>
  <data name="DocumentEditor_Landscape" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="DocumentEditor_Portrait" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="DocumentEditor_ShowPageNumbers" xml:space="preserve">
    <value>Show page numbers</value>
  </data>
  <data name="DocumentEditor_RightAlignPageNumbers" xml:space="preserve">
    <value>Right align page numbers.</value>
  </data>
  <data name="DocumentEditor_Nothing" xml:space="preserve">
    <value>Nothing</value>
  </data>
  <data name="DocumentEditor_TabLeader" xml:space="preserve">
    <value>Tab leader</value>
  </data>
  <data name="DocumentEditor_ShowLevels" xml:space="preserve">
    <value>Show levels</value>
  </data>
  <data name="DocumentEditor_UseHyperlinksInsteadOfPageNumbers" xml:space="preserve">
    <value>Use hyperlinks instead of page numbers</value>
  </data>
  <data name="DocumentEditor_BuildTableOfContentsFrom" xml:space="preserve">
    <value>Build table of contents from</value>
  </data>
  <data name="DocumentEditor_Styles" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="DocumentEditor_AvailableStyles" xml:space="preserve">
    <value>Available styles</value>
  </data>
  <data name="DocumentEditor_TOCLevel" xml:space="preserve">
    <value>TOC level</value>
  </data>
  <data name="DocumentEditor_Heading" xml:space="preserve">
    <value>Heading</value>
  </data>
  <data name="DocumentEditor_Heading1" xml:space="preserve">
    <value>Heading 1</value>
  </data>
  <data name="DocumentEditor_Heading2" xml:space="preserve">
    <value>Heading 2</value>
  </data>
  <data name="DocumentEditor_Heading3" xml:space="preserve">
    <value>Heading 3</value>
  </data>
  <data name="DocumentEditor_Heading4" xml:space="preserve">
    <value>Heading 4</value>
  </data>
  <data name="DocumentEditor_Heading5" xml:space="preserve">
    <value>Heading 5</value>
  </data>
  <data name="DocumentEditor_Heading6" xml:space="preserve">
    <value>Heading 6</value>
  </data>
  <data name="DocumentEditor_Heading7" xml:space="preserve">
    <value>Heading 7</value>
  </data>
  <data name="DocumentEditor_Heading8" xml:space="preserve">
    <value>Heading 8</value>
  </data>
  <data name="DocumentEditor_Heading9" xml:space="preserve">
    <value>Heading 9</value>
  </data>
  <data name="DocumentEditor_ListParagraph" xml:space="preserve">
    <value>List Paragraph</value>
  </data>
  <data name="DocumentEditor_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="DocumentEditor_OutlineLevels" xml:space="preserve">
    <value>Outline levels</value>
  </data>
  <data name="DocumentEditor_TableEntryFields" xml:space="preserve">
    <value>Table entry fields</value>
  </data>
  <data name="DocumentEditor_Modify" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="DocumentEditor_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="DocumentEditor_Setting" xml:space="preserve">
    <value>Setting</value>
  </data>
  <data name="DocumentEditor_Box" xml:space="preserve">
    <value>Box</value>
  </data>
  <data name="DocumentEditor_All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="DocumentEditor_Custom" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="DocumentEditor_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="DocumentEditor_Shading" xml:space="preserve">
    <value>Shading</value>
  </data>
  <data name="DocumentEditor_Fill" xml:space="preserve">
    <value>Fill</value>
  </data>
  <data name="DocumentEditor_ApplyTo" xml:space="preserve">
    <value>Apply to</value>
  </data>
  <data name="DocumentEditor_TableProperties" xml:space="preserve">
    <value>Table Properties</value>
  </data>
  <data name="DocumentEditor_CellOptions" xml:space="preserve">
    <value>Cell Options</value>
  </data>
  <data name="DocumentEditor_TableOptions" xml:space="preserve">
    <value>Table Options</value>
  </data>
  <data name="DocumentEditor_InsertTable" xml:space="preserve">
    <value>Insert Table</value>
  </data>
  <data name="DocumentEditor_NumberOfColumns" xml:space="preserve">
    <value>Number of columns</value>
  </data>
  <data name="DocumentEditor_NumberOfRows" xml:space="preserve">
    <value>Number of rows</value>
  </data>
  <data name="DocumentEditor_TextToDisplay" xml:space="preserve">
    <value>Text to display</value>
  </data>
  <data name="DocumentEditor_Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="DocumentEditor_InsertHyperlink" xml:space="preserve">
    <value>Insert Hyperlink</value>
  </data>
  <data name="DocumentEditor_EditHyperlink" xml:space="preserve">
    <value>Edit Hyperlink</value>
  </data>
  <data name="DocumentEditor_Insert" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="DocumentEditor_General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="DocumentEditor_Indentation" xml:space="preserve">
    <value>Indentation</value>
  </data>
  <data name="DocumentEditor_BeforeText" xml:space="preserve">
    <value>Before text</value>
  </data>
  <data name="DocumentEditor_Special" xml:space="preserve">
    <value>Special</value>
  </data>
  <data name="DocumentEditor_FirstLine" xml:space="preserve">
    <value>First line</value>
  </data>
  <data name="DocumentEditor_Hanging" xml:space="preserve">
    <value>Hanging</value>
  </data>
  <data name="DocumentEditor_AfterText" xml:space="preserve">
    <value>After text</value>
  </data>
  <data name="DocumentEditor_By" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="DocumentEditor_Before" xml:space="preserve">
    <value>Before</value>
  </data>
  <data name="DocumentEditor_LineSpacing" xml:space="preserve">
    <value>Line spacing</value>
  </data>
  <data name="DocumentEditor_After" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="DocumentEditor_At" xml:space="preserve">
    <value>At</value>
  </data>
  <data name="DocumentEditor_Multiple" xml:space="preserve">
    <value>Multiple</value>
  </data>
  <data name="DocumentEditor_Spacing" xml:space="preserve">
    <value>Spacing</value>
  </data>
  <data name="DocumentEditor_DefineNewMultilevelList" xml:space="preserve">
    <value>Define new Multilevel list</value>
  </data>
  <data name="DocumentEditor_ListLevel" xml:space="preserve">
    <value>List level</value>
  </data>
  <data name="DocumentEditor_ChooseLevelToModify" xml:space="preserve">
    <value>Choose level to modify</value>
  </data>
  <data name="DocumentEditor_Level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="DocumentEditor_NumberFormat" xml:space="preserve">
    <value>Number format</value>
  </data>
  <data name="DocumentEditor_NumberStyleForThisLevel" xml:space="preserve">
    <value>Number style for this level</value>
  </data>
  <data name="DocumentEditor_EnterFormattingForNumber" xml:space="preserve">
    <value>Enter formatting for number</value>
  </data>
  <data name="DocumentEditor_StartAt" xml:space="preserve">
    <value>Start at</value>
  </data>
  <data name="DocumentEditor_RestartListAfter" xml:space="preserve">
    <value>Restart list after</value>
  </data>
  <data name="DocumentEditor_Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="DocumentEditor_TextIndentAt" xml:space="preserve">
    <value>Text indent at</value>
  </data>
  <data name="DocumentEditor_AlignedAt" xml:space="preserve">
    <value>Aligned at</value>
  </data>
  <data name="DocumentEditor_FollowNumberWith" xml:space="preserve">
    <value>Follow number with</value>
  </data>
  <data name="DocumentEditor_TabCharacter" xml:space="preserve">
    <value>Tab character</value>
  </data>
  <data name="DocumentEditor_Space" xml:space="preserve">
    <value>Space</value>
  </data>
  <data name="DocumentEditor_Arabic" xml:space="preserve">
    <value>Arabic</value>
  </data>
  <data name="DocumentEditor_UpRoman" xml:space="preserve">
    <value>UpRoman</value>
  </data>
  <data name="DocumentEditor_LowRoman" xml:space="preserve">
    <value>LowRoman</value>
  </data>
  <data name="DocumentEditor_UpLetter" xml:space="preserve">
    <value>UpLetter</value>
  </data>
  <data name="DocumentEditor_LowLetter" xml:space="preserve">
    <value>LowLetter</value>
  </data>
  <data name="DocumentEditor_Number" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="DocumentEditor_LeadingZero" xml:space="preserve">
    <value>Leading zero</value>
  </data>
  <data name="DocumentEditor_Bullet" xml:space="preserve">
    <value>Bullet</value>
  </data>
  <data name="DocumentEditor_Ordinal" xml:space="preserve">
    <value>Ordinal</value>
  </data>
  <data name="DocumentEditor_OrdinalText" xml:space="preserve">
    <value>Ordinal Text</value>
  </data>
  <data name="DocumentEditor_ForEast" xml:space="preserve">
    <value>For East</value>
  </data>
  <data name="DocumentEditor_NoRestart" xml:space="preserve">
    <value>No Restart</value>
  </data>
  <data name="DocumentEditor_Font" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="DocumentEditor_FontStyle" xml:space="preserve">
    <value>Font style</value>
  </data>
  <data name="DocumentEditor_UnderlineStyle" xml:space="preserve">
    <value>Underline style</value>
  </data>
  <data name="DocumentEditor_FontColor" xml:space="preserve">
    <value>Font color</value>
  </data>
  <data name="DocumentEditor_Effects" xml:space="preserve">
    <value>Effects</value>
  </data>
  <data name="DocumentEditor_Strikethrough" xml:space="preserve">
    <value>Strikethrough</value>
  </data>
  <data name="DocumentEditor_Superscript" xml:space="preserve">
    <value>Superscript</value>
  </data>
  <data name="DocumentEditor_Subscript" xml:space="preserve">
    <value>Subscript</value>
  </data>
  <data name="DocumentEditor_DoubleStrikethrough" xml:space="preserve">
    <value>Double strikethrough</value>
  </data>
  <data name="DocumentEditor_Regular" xml:space="preserve">
    <value>Regular</value>
  </data>
  <data name="DocumentEditor_Bold" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="DocumentEditor_Italic" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="DocumentEditor_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="DocumentEditor_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="DocumentEditor_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="DocumentEditor_Hyperlink" xml:space="preserve">
    <value>Hyperlink</value>
  </data>
  <data name="DocumentEditor_OpenHyperlink" xml:space="preserve">
    <value>Open Hyperlink</value>
  </data>
  <data name="DocumentEditor_CopyHyperlink" xml:space="preserve">
    <value>Copy Hyperlink</value>
  </data>
  <data name="DocumentEditor_RemoveHyperlink" xml:space="preserve">
    <value>Remove Hyperlink</value>
  </data>
  <data name="DocumentEditor_Paragraph" xml:space="preserve">
    <value>Paragraph</value>
  </data>
  <data name="DocumentEditor_LinkedStyle" xml:space="preserve">
    <value>Linked(Paragraph and Character)</value>
  </data>
  <data name="DocumentEditor_Character" xml:space="preserve">
    <value>Character</value>
  </data>
  <data name="DocumentEditor_MergeCells" xml:space="preserve">
    <value>Merge Cells</value>
  </data>
  <data name="DocumentEditor_InsertAbove" xml:space="preserve">
    <value>Insert Above</value>
  </data>
  <data name="DocumentEditor_InsertBelow" xml:space="preserve">
    <value>Insert Below</value>
  </data>
  <data name="DocumentEditor_InsertLeft" xml:space="preserve">
    <value>Insert Left</value>
  </data>
  <data name="DocumentEditor_InsertRight" xml:space="preserve">
    <value>Insert Right</value>
  </data>
  <data name="DocumentEditor_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DocumentEditor_DeleteTable" xml:space="preserve">
    <value>Delete Table</value>
  </data>
  <data name="DocumentEditor_DeleteRow" xml:space="preserve">
    <value>Delete Row</value>
  </data>
  <data name="DocumentEditor_DeleteColumn" xml:space="preserve">
    <value>Delete Column</value>
  </data>
  <data name="DocumentEditor_FileName" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="DocumentEditor_FormatType" xml:space="preserve">
    <value>Format Type</value>
  </data>
  <data name="DocumentEditor_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="DocumentEditor_Navigation" xml:space="preserve">
    <value>Navigation</value>
  </data>
  <data name="DocumentEditor_Results" xml:space="preserve">
    <value>Results</value>
  </data>
  <data name="DocumentEditor_Replace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="DocumentEditor_ReplaceAll" xml:space="preserve">
    <value>Replace All</value>
  </data>
  <data name="DocumentEditor_WeReplacedAll" xml:space="preserve">
    <value>We replaced all</value>
  </data>
  <data name="DocumentEditor_Find" xml:space="preserve">
    <value>Find</value>
  </data>
  <data name="DocumentEditor_Headings" xml:space="preserve">
    <value>Headings</value>
  </data>
  <data name="DocumentEditor_NoMatches" xml:space="preserve">
    <value>No matches</value>
  </data>
  <data name="DocumentEditor_AllDone" xml:space="preserve">
    <value>All Done</value>
  </data>
  <data name="DocumentEditor_Result" xml:space="preserve">
    <value>Result</value>
  </data>
  <data name="DocumentEditor_Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="DocumentEditor_Instances" xml:space="preserve">
    <value>instances</value>
  </data>
  <data name="DocumentEditor_With" xml:space="preserve">
    <value>with</value>
  </data>
  <data name="DocumentEditor_ClickToFollowLink" xml:space="preserve">
    <value>Click to follow link</value>
  </data>
  <data name="DocumentEditor_ContinueNumbering" xml:space="preserve">
    <value>Continue Numbering</value>
  </data>
  <data name="DocumentEditor_BookmarkName" xml:space="preserve">
    <value>Bookmark name</value>
  </data>
  <data name="DocumentEditor_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="DocumentEditor_RestartAt" xml:space="preserve">
    <value>Restart At</value>
  </data>
  <data name="DocumentEditor_Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="DocumentEditor_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="DocumentEditor_StyleType" xml:space="preserve">
    <value>Style type</value>
  </data>
  <data name="DocumentEditor_StyleBasedOn" xml:space="preserve">
    <value>Style based on</value>
  </data>
  <data name="DocumentEditor_StyleForFollowingParagraph" xml:space="preserve">
    <value>Style for following paragraph</value>
  </data>
  <data name="DocumentEditor_Formatting" xml:space="preserve">
    <value>Formatting</value>
  </data>
  <data name="DocumentEditor_NumberingAndBullets" xml:space="preserve">
    <value>Numbering and Bullets</value>
  </data>
  <data name="DocumentEditor_Numbering" xml:space="preserve">
    <value>Numbering</value>
  </data>
  <data name="DocumentEditor_UpdateField" xml:space="preserve">
    <value>Update Field</value>
  </data>
  <data name="DocumentEditor_EditField" xml:space="preserve">
    <value>Edit Field</value>
  </data>
  <data name="DocumentEditor_Bookmark" xml:space="preserve">
    <value>Bookmark</value>
  </data>
  <data name="DocumentEditor_PageSetup" xml:space="preserve">
    <value>Page Setup</value>
  </data>
  <data name="DocumentEditor_NoBookmarksFound" xml:space="preserve">
    <value>No bookmarks found</value>
  </data>
  <data name="DocumentEditor_Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="DocumentEditor_CreateNewStyle" xml:space="preserve">
    <value>Create New Style</value>
  </data>
  <data name="DocumentEditor_ModifyStyle" xml:space="preserve">
    <value>Modify Style</value>
  </data>
  <data name="DocumentEditor_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="DocumentEditor_Bullets" xml:space="preserve">
    <value>Bullets</value>
  </data>
  <data name="DocumentEditor_UseBookmarks" xml:space="preserve">
    <value>Use bookmarks</value>
  </data>
  <data name="DocumentEditor_TableOfContents" xml:space="preserve">
    <value>Table of Contents</value>
  </data>
  <data name="DocumentEditor_AutoFit" xml:space="preserve">
    <value>AutoFit</value>
  </data>
  <data name="DocumentEditor_AutoFitToContents" xml:space="preserve">
    <value>AutoFit to Contents</value>
  </data>
  <data name="DocumentEditor_AutoFitToWindow" xml:space="preserve">
    <value>AutoFit to Window</value>
  </data>
  <data name="DocumentEditor_FixedColumnWidth" xml:space="preserve">
    <value>Fixed Column Width</value>
  </data>
  <data name="DocumentEditor_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="DocumentEditor_MatchCase" xml:space="preserve">
    <value>Match case</value>
  </data>
  <data name="DocumentEditor_WholeWords" xml:space="preserve">
    <value>Whole words</value>
  </data>
  <data name="DocumentEditor_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="DocumentEditor_GoTo" xml:space="preserve">
    <value>Go To</value>
  </data>
  <data name="DocumentEditor_SearchFor" xml:space="preserve">
    <value>Search for</value>
  </data>
  <data name="DocumentEditor_ReplaceWith" xml:space="preserve">
    <value>Replace with</value>
  </data>
  <data name="DocumentEditor_TOC1" xml:space="preserve">
    <value>TOC 1</value>
  </data>
  <data name="DocumentEditor_TOC2" xml:space="preserve">
    <value>TOC 2</value>
  </data>
  <data name="DocumentEditor_TOC3" xml:space="preserve">
    <value>TOC 3</value>
  </data>
  <data name="DocumentEditor_TOC4" xml:space="preserve">
    <value>TOC 4</value>
  </data>
  <data name="DocumentEditor_TOC5" xml:space="preserve">
    <value>TOC 5</value>
  </data>
  <data name="DocumentEditor_TOC6" xml:space="preserve">
    <value>TOC 6</value>
  </data>
  <data name="DocumentEditor_TOC7" xml:space="preserve">
    <value>TOC 7</value>
  </data>
  <data name="DocumentEditor_TOC8" xml:space="preserve">
    <value>TOC 8</value>
  </data>
  <data name="DocumentEditor_TOC9" xml:space="preserve">
    <value>TOC 9</value>
  </data>
  <data name="DocumentEditor_RightToLeft" xml:space="preserve">
    <value>Right-to-left</value>
  </data>
  <data name="DocumentEditor_LeftToRight" xml:space="preserve">
    <value>Left-to-right</value>
  </data>
  <data name="DocumentEditor_Direction" xml:space="preserve">
    <value>Direction</value>
  </data>
  <data name="DocumentEditor_TableDirection" xml:space="preserve">
    <value>Table direction</value>
  </data>
  <data name="DocumentEditor_IndentFromRight" xml:space="preserve">
    <value>Indent from right</value>
  </data>
  <data name="DocumentEditor_ContextualSpacing" xml:space="preserve">
    <value>Don't add space between the paragraphs of the same styles</value>
  </data>
  <data name="DocumentEditor_PasswordMismatch" xml:space="preserve">
    <value>The password don't match</value>
  </data>
  <data name="DocumentEditor_RestrictEditing" xml:space="preserve">
    <value>Restrict Editing</value>
  </data>
  <data name="DocumentEditor_FormattingRestrictions" xml:space="preserve">
    <value>Formatting restrictions</value>
  </data>
  <data name="DocumentEditor_AllowFormatting" xml:space="preserve">
    <value>Allow formatting</value>
  </data>
  <data name="DocumentEditor_EditingRestrictions" xml:space="preserve">
    <value>Editing restrictions</value>
  </data>
  <data name="DocumentEditor_ReadOnly" xml:space="preserve">
    <value>Read only</value>
  </data>
  <data name="DocumentEditor_ExceptionsOptional" xml:space="preserve">
    <value>Exceptions (optional)</value>
  </data>
  <data name="DocumentEditor_SelectPartOfDocumentAndUser" xml:space="preserve">
    <value>Select parts of the document and choose users who are allowed to freely edit them.</value>
  </data>
  <data name="DocumentEditor_Everyone" xml:space="preserve">
    <value>Everyone</value>
  </data>
  <data name="DocumentEditor_MoreUsers" xml:space="preserve">
    <value>More users</value>
  </data>
  <data name="DocumentEditor_AddUsers" xml:space="preserve">
    <value>Add Users</value>
  </data>
  <data name="DocumentEditor_EnforcingProtection" xml:space="preserve">
    <value>Yes, Start Enforcing Protection</value>
  </data>
  <data name="DocumentEditor_StartEnforcingProtection" xml:space="preserve">
    <value>Start Enforcing Protection</value>
  </data>
  <data name="DocumentEditor_EnterUser" xml:space="preserve">
    <value>Enter User</value>
  </data>
  <data name="DocumentEditor_Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="DocumentEditor_EnterNewPassword" xml:space="preserve">
    <value>Enter new password</value>
  </data>
  <data name="DocumentEditor_ReenterNewPasswordToConfirm" xml:space="preserve">
    <value>Reenter new password to confirm</value>
  </data>
  <data name="DocumentEditor_YourPermissions" xml:space="preserve">
    <value>Your permissions</value>
  </data>
  <data name="DocumentEditor_ProtectedDocument" xml:space="preserve">
    <value>This document is protected from unintentional editing.</value>
  </data>
  <data name="DocumentEditor_YouMayFormatTextOnlyWithCertainStyles" xml:space="preserve">
    <value>You may format text only with certain styles.</value>
  </data>
  <data name="DocumentEditor_StopProtection" xml:space="preserve">
    <value>Stop Protection</value>
  </data>
  <data name="DocumentEditor_Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="DocumentEditor_SpellingEditor" xml:space="preserve">
    <value>Spelling Editor</value>
  </data>
  <data name="DocumentEditor_Spelling" xml:space="preserve">
    <value>Spelling</value>
  </data>
  <data name="DocumentEditor_SpellCheck" xml:space="preserve">
    <value>Spell Check</value>
  </data>
  <data name="DocumentEditor_UnderlineErrors" xml:space="preserve">
    <value>Underline errors</value>
  </data>
  <data name="DocumentEditor_Ignore" xml:space="preserve">
    <value>Ignore</value>
  </data>
  <data name="DocumentEditor_IgnoreAll" xml:space="preserve">
    <value>Ignore All</value>
  </data>
  <data name="DocumentEditor_AddToDictionary" xml:space="preserve">
    <value>Add to Dictionary</value>
  </data>
  <data name="DocumentEditor_Change" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="DocumentEditor_ChangeAll" xml:space="preserve">
    <value>Change All</value>
  </data>
  <data name="DocumentEditor_Suggestions" xml:space="preserve">
    <value>Suggestions</value>
  </data>
  <data name="DocumentEditor_ThePasswordIsIncorrect" xml:space="preserve">
    <value>The password is incorrect</value>
  </data>
  <data name="DocumentEditor_ErrorInEstablishingConnectionWithWebServer" xml:space="preserve">
    <value>Error in establishing connection with web server</value>
  </data>
  <data name="DocumentEditor_HighlightTheRegionsICanEdit" xml:space="preserve">
    <value>Highlight the regions I can edit</value>
  </data>
  <data name="DocumentEditor_ShowAllRegionsICanEdit" xml:space="preserve">
    <value>Show All Regions I Can Edit</value>
  </data>
  <data name="DocumentEditor_FindNextRegionICanEdit" xml:space="preserve">
    <value>Find Next Region I Can Edit</value>
  </data>
  <data name="DocumentEditor_KeepSourceFormatting" xml:space="preserve">
    <value>Keep source formatting</value>
  </data>
  <data name="DocumentEditor_MatchDestinationFormatting" xml:space="preserve">
    <value>Match destination formatting</value>
  </data>
  <data name="DocumentEditor_TextOnly" xml:space="preserve">
    <value>Text only</value>
  </data>
  <data name="DocumentEditor_Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="DocumentEditor_TypeYourComment" xml:space="preserve">
    <value>Type your comment</value>
  </data>
  <data name="DocumentEditor_Post" xml:space="preserve">
    <value>Post</value>
  </data>
  <data name="DocumentEditor_Reply" xml:space="preserve">
    <value>Reply</value>
  </data>
  <data name="DocumentEditor_NewComment" xml:space="preserve">
    <value>New Comment</value>
  </data>
  <data name="DocumentEditor_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="DocumentEditor_Resolve" xml:space="preserve">
    <value>Resolve</value>
  </data>
  <data name="DocumentEditor_Reopen" xml:space="preserve">
    <value>Reopen</value>
  </data>
  <data name="DocumentEditor_NoCommentsInThisDocument" xml:space="preserve">
    <value>No comments in this document</value>
  </data>
  <data name="DocumentEditor_More" xml:space="preserve">
    <value>more</value>
  </data>
  <data name="DocumentEditor_TypeYourCommentHere" xml:space="preserve">
    <value>Type your comment here</value>
  </data>
  <data name="DocumentEditor_NextComment" xml:space="preserve">
    <value>Next Comment</value>
  </data>
  <data name="DocumentEditor_PreviousComment" xml:space="preserve">
    <value>Previous Comment</value>
  </data>
  <data name="DocumentEditor_UnPostedComments" xml:space="preserve">
    <value>Un-posted comments</value>
  </data>
  <data name="DocumentEditor_DiscardComment" xml:space="preserve">
    <value>Added comments not posted. If you continue, that comment will be discarded.</value>
  </data>
  <data name="DocumentEditor_NoHeadings" xml:space="preserve">
    <value>No Heading Found!</value>
  </data>
  <data name="DocumentEditor_AddHeadings" xml:space="preserve">
    <value>This document has no headings. Please add headings and try again.</value>
  </data>
  <data name="DocumentEditor_MoreOptions" xml:space="preserve">
    <value>More Options</value>
  </data>
  <data name="DocumentEditor_ClickToSeeThisComment" xml:space="preserve">
    <value>Click to see this comment</value>
  </data>
  <data name="DocumentEditor_DropDownFormField" xml:space="preserve">
    <value>Drop Down Form Field</value>
  </data>
  <data name="DocumentEditor_DropDownItems" xml:space="preserve">
    <value>Drop-down items</value>
  </data>
  <data name="DocumentEditor_ItemsInDropDownList" xml:space="preserve">
    <value>Items in drop-down list</value>
  </data>
  <data name="DocumentEditor_ADDButton" xml:space="preserve">
    <value>ADD</value>
  </data>
  <data name="DocumentEditor_REMOVE" xml:space="preserve">
    <value>REMOVE</value>
  </data>
  <data name="DocumentEditor_FieldSettings" xml:space="preserve">
    <value>Field settings</value>
  </data>
  <data name="DocumentEditor_Tooltip" xml:space="preserve">
    <value>Tooltip</value>
  </data>
  <data name="DocumentEditor_DropDownEnabled" xml:space="preserve">
    <value>Drop-down enabled</value>
  </data>
  <data name="DocumentEditor_CheckBoxFormField" xml:space="preserve">
    <value>Check Box Form Field</value>
  </data>
  <data name="DocumentEditor_CheckBoxSize" xml:space="preserve">
    <value>Check box size</value>
  </data>
  <data name="DocumentEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="DocumentEditor_DefaultValue" xml:space="preserve">
    <value>Default value</value>
  </data>
  <data name="DocumentEditor_NotChecked" xml:space="preserve">
    <value>Not checked</value>
  </data>
  <data name="DocumentEditor_Checked" xml:space="preserve">
    <value>Checked</value>
  </data>
  <data name="DocumentEditor_CheckBoxEnabled" xml:space="preserve">
    <value>Check box enabled</value>
  </data>
  <data name="DocumentEditor_TextFormField" xml:space="preserve">
    <value>Text Form Field</value>
  </data>
  <data name="DocumentEditor_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="DocumentEditor_DefaultText" xml:space="preserve">
    <value>Default text</value>
  </data>
  <data name="DocumentEditor_MaximumLength" xml:space="preserve">
    <value>Maximum length</value>
  </data>
  <data name="DocumentEditor_TextFormat" xml:space="preserve">
    <value>Text format</value>
  </data>
  <data name="DocumentEditor_FillInEnabled" xml:space="preserve">
    <value>Fill-in enabled</value>
  </data>
  <data name="DocumentEditor_DefaultNumber" xml:space="preserve">
    <value>Default number</value>
  </data>
  <data name="DocumentEditor_DefaultDate" xml:space="preserve">
    <value>Default date</value>
  </data>
  <data name="DocumentEditor_DateFormat" xml:space="preserve">
    <value>Date format</value>
  </data>
  <data name="DocumentEditor_MergeTrack" xml:space="preserve">
    <value>This action wont be marked as change. Do you want to continue?</value>
  </data>
  <data name="DocumentEditor_UnTrack" xml:space="preserve">
    <value>Cannot be tracked</value>
  </data>
  <data name="DocumentEditor_Accept" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="DocumentEditor_Reject" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="DocumentEditor_PreviousChanges" xml:space="preserve">
    <value>Previous Changes</value>
  </data>
  <data name="DocumentEditor_NextChanges" xml:space="preserve">
    <value>Next Changes</value>
  </data>
  <data name="DocumentEditor_Inserted" xml:space="preserve">
    <value>Inserted</value>
  </data>
  <data name="DocumentEditor_Deleted" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="DocumentEditor_Changes" xml:space="preserve">
    <value>Changes</value>
  </data>
  <data name="DocumentEditor_AcceptAll" xml:space="preserve">
    <value>Accept all</value>
  </data>
  <data name="DocumentEditor_RejectAll" xml:space="preserve">
    <value>Reject all</value>
  </data>
  <data name="DocumentEditor_NoChanges" xml:space="preserve">
    <value>No Changes</value>
  </data>
  <data name="DocumentEditor_AcceptChanges" xml:space="preserve">
    <value>Accept Changes</value>
  </data>
  <data name="DocumentEditor_RejectChanges" xml:space="preserve">
    <value>Reject Changes</value>
  </data>
  <data name="DocumentEditor_User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="DocumentEditor_View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="DocumentEditor_UnprotectDocument" xml:space="preserve">
    <value>Unprotect Document</value>
  </data>
  <data name="DocumentEditor_FormFieldsOnly" xml:space="preserve">
    <value>You may only fill in forms in this region.</value>
  </data>
  <data name="DocumentEditor_IndentsAndSpacing" xml:space="preserve">
    <value>Indents and Spacing</value>
  </data>
  <data name="DocumentEditor_LineAndBreakPage" xml:space="preserve">
    <value>Line and Page Breaks</value>
  </data>
  <data name="DocumentEditor_Pagination" xml:space="preserve">
    <value>Pagination</value>
  </data>
  <data name="DocumentEditor_WidowControl" xml:space="preserve">
    <value>Widow/Orphan control</value>
  </data>
  <data name="DocumentEditor_KeepWithNext" xml:space="preserve">
    <value>Keep with next</value>
  </data>
  <data name="DocumentEditor_KeepLinesTogether" xml:space="preserve">
    <value>Keep lines together</value>
  </data>
  <data name="DocumentEditor_ScreenTip" xml:space="preserve">
    <value>ScreenTip text</value>
  </data>
  <data name="DocumentEditor_KeepSource" xml:space="preserve">
    <value>Keep source formatting</value>
  </data>
  <data name="DocumentEditor_DestinationFormat" xml:space="preserve">
    <value>Match destination formatting</value>
  </data>
  <data name="DocumentEditor_CommentsOnly" xml:space="preserve">
    <value>You may only insert comments into this region.</value>
  </data>
  <data name="DocumentEditor_RowRange" xml:space="preserve">
    <value>Number of rows must be between 1 and 32767</value>
  </data>
  <data name="DocumentEditor_ColumnRange" xml:space="preserve">
    <value>Number of columns must be between 1 and 63</value>
  </data>
  <data name="DocumentEditor_Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="DocumentEditor_Dot" xml:space="preserve">
    <value>Dot</value>
  </data>
  <data name="DocumentEditor_DashSmallGap" xml:space="preserve">
    <value>DashSmallGap</value>
  </data>
  <data name="DocumentEditor_DashDot" xml:space="preserve">
    <value>DashDot</value>
  </data>
  <data name="DocumentEditor_ThinThickSmallGap" xml:space="preserve">
    <value>ThinThickSmallGap</value>
  </data>
  <data name="DocumentEditor_ThickThinSmallGap" xml:space="preserve">
    <value>ThickThinSmallGap</value>
  </data>
  <data name="DocumentEditor_ThickThinMediumGap" xml:space="preserve">
    <value>ThickThinMediumGap</value>
  </data>
  <data name="DocumentEditor_ThickThinLargeGap" xml:space="preserve">
    <value>ThickThinLargeGap</value>
  </data>
  <data name="DocumentEditor_SingleWavy" xml:space="preserve">
    <value>SingleWavy</value>
  </data>
  <data name="DocumentEditor_DoubleWavy" xml:space="preserve">
    <value>DoubleWavy</value>
  </data>
  <data name="DocumentEditor_Inset" xml:space="preserve">
    <value>Inset</value>
  </data>
  <data name="DocumentEditor_DashLargeGap" xml:space="preserve">
    <value>DashLargeGap</value>
  </data>
  <data name="DocumentEditor_DashDotDot" xml:space="preserve">
    <value>DashDotDot</value>
  </data>
  <data name="DocumentEditor_Triple" xml:space="preserve">
    <value>Triple</value>
  </data>
  <data name="DocumentEditor_ThinThickThinSmallGap" xml:space="preserve">
    <value>ThinThickThinSmallGap</value>
  </data>
  <data name="DocumentEditor_ThinThickThinMediumGap" xml:space="preserve">
    <value>ThinThickThinMediumGap</value>
  </data>
  <data name="DocumentEditor_ThinThickThinLargeGap" xml:space="preserve">
    <value>ThinThickThinLargeGap</value>
  </data>
  <data name="DocumentEditor_DashDotStroked" xml:space="preserve">
    <value>DashDotStroked</value>
  </data>
  <data name="DocumentEditor_Engrave3D" xml:space="preserve">
    <value>Engrave3D</value>
  </data>
  <data name="DocumentEditor_Thick" xml:space="preserve">
    <value>Thick</value>
  </data>
  <data name="DocumentEditor_Outset" xml:space="preserve">
    <value>Outset</value>
  </data>
  <data name="DocumentEditor_Emboss3D" xml:space="preserve">
    <value>Emboss3D</value>
  </data>
  <data name="DocumentEditor_ThinThickLargeGap" xml:space="preserve">
    <value>ThinThickLargeGap</value>
  </data>
  <data name="DocumentEditor_ThinThickMediumGap" xml:space="preserve">
    <value>ThinThickMediumGap</value>
  </data>
  <data name="DocumentEditor_RegularText" xml:space="preserve">
    <value>Regular text</value>
  </data>
  <data name="DocumentEditor_Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DocumentEditor_Uppercase" xml:space="preserve">
    <value>Uppercase</value>
  </data>
  <data name="DocumentEditor_Lowercase" xml:space="preserve">
    <value>Lowercase</value>
  </data>
  <data name="DocumentEditor_FirstCapital" xml:space="preserve">
    <value>FirstCapital</value>
  </data>
  <data name="DocumentEditor_Titlecase" xml:space="preserve">
    <value>Titlecase</value>
  </data>
  <data name="DocumentEditor_MoveFrom" xml:space="preserve">
    <value>Move From</value>
  </data>
  <data name="DocumentEditor_MoveTo" xml:space="preserve">
    <value>Move To</value>
  </data>
  <data name="DocumentEditor_Px" xml:space="preserve">
    <value>px</value>
  </data>
  <data name="DocumentEditor_ReadOnlyProtection" xml:space="preserve">
    <value>You may edit in this region.</value>
  </data>
  <data name="DocumentEditor_SectionBreakNextPage" xml:space="preserve">
    <value>Section Break (Next Page)</value>
  </data>
  <data name="DocumentEditor_PageBreak" xml:space="preserve">
    <value>Page Break</value>
  </data>
  <data name="DocumentEditor_ColumnBreak" xml:space="preserve">
    <value>Column Break</value>
  </data>
  <data name="DocumentEditor_One" xml:space="preserve">
    <value>One</value>
  </data>
  <data name="DocumentEditor_Two" xml:space="preserve">
    <value>Two</value>
  </data>
  <data name="DocumentEditor_Three" xml:space="preserve">
    <value>Three</value>
  </data>
  <data name="DocumentEditor_Presets" xml:space="preserve">
    <value>Presets</value>
  </data>
  <data name="DocumentEditor_Columns" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="DocumentEditor_SplitToColumns" xml:space="preserve">
    <value>Split your text into two or more columns</value>
  </data>
  <data name="DocumentEditor_LineBetweenColumn" xml:space="preserve">
    <value>Line between column</value>
  </data>
  <data name="DocumentEditor_WidthAndSpacing" xml:space="preserve">
    <value>Width and Spacing</value>
  </data>
  <data name="DocumentEditor_EqualColumnWidth" xml:space="preserve">
    <value>Equal column width</value>
  </data>
  <data name="DocumentEditor_Column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="DocumentEditor_SectionBreakContinuous" xml:space="preserve">
    <value>Section Break (Continuous)</value>
  </data>
  <data name="DocumentEditor_PasteContentDialog" xml:space="preserve">
    <value>Due to browser’s security policy, paste from system clipboard is restricted. Alternatively use the keyboard shortcut</value>
  </data>
  <data name="DocumentEditor_PasteCheckBoxContentDialog" xml:space="preserve">
    <value>Don't show again</value>
  </data>
  <data name="DocumentEditorContainer_Columns" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="DocumentEditorContainer_ShowHiddenMarks" xml:space="preserve">
    <value>Show the hidden characters like spaces, tab, paragraph marks, and breaks.(Ctrl + *)</value>
  </data>
  <data name="DocumentEditorContainer_RegularTooltip" xml:space="preserve">
    <value>Regular</value>
  </data>
  <data name="DocumentEditorContainer_BoldItalicTooltip" xml:space="preserve">
    <value>BoldItaic</value>
  </data>
  <data name="DocumentEditorContainer_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="DocumentEditorContainer_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="DocumentEditorContainer_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="DocumentEditorContainer_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="DocumentEditorContainer_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="DocumentEditorContainer_Table" xml:space="preserve">
    <value>Table</value>
  </data>
  <data name="DocumentEditorContainer_Link" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="DocumentEditorContainer_Bookmark" xml:space="preserve">
    <value>Bookmark</value>
  </data>
  <data name="DocumentEditorContainer_TableOfContents" xml:space="preserve">
    <value>Table of Contents</value>
  </data>
  <data name="DocumentEditorContainer_HEADING1" xml:space="preserve">
    <value>HEADING - - - - 1</value>
  </data>
  <data name="DocumentEditorContainer_HEADING2" xml:space="preserve">
    <value>HEADING - - - - 2</value>
  </data>
  <data name="DocumentEditorContainer_HEADING3" xml:space="preserve">
    <value>HEADING - - - - 3</value>
  </data>
  <data name="DocumentEditorContainer_Header" xml:space="preserve">
    <value>Header</value>
  </data>
  <data name="DocumentEditorContainer_Footer" xml:space="preserve">
    <value>Footer</value>
  </data>
  <data name="DocumentEditorContainer_PageSetup" xml:space="preserve">
    <value>Page Setup</value>
  </data>
  <data name="DocumentEditorContainer_PageNumber" xml:space="preserve">
    <value>Page Number</value>
  </data>
  <data name="DocumentEditorContainer_Break" xml:space="preserve">
    <value>Break</value>
  </data>
  <data name="DocumentEditorContainer_Find" xml:space="preserve">
    <value>Find</value>
  </data>
  <data name="DocumentEditorContainer_Headings" xml:space="preserve">
    <value>Headings</value>
  </data>
  <data name="DocumentEditorContainer_LocalClipboard" xml:space="preserve">
    <value>Local Clipboard</value>
  </data>
  <data name="DocumentEditorContainer_RestrictEditing" xml:space="preserve">
    <value>Restrict Editing</value>
  </data>
  <data name="DocumentEditorContainer_UploadFromComputer" xml:space="preserve">
    <value>Upload from computer</value>
  </data>
  <data name="DocumentEditorContainer_ByURL" xml:space="preserve">
    <value>By URL</value>
  </data>
  <data name="DocumentEditorContainer_HeaderAndFooter" xml:space="preserve">
    <value>Header and Footer</value>
  </data>
  <data name="DocumentEditorContainer_Options" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="DocumentEditorContainer_Levels" xml:space="preserve">
    <value>Levels</value>
  </data>
  <data name="DocumentEditorContainer_DifferentFirstPage" xml:space="preserve">
    <value>Different First Page</value>
  </data>
  <data name="DocumentEditorContainer_DifferentHeaderAndFooterForOddAndEvenPages" xml:space="preserve">
    <value>Different header and footer for odd and even pages.</value>
  </data>
  <data name="DocumentEditorContainer_DifferentOddAndEvenPages" xml:space="preserve">
    <value>Different Odd and Even Pages</value>
  </data>
  <data name="DocumentEditorContainer_LinkToPreviousPages" xml:space="preserve">
    <value>Link to Previous</value>
  </data>
  <data name="DocumentEditorContainer_DifferentHeaderAndFooterForFirstPage" xml:space="preserve">
    <value>Different header and footer for first page.</value>
  </data>
  <data name="DocumentEditorContainer_Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="DocumentEditorContainer_HeaderFromTop" xml:space="preserve">
    <value>Header from Top</value>
  </data>
  <data name="DocumentEditorContainer_FooterFromBottom" xml:space="preserve">
    <value>Footer from Bottom</value>
  </data>
  <data name="DocumentEditorContainer_DistanceFromTopOfThePageToTopOfTheHeader" xml:space="preserve">
    <value>Distance from top of the page to top of the header.</value>
  </data>
  <data name="DocumentEditorContainer_DistanceFromBottomOfThePageToBottomOfTheFooter" xml:space="preserve">
    <value>Distance from bottom of the page to bottom of the footer.</value>
  </data>
  <data name="DocumentEditorContainer_AspectRatio" xml:space="preserve">
    <value>Aspect ratio</value>
  </data>
  <data name="DocumentEditorContainer_W" xml:space="preserve">
    <value>W</value>
  </data>
  <data name="DocumentEditorContainer_H" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="DocumentEditorContainer_Width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="DocumentEditorContainer_Height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="DocumentEditorContainer_Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="DocumentEditorContainer_AlternateText" xml:space="preserve">
    <value>AlternativeText</value>
  </data>
  <data name="DocumentEditorContainer_Paragraph" xml:space="preserve">
    <value>Paragraph</value>
  </data>
  <data name="DocumentEditorContainer_Fill" xml:space="preserve">
    <value>Fill</value>
  </data>
  <data name="DocumentEditorContainer_FillColor" xml:space="preserve">
    <value>Fill color</value>
  </data>
  <data name="DocumentEditorContainer_BorderStyle" xml:space="preserve">
    <value>Border Style</value>
  </data>
  <data name="DocumentEditorContainer_OutsideBorders" xml:space="preserve">
    <value>Outside borders</value>
  </data>
  <data name="DocumentEditorContainer_AllBorders" xml:space="preserve">
    <value>All borders</value>
  </data>
  <data name="DocumentEditorContainer_InsideBorders" xml:space="preserve">
    <value>Inside borders</value>
  </data>
  <data name="DocumentEditorContainer_LeftBorder" xml:space="preserve">
    <value>Left border</value>
  </data>
  <data name="DocumentEditorContainer_InsideVerticalBorder" xml:space="preserve">
    <value>Inside vertical border</value>
  </data>
  <data name="DocumentEditorContainer_RightBorder" xml:space="preserve">
    <value>Right border</value>
  </data>
  <data name="DocumentEditorContainer_TopBorder" xml:space="preserve">
    <value>Top border</value>
  </data>
  <data name="DocumentEditorContainer_InsideHorizontalBorder" xml:space="preserve">
    <value>Inside horizontal border</value>
  </data>
  <data name="DocumentEditorContainer_BottomBorder" xml:space="preserve">
    <value>Bottom border</value>
  </data>
  <data name="DocumentEditorContainer_BorderColor" xml:space="preserve">
    <value>Border color</value>
  </data>
  <data name="DocumentEditorContainer_BorderWidth" xml:space="preserve">
    <value>Border width</value>
  </data>
  <data name="DocumentEditorContainer_Cell" xml:space="preserve">
    <value>Cell</value>
  </data>
  <data name="DocumentEditorContainer_MergeCells" xml:space="preserve">
    <value>Merge cells</value>
  </data>
  <data name="DocumentEditorContainer_InsertOrDelete" xml:space="preserve">
    <value>Insert / Delete</value>
  </data>
  <data name="DocumentEditorContainer_InsertColumnsToTheLeft" xml:space="preserve">
    <value>Insert columns to the left</value>
  </data>
  <data name="DocumentEditorContainer_InsertColumnsToTheRight" xml:space="preserve">
    <value>Insert columns to the right</value>
  </data>
  <data name="DocumentEditorContainer_InsertRowsAbove" xml:space="preserve">
    <value>Insert rows above</value>
  </data>
  <data name="DocumentEditorContainer_InsertRowsBelow" xml:space="preserve">
    <value>Insert rows below</value>
  </data>
  <data name="DocumentEditorContainer_DeleteRows" xml:space="preserve">
    <value>Delete rows</value>
  </data>
  <data name="DocumentEditorContainer_DeleteColumns" xml:space="preserve">
    <value>Delete columns</value>
  </data>
  <data name="DocumentEditorContainer_CellMargin" xml:space="preserve">
    <value>Cell Margin</value>
  </data>
  <data name="DocumentEditorContainer_Top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="DocumentEditorContainer_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="DocumentEditorContainer_Left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="DocumentEditorContainer_Right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="DocumentEditorContainer_AlignText" xml:space="preserve">
    <value>Align Text</value>
  </data>
  <data name="DocumentEditorContainer_Aligntop" xml:space="preserve">
    <value>Align top</value>
  </data>
  <data name="DocumentEditorContainer_AlignBottom" xml:space="preserve">
    <value>Align bottom</value>
  </data>
  <data name="DocumentEditorContainer_AlignCenter" xml:space="preserve">
    <value>Align center</value>
  </data>
  <data name="DocumentEditorContainer_NumberOfHeadingOrOutlineLevelsToBeShownInTableOfContents" xml:space="preserve">
    <value>Number of heading or outline levels to be shown in table of contents.</value>
  </data>
  <data name="DocumentEditorContainer_ShowPageNumbers" xml:space="preserve">
    <value>Show page numbers</value>
  </data>
  <data name="DocumentEditorContainer_ShowPageNumbersInTableOfContents" xml:space="preserve">
    <value>Show page numbers in table of contents.</value>
  </data>
  <data name="DocumentEditorContainer_RightAlignPageNumbers" xml:space="preserve">
    <value>Right align page numbers.</value>
  </data>
  <data name="DocumentEditorContainer_RightAlignPageNumbersInTableOfContents" xml:space="preserve">
    <value>Right align page numbers in table of contents.</value>
  </data>
  <data name="DocumentEditorContainer_UseHyperlinks" xml:space="preserve">
    <value>Use hyperlinks</value>
  </data>
  <data name="DocumentEditorContainer_UseHyperlinksInsteadOfPageNumbers" xml:space="preserve">
    <value>Use hyperlinks instead of page numbers.</value>
  </data>
  <data name="DocumentEditorContainer_Font" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="DocumentEditorContainer_FontSize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="DocumentEditorContainer_FontColor" xml:space="preserve">
    <value>Font color</value>
  </data>
  <data name="DocumentEditorContainer_TextHighlightColor" xml:space="preserve">
    <value>Text highlight color</value>
  </data>
  <data name="DocumentEditorContainer_ClearAllFormatting" xml:space="preserve">
    <value>Clear all formatting</value>
  </data>
  <data name="DocumentEditorContainer_BoldTooltip" xml:space="preserve">
    <value>Bold (Ctrl+B)</value>
  </data>
  <data name="DocumentEditorContainer_ItalicTooltip" xml:space="preserve">
    <value>Italic (Ctrl+I)</value>
  </data>
  <data name="DocumentEditorContainer_UnderlineTooltip" xml:space="preserve">
    <value>Underline (Ctrl+U)</value>
  </data>
  <data name="DocumentEditorContainer_Strikethrough" xml:space="preserve">
    <value>Strikethrough</value>
  </data>
  <data name="DocumentEditorContainer_SuperscriptTooltip" xml:space="preserve">
    <value>Superscript (Ctrl+Shift++)</value>
  </data>
  <data name="DocumentEditorContainer_SubscriptTooltip" xml:space="preserve">
    <value>Subscript (Ctrl+=)</value>
  </data>
  <data name="DocumentEditorContainer_AlignLeftTooltip" xml:space="preserve">
    <value>Align left (Ctrl+L)</value>
  </data>
  <data name="DocumentEditorContainer_CenterTooltip" xml:space="preserve">
    <value>Center (Ctrl+E)</value>
  </data>
  <data name="DocumentEditorContainer_AlignRightTooltip" xml:space="preserve">
    <value>Align right (Ctrl+R)</value>
  </data>
  <data name="DocumentEditorContainer_JustifyTooltip" xml:space="preserve">
    <value>Justify (Ctrl+J)</value>
  </data>
  <data name="DocumentEditorContainer_DecreaseIndent" xml:space="preserve">
    <value>Decrease indent</value>
  </data>
  <data name="DocumentEditorContainer_IncreaseIndent" xml:space="preserve">
    <value>Increase indent</value>
  </data>
  <data name="DocumentEditorContainer_LineSpacing" xml:space="preserve">
    <value>Line spacing</value>
  </data>
  <data name="DocumentEditorContainer_Bullets" xml:space="preserve">
    <value>Bullets</value>
  </data>
  <data name="DocumentEditorContainer_Numbering" xml:space="preserve">
    <value>Numbering</value>
  </data>
  <data name="DocumentEditorContainer_Styles" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="DocumentEditorContainer_ManageStyles" xml:space="preserve">
    <value>Manage Styles</value>
  </data>
  <data name="DocumentEditorContainer_Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="DocumentEditorContainer_Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="DocumentEditorContainer_FitOnePage" xml:space="preserve">
    <value>Fit one page</value>
  </data>
  <data name="DocumentEditorContainer_SpellCheck" xml:space="preserve">
    <value>Spell Check</value>
  </data>
  <data name="DocumentEditorContainer_UnderlineErrors" xml:space="preserve">
    <value>Underline errors</value>
  </data>
  <data name="DocumentEditorContainer_FitPageWidth" xml:space="preserve">
    <value>Fit page width</value>
  </data>
  <data name="DocumentEditorContainer_Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="DocumentEditorContainer_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DocumentEditorContainer_Insert" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="DocumentEditorContainer_NoBorder" xml:space="preserve">
    <value>No Border</value>
  </data>
  <data name="DocumentEditorContainer_CreateANewDocument" xml:space="preserve">
    <value>Create a new document.</value>
  </data>
  <data name="DocumentEditorContainer_OpenADocument" xml:space="preserve">
    <value>Open a document.</value>
  </data>
  <data name="DocumentEditorContainer_UndoTooltip" xml:space="preserve">
    <value>Undo the last operation (Ctrl+Z).</value>
  </data>
  <data name="DocumentEditorContainer_RedoTooltip" xml:space="preserve">
    <value>Redo the last operation (Ctrl+Y).</value>
  </data>
  <data name="DocumentEditorContainer_InsertInlinePictureFromAFile" xml:space="preserve">
    <value>Insert inline picture from a file.</value>
  </data>
  <data name="DocumentEditorContainer_InsertATableIntoTheDocument" xml:space="preserve">
    <value>Insert a table into the document.</value>
  </data>
  <data name="DocumentEditorContainer_CreateHyperlink" xml:space="preserve">
    <value>Create a link in your document for quick access to web pages and files (Ctrl+K).</value>
  </data>
  <data name="DocumentEditorContainer_InsertABookmarkInASpecificPlaceInThisDocument" xml:space="preserve">
    <value>Insert a bookmark in a specific place in this document.</value>
  </data>
  <data name="DocumentEditorContainer_ProvideAnOverviewOfYourDocumentByAddingATableOfContents" xml:space="preserve">
    <value>Provide an overview of your document by adding a table of contents.</value>
  </data>
  <data name="DocumentEditorContainer_AddOrEditTheHeader" xml:space="preserve">
    <value>Add or edit the header.</value>
  </data>
  <data name="DocumentEditorContainer_AddOrEditTheFooter" xml:space="preserve">
    <value>Add or edit the footer.</value>
  </data>
  <data name="DocumentEditorContainer_OpenThePageSetupDialog" xml:space="preserve">
    <value>Open the page setup dialog.</value>
  </data>
  <data name="DocumentEditorContainer_AddPageNumbers" xml:space="preserve">
    <value>Add page numbers.</value>
  </data>
  <data name="DocumentEditorContainer_FindText" xml:space="preserve">
    <value>Find text in the document (Ctrl+F).</value>
  </data>
  <data name="DocumentEditorContainer_CurrentPageNumber" xml:space="preserve">
    <value>The current page number in the document. Click or tap to navigate specific page.</value>
  </data>
  <data name="DocumentEditorContainer_ReadOnly" xml:space="preserve">
    <value>Read only</value>
  </data>
  <data name="DocumentEditorContainer_Protections" xml:space="preserve">
    <value>Protections</value>
  </data>
  <data name="DocumentEditorContainer_ErrorInEstablishingConnectionWithWebServer" xml:space="preserve">
    <value>Error in establishing connection with web server</value>
  </data>
  <data name="DocumentEditorContainer_Single" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="DocumentEditorContainer_Double" xml:space="preserve">
    <value>Double</value>
  </data>
  <data name="DocumentEditorContainer_NewComment" xml:space="preserve">
    <value>New comment</value>
  </data>
  <data name="DocumentEditorContainer_Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="DocumentEditorContainer_PrintLayout" xml:space="preserve">
    <value>Print layout</value>
  </data>
  <data name="DocumentEditorContainer_WebLayout" xml:space="preserve">
    <value>Web layout</value>
  </data>
  <data name="DocumentEditorContainer_ToggleBetweenTheInternalClipboardAndSystemClipboard" xml:space="preserve">
    <value>Toggle between the internal clipboard and system clipboard. Access to system clipboard through script is denied due to browsers security policy. Instead,
1. You can enable internal clipboard to cut, copy and paste within the component.
2. You can use the keyboard shortcuts (Ctrl+X, Ctrl+C and Ctrl+V) to cut, copy and paste with system clipboard.</value>
  </data>
  <data name="DocumentEditorContainer_TextForm" xml:space="preserve">
    <value>Text Form</value>
  </data>
  <data name="DocumentEditorContainer_CheckBox" xml:space="preserve">
    <value>Check Box</value>
  </data>
  <data name="DocumentEditorContainer_DropDown" xml:space="preserve">
    <value>Drop-Down</value>
  </data>
  <data name="DocumentEditorContainer_UpdateFields" xml:space="preserve">
    <value>Update Fields</value>
  </data>
  <data name="DocumentEditorContainer_UpdateCrossReferenceFields" xml:space="preserve">
    <value>Update cross reference fields.</value>
  </data>
  <data name="DocumentEditorContainer_Track_changes" xml:space="preserve">
    <value>Keep track of the changes made in the document.</value>
  </data>
  <data name="DocumentEditorContainer_TrackChanges" xml:space="preserve">
    <value>Track Changes</value>
  </data>
  <data name="DocumentEditorContainer_ZoomLevel" xml:space="preserve">
    <value>Zoom Level</value>
  </data>
  <data name="DocumentEditorContainer_Footnote_Tooltip" xml:space="preserve">
    <value>Insert Footnote (Alt+Ctrl+F).</value>
  </data>
  <data name="DocumentEditorContainer_Endnote_Tooltip" xml:space="preserve">
    <value>Insert Endnote (Alt+Ctrl+D).</value>
  </data>
  <data name="DocumentEditorContainer_ShowComments" xml:space="preserve">
    <value>Insert Comments</value>
  </data>
  <data name="DocumentEditorContainer_FormFields" xml:space="preserve">
    <value>Form Fields</value>
  </data>
  <data name="DocumentEditorContainer_Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="DocumentEditorContainer_AllCaps" xml:space="preserve">
    <value>All caps</value>
  </data>
  <data name="DocumentEditorContainer_Insert_Footnote" xml:space="preserve">
    <value>Insert Footnote</value>
  </data>
  <data name="DocumentEditorContainer_Insert_Endnote" xml:space="preserve">
    <value>Insert Endnote</value>
  </data>
  <data name="DocumentEditorContainer_Changecase" xml:space="preserve">
    <value>Change case</value>
  </data>
  <data name="DocumentEditorContainer_Uppercase" xml:space="preserve">
    <value>UPPERCASE</value>
  </data>
  <data name="DocumentEditorContainer_Lowercase" xml:space="preserve">
    <value>Lowercase</value>
  </data>
  <data name="DocumentEditorContainer_SentenceCase" xml:space="preserve">
    <value>Sentence case</value>
  </data>
  <data name="DocumentEditorContainer_CapitalizeEachWord" xml:space="preserve">
    <value>Capitalize Each Word</value>
  </data>
  <data name="DocumentEditorContainer_ToggleCase" xml:space="preserve">
    <value>Toggle case</value>
  </data>
  <data name="PivotView_AddCondition" xml:space="preserve">
    <value>Add Condition</value>
  </data>
  <data name="PivotView_AddToColumn" xml:space="preserve">
    <value>Add to Column</value>
  </data>
  <data name="PivotView_AddToFilter" xml:space="preserve">
    <value>Add to Filter</value>
  </data>
  <data name="PivotView_AddToRow" xml:space="preserve">
    <value>Add to Row</value>
  </data>
  <data name="PivotView_AddToValue" xml:space="preserve">
    <value>Add to Value</value>
  </data>
  <data name="PivotView_After" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="PivotView_AfterOrEqualTo" xml:space="preserve">
    <value>After Or Equal To</value>
  </data>
  <data name="PivotView_Aggregate" xml:space="preserve">
    <value>Aggregate</value>
  </data>
  <data name="PivotView_Alert" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="PivotView_All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="PivotView_AllFields" xml:space="preserve">
    <value>All Fields</value>
  </data>
  <data name="PivotView_AllValues" xml:space="preserve">
    <value>All Values</value>
  </data>
  <data name="PivotView_And" xml:space="preserve">
    <value>and</value>
  </data>
  <data name="PivotView_Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="PivotView_Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="PivotView_Ascending" xml:space="preserve">
    <value>Ascending</value>
  </data>
  <data name="PivotView_Avg" xml:space="preserve">
    <value>Avg</value>
  </data>
  <data name="PivotView_Bar" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="PivotView_BaseField" xml:space="preserve">
    <value>Base field</value>
  </data>
  <data name="PivotView_BaseItem" xml:space="preserve">
    <value>Base item</value>
  </data>
  <data name="PivotView_Before" xml:space="preserve">
    <value>Before</value>
  </data>
  <data name="PivotView_BeforeOrEqualTo" xml:space="preserve">
    <value>Before Or Equal To</value>
  </data>
  <data name="PivotView_BeginWith" xml:space="preserve">
    <value>Begins With</value>
  </data>
  <data name="PivotView_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="PivotView_Blank" xml:space="preserve">
    <value>(Blank)</value>
  </data>
  <data name="PivotView_By" xml:space="preserve">
    <value>by</value>
  </data>
  <data name="PivotView_CalculatedField" xml:space="preserve">
    <value>Calculated Field</value>
  </data>
  <data name="PivotView_CalculatedField_ConfirmMessage" xml:space="preserve">
    <value>A calculation field already exists in this name. Do you want to replace it?</value>
  </data>
  <data name="PivotView_CalculatedField_DragDropMessage" xml:space="preserve">
    <value>Drag and drop fields to formula</value>
  </data>
  <data name="PivotView_CalculatedField_DragMessage" xml:space="preserve">
    <value>Drag field to formula</value>
  </data>
  <data name="PivotView_CalculatedField_DropMessage" xml:space="preserve">
    <value>Calculated field cannot be place in any other region except value axis.</value>
  </data>
  <data name="PivotView_CalculatedField_ExampleWatermark" xml:space="preserve">
    <value>Example: ('Sum(Order_Count)' + 'Sum(In_Stock)') * 250</value>
  </data>
  <data name="PivotView_CalculatedField_ExistMessage" xml:space="preserve">
    <value>A field already exists in this name. Please enter a different name.</value>
  </data>
  <data name="PivotView_CalculatedField_MobileWatermark" xml:space="preserve">
    <value>Add fields and edit formula here.</value>
  </data>
  <data name="PivotView_CalculatedField_NameWatermark" xml:space="preserve">
    <value>Enter the field name</value>
  </data>
  <data name="PivotView_CalculatedField_OLAPExampleWatermark" xml:space="preserve">
    <value>Example: [Measures].[Order Quantity] + ([Measures].[Order Quantity] * 0.10)</value>
  </data>
  <data name="PivotView_CalculatedField_Tooltip" xml:space="preserve">
    <value>Drag and drop fields to create an expression. And, if you want to edit the existing the calculated fields! You can achieve it by simply selecting the field under 'Calculated Members'.</value>
  </data>
  <data name="PivotView_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PivotView_Chart" xml:space="preserve">
    <value>Chart</value>
  </data>
  <data name="PivotView_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="PivotView_ClearFilter" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="PivotView_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="PivotView_Collapse" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="PivotView_Column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="PivotView_ColumnAxisWatermark" xml:space="preserve">
    <value>Drop column here</value>
  </data>
  <data name="PivotView_Columns" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="PivotView_ConditionalFormating" xml:space="preserve">
    <value>Conditional Formatting</value>
  </data>
  <data name="PivotView_ConditionalFormatting" xml:space="preserve">
    <value>Conditional formatting</value>
  </data>
  <data name="PivotView_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="PivotView_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="PivotView_Count" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="PivotView_CreateCalculatedField" xml:space="preserve">
    <value>Create Calculated Field</value>
  </data>
  <data name="PivotView_CSV" xml:space="preserve">
    <value>CSV</value>
  </data>
  <data name="PivotView_Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="PivotView_CurrencySymbol" xml:space="preserve">
    <value>Currency Symbol</value>
  </data>
  <data name="PivotView_Custom" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="PivotView_CustomFormat" xml:space="preserve">
    <value>Custom Format</value>
  </data>
  <data name="PivotView_CustomFormatMessage" xml:space="preserve">
    <value>Enter custom format string</value>
  </data>
  <data name="PivotView_Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="PivotView_DateTextMessage" xml:space="preserve">
    <value>Show the items for which the date</value>
  </data>
  <data name="PivotView_Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="PivotView_DecimalPlaces" xml:space="preserve">
    <value>Decimal Places</value>
  </data>
  <data name="PivotView_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="PivotView_DeleteReport" xml:space="preserve">
    <value>Delete a current report</value>
  </data>
  <data name="PivotView_Descending" xml:space="preserve">
    <value>Descending</value>
  </data>
  <data name="PivotView_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="PivotView_DifferenceFrom" xml:space="preserve">
    <value>Difference From</value>
  </data>
  <data name="PivotView_Dimension" xml:space="preserve">
    <value>Dimension</value>
  </data>
  <data name="PivotView_DistinctCount" xml:space="preserve">
    <value>Distinct Count</value>
  </data>
  <data name="PivotView_DoesNotBeginWith" xml:space="preserve">
    <value>Does Not Begin With</value>
  </data>
  <data name="PivotView_DoesNotContains" xml:space="preserve">
    <value>Does Not Contain</value>
  </data>
  <data name="PivotView_DoesNotEndsWith" xml:space="preserve">
    <value>Does Not End With</value>
  </data>
  <data name="PivotView_DoesNotEquals" xml:space="preserve">
    <value>Does Not Equal</value>
  </data>
  <data name="PivotView_DoNotShowGrandTotals" xml:space="preserve">
    <value>Do not show grand totals</value>
  </data>
  <data name="PivotView_Drag" xml:space="preserve">
    <value>Drag</value>
  </data>
  <data name="PivotView_DrillThrough" xml:space="preserve">
    <value>Drill Through</value>
  </data>
  <data name="PivotView_DrillThroughErrorMessage" xml:space="preserve">
    <value>Cannot show the raw items of calculated fields.</value>
  </data>
  <data name="PivotView_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="PivotView_EmptyRecordsMessage" xml:space="preserve">
    <value>No records to display</value>
  </data>
  <data name="PivotView_EmptyReportMessage" xml:space="preserve">
    <value>No reports found!!</value>
  </data>
  <data name="PivotView_EndAt" xml:space="preserve">
    <value>Ending at</value>
  </data>
  <data name="PivotView_EndsWith" xml:space="preserve">
    <value>Ends With</value>
  </data>
  <data name="PivotView_EnterDate" xml:space="preserve">
    <value>Enter date</value>
  </data>
  <data name="PivotView_EnterReportNameMessage" xml:space="preserve">
    <value>Enter a report name</value>
  </data>
  <data name="PivotView_EnterValue" xml:space="preserve">
    <value>Enter value</value>
  </data>
  <data name="PivotView_Equals" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="PivotView_Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="PivotView_Example" xml:space="preserve">
    <value>e.g:</value>
  </data>
  <data name="PivotView_Excel" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="PivotView_Expand" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="PivotView_Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="PivotView_Expression" xml:space="preserve">
    <value>Expression</value>
  </data>
  <data name="PivotView_False" xml:space="preserve">
    <value>False</value>
  </data>
  <data name="PivotView_FieldCaption" xml:space="preserve">
    <value>Field Caption</value>
  </data>
  <data name="PivotView_FieldCaptionMessage" xml:space="preserve">
    <value>Field caption</value>
  </data>
  <data name="PivotView_FieldDropErrorMessage" xml:space="preserve">
    <value>The field you are moving cannot be placed in that area of the report</value>
  </data>
  <data name="PivotView_FieldName" xml:space="preserve">
    <value>Field Name</value>
  </data>
  <data name="PivotView_FieldNameMessage" xml:space="preserve">
    <value>Field name :</value>
  </data>
  <data name="PivotView_FieldType" xml:space="preserve">
    <value>Field Type</value>
  </data>
  <data name="PivotView_Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="PivotView_FilterAxisWatermark" xml:space="preserve">
    <value>Drop filter here</value>
  </data>
  <data name="PivotView_Filtered" xml:space="preserve">
    <value>Filtered</value>
  </data>
  <data name="PivotView_Filters" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="PivotView_Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="PivotView_FormatString" xml:space="preserve">
    <value>Format String</value>
  </data>
  <data name="PivotView_FormatType" xml:space="preserve">
    <value>Format Type</value>
  </data>
  <data name="PivotView_Formula" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="PivotView_GrandTotal" xml:space="preserve">
    <value>Grand Total</value>
  </data>
  <data name="PivotView_GrandTotals" xml:space="preserve">
    <value>Grand totals</value>
  </data>
  <data name="PivotView_GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="PivotView_GreaterThanOrEqualTo" xml:space="preserve">
    <value>Greater Than Or Equal To</value>
  </data>
  <data name="PivotView_Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="PivotView_GroupCaptionMessage" xml:space="preserve">
    <value>Enter the caption to display in header</value>
  </data>
  <data name="PivotView_GroupFieldCaptionMessage" xml:space="preserve">
    <value>Enter the caption for group field</value>
  </data>
  <data name="PivotView_Grouping" xml:space="preserve">
    <value>Grouping</value>
  </data>
  <data name="PivotView_GroupName" xml:space="preserve">
    <value>Group name</value>
  </data>
  <data name="PivotView_Hours" xml:space="preserve">
    <value>Hours</value>
  </data>
  <data name="PivotView_Index" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="PivotView_IntervalBy" xml:space="preserve">
    <value>Interval by</value>
  </data>
  <data name="PivotView_InvalidFormat" xml:space="preserve">
    <value>Invalid Format.</value>
  </data>
  <data name="PivotView_InvalidFormula" xml:space="preserve">
    <value>Invalid formula.</value>
  </data>
  <data name="PivotView_InvalidGroupSelectionMessage" xml:space="preserve">
    <value>Cannot group that selection.</value>
  </data>
  <data name="PivotView_JPEG" xml:space="preserve">
    <value>JPEG</value>
  </data>
  <data name="PivotView_Label" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="PivotView_LabelTextMessage" xml:space="preserve">
    <value>Show the items for which the label</value>
  </data>
  <data name="PivotView_Left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="PivotView_LessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="PivotView_LessThanOrEqualTo" xml:space="preserve">
    <value>Less Than Or Equal To</value>
  </data>
  <data name="PivotView_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="PivotView_LoadReport" xml:space="preserve">
    <value>Load</value>
  </data>
  <data name="PivotView_Loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="PivotView_ManageRecords" xml:space="preserve">
    <value>Manage Records</value>
  </data>
  <data name="PivotView_Max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="PivotView_MdxQuery" xml:space="preserve">
    <value>MDX Query</value>
  </data>
  <data name="PivotView_Measure" xml:space="preserve">
    <value>Measure</value>
  </data>
  <data name="PivotView_Member" xml:space="preserve">
    <value>Member</value>
  </data>
  <data name="PivotView_MemberLimitMessage" xml:space="preserve">
    <value>more items. Search to refine further.</value>
  </data>
  <data name="PivotView_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="PivotView_Minutes" xml:space="preserve">
    <value>Minutes</value>
  </data>
  <data name="PivotView_Months" xml:space="preserve">
    <value>Months</value>
  </data>
  <data name="PivotView_MoreOption" xml:space="preserve">
    <value>More...</value>
  </data>
  <data name="PivotView_MultipleItems" xml:space="preserve">
    <value>Multiple items</value>
  </data>
  <data name="PivotView_NewReport" xml:space="preserve">
    <value>Create a new report</value>
  </data>
  <data name="PivotView_NewReportConfirmMessage" xml:space="preserve">
    <value>Do you want to save changes to this report?</value>
  </data>
  <data name="PivotView_NoFormatMessage" xml:space="preserve">
    <value>No format found!!!</value>
  </data>
  <data name="PivotView_NoInputMessage" xml:space="preserve">
    <value>Enter a value</value>
  </data>
  <data name="PivotView_NoMatchesMessage" xml:space="preserve">
    <value>No matches</value>
  </data>
  <data name="PivotView_NotBetween" xml:space="preserve">
    <value>Not Between</value>
  </data>
  <data name="PivotView_NotEquals" xml:space="preserve">
    <value>Not Equals</value>
  </data>
  <data name="PivotView_NoValue" xml:space="preserve">
    <value>No value</value>
  </data>
  <data name="PivotView_Null" xml:space="preserve">
    <value>null</value>
  </data>
  <data name="PivotView_Number" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="PivotView_NumberFormatting" xml:space="preserve">
    <value>Number Formatting</value>
  </data>
  <data name="PivotView_Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="PivotView_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PivotView_OutOfRange" xml:space="preserve">
    <value>Out of Range</value>
  </data>
  <data name="PivotView_ParentHierarchy" xml:space="preserve">
    <value>Parent Hierarchy</value>
  </data>
  <data name="PivotView_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="PivotView_Percent" xml:space="preserve">
    <value>Percent</value>
  </data>
  <data name="PivotView_Percentage" xml:space="preserve">
    <value>Percentage</value>
  </data>
  <data name="PivotView_PercentageOfColumnTotal" xml:space="preserve">
    <value>% of Column Total</value>
  </data>
  <data name="PivotView_PercentageOfDifferenceFrom" xml:space="preserve">
    <value>% of Difference From</value>
  </data>
  <data name="PivotView_PercentageOfGrandTotal" xml:space="preserve">
    <value>% of Grand Total</value>
  </data>
  <data name="PivotView_PercentageOfParentColumnTotal" xml:space="preserve">
    <value>% of Parent Column Total</value>
  </data>
  <data name="PivotView_PercentageOfParentRowTotal" xml:space="preserve">
    <value>% of Parent Row Total</value>
  </data>
  <data name="PivotView_PercentageOfParentTotal" xml:space="preserve">
    <value>% of Parent Total</value>
  </data>
  <data name="PivotView_PercentageOfRowTotal" xml:space="preserve">
    <value>% of Row Total</value>
  </data>
  <data name="PivotView_PNG" xml:space="preserve">
    <value>PNG</value>
  </data>
  <data name="PivotView_Polar" xml:space="preserve">
    <value>Polar</value>
  </data>
  <data name="PivotView_PopulationStDev" xml:space="preserve">
    <value>Population StDev</value>
  </data>
  <data name="PivotView_PopulationVar" xml:space="preserve">
    <value>Population Var</value>
  </data>
  <data name="PivotView_Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="PivotView_Quarter" xml:space="preserve">
    <value>Qtr</value>
  </data>
  <data name="PivotView_Quarters" xml:space="preserve">
    <value>Quarters</value>
  </data>
  <data name="PivotView_QuarterYear" xml:space="preserve">
    <value>Quarter Year</value>
  </data>
  <data name="PivotView_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="PivotView_RemoveReportConfirmMessage" xml:space="preserve">
    <value>Are you sure want to delete this report?</value>
  </data>
  <data name="PivotView_RenameReport" xml:space="preserve">
    <value>Rename a current report</value>
  </data>
  <data name="PivotView_ReportList" xml:space="preserve">
    <value>Report list</value>
  </data>
  <data name="PivotView_ReportNameMessage" xml:space="preserve">
    <value>Report Name :</value>
  </data>
  <data name="PivotView_Right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="PivotView_Row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="PivotView_RowAxisWatermark" xml:space="preserve">
    <value>Drop row here</value>
  </data>
  <data name="PivotView_Rows" xml:space="preserve">
    <value>Rows</value>
  </data>
  <data name="PivotView_RunningTotals" xml:space="preserve">
    <value>Running Totals</value>
  </data>
  <data name="PivotView_SampleReport" xml:space="preserve">
    <value>Sample Report</value>
  </data>
  <data name="PivotView_SampleStDev" xml:space="preserve">
    <value>Sample StDev</value>
  </data>
  <data name="PivotView_SampleVar" xml:space="preserve">
    <value>Sample Var</value>
  </data>
  <data name="PivotView_SaveAsReport" xml:space="preserve">
    <value>Save as current report</value>
  </data>
  <data name="PivotView_SaveReport" xml:space="preserve">
    <value>Save a report</value>
  </data>
  <data name="PivotView_Scatter" xml:space="preserve">
    <value>Scatter</value>
  </data>
  <data name="PivotView_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="PivotView_Seconds" xml:space="preserve">
    <value>Seconds</value>
  </data>
  <data name="PivotView_SelectedItems" xml:space="preserve">
    <value>Selected items</value>
  </data>
  <data name="PivotView_SelectGroups" xml:space="preserve">
    <value>Select groups</value>
  </data>
  <data name="PivotView_ShowColumnGrandTotalsOnly" xml:space="preserve">
    <value>Show grand totals columns only</value>
  </data>
  <data name="PivotView_ShowFieldList" xml:space="preserve">
    <value>Show field list</value>
  </data>
  <data name="PivotView_ShowGrandTotals" xml:space="preserve">
    <value>Show grand totals</value>
  </data>
  <data name="PivotView_ShowRowGrandTotalsOnly" xml:space="preserve">
    <value>Show grand totals rows only</value>
  </data>
  <data name="PivotView_ShowTable" xml:space="preserve">
    <value>Show table</value>
  </data>
  <data name="PivotView_Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="PivotView_Standard" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="PivotView_StartAt" xml:space="preserve">
    <value>Starting at</value>
  </data>
  <data name="PivotView_Subtotals" xml:space="preserve">
    <value>Sub totals</value>
  </data>
  <data name="PivotView_Sum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="PivotView_Summaries" xml:space="preserve">
    <value>Summaries values by</value>
  </data>
  <data name="PivotView_SummarizeValuesBy" xml:space="preserve">
    <value>Summarize values by</value>
  </data>
  <data name="PivotView_SVG" xml:space="preserve">
    <value>SVG</value>
  </data>
  <data name="PivotView_SymbolPosition" xml:space="preserve">
    <value>Symbol Position</value>
  </data>
  <data name="PivotView_Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="PivotView_True" xml:space="preserve">
    <value>True</value>
  </data>
  <data name="PivotView_Undefined" xml:space="preserve">
    <value>undefined</value>
  </data>
  <data name="PivotView_Ungroup" xml:space="preserve">
    <value>Ungroup</value>
  </data>
  <data name="PivotView_ValidReportNameMessage" xml:space="preserve">
    <value>Please enter vaild report name!!!</value>
  </data>
  <data name="PivotView_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="PivotView_ValueAxisWatermark" xml:space="preserve">
    <value>Drop value here</value>
  </data>
  <data name="PivotView_ValueFieldSettings" xml:space="preserve">
    <value>Value field settings</value>
  </data>
  <data name="PivotView_Values" xml:space="preserve">
    <value>Values</value>
  </data>
  <data name="PivotView_ValueTextMessage" xml:space="preserve">
    <value>Show the items for which</value>
  </data>
  <data name="PivotView_Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="PivotView_Years" xml:space="preserve">
    <value>Years</value>
  </data>
  <data name="PivotView_MultipleAxes" xml:space="preserve">
    <value>Multiple Axis</value>
  </data>
  <data name="PivotView_ChartTypeSettings" xml:space="preserve">
    <value>Chart Type Settings</value>
  </data>
  <data name="PivotView_ChartType" xml:space="preserve">
    <value>Chart Type</value>
  </data>
  <data name="PivotView_Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="PivotView_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="PivotView_NumberFormatMenu" xml:space="preserve">
    <value>Number Formatting...</value>
  </data>
  <data name="PivotView_ConditionalFormatingMenu" xml:space="preserve">
    <value>Conditional Formatting...</value>
  </data>
  <data name="PivotView_CalculatedField_RemoveMessage" xml:space="preserve">
    <value>Are you sure you want to delete this calculated field?</value>
  </data>
  <data name="PivotView_StackedArea" xml:space="preserve">
    <value>Stacked Area</value>
  </data>
  <data name="PivotView_StackedColumn" xml:space="preserve">
    <value>Stacked Column</value>
  </data>
  <data name="PivotView_StackedBar" xml:space="preserve">
    <value>Stacked Bar</value>
  </data>
  <data name="PivotView_StepLine" xml:space="preserve">
    <value>Step Line</value>
  </data>
  <data name="PivotView_StepArea" xml:space="preserve">
    <value>Step Area</value>
  </data>
  <data name="PivotView_SplineArea" xml:space="preserve">
    <value>Spline Area</value>
  </data>
  <data name="PivotView_Spline" xml:space="preserve">
    <value>Spline</value>
  </data>
  <data name="PivotView_StackedColumn100" xml:space="preserve">
    <value>100% Stacked Column</value>
  </data>
  <data name="PivotView_StackedBar100" xml:space="preserve">
    <value>100% Stacked Bar</value>
  </data>
  <data name="PivotView_StackedArea100" xml:space="preserve">
    <value>100% Stacked Area</value>
  </data>
  <data name="PivotView_Bubble" xml:space="preserve">
    <value>Bubble</value>
  </data>
  <data name="PivotView_Pareto" xml:space="preserve">
    <value>Pareto</value>
  </data>
  <data name="PivotView_Radar" xml:space="preserve">
    <value>Radar</value>
  </data>
  <data name="PivotView_CalculatedField_EditTooltipMessage" xml:space="preserve">
    <value>Edit calculated field</value>
  </data>
  <data name="PivotView_CalculatedField_ClearTooltipMessage" xml:space="preserve">
    <value>Clear edited field info</value>
  </data>
  <data name="PivotView_NumberFormat_ExampleWatermark" xml:space="preserve">
    <value>Example: C, P, 0000 %, ###0.##0#, etc.</value>
  </data>
  <data name="PivotView_ShowLegend" xml:space="preserve">
    <value>Show Legend</value>
  </data>
  <data name="PivotView_FieldCaptionWatermark" xml:space="preserve">
    <value>Enter the field caption</value>
  </data>
  <data name="PivotView_SortNone_TooltipMessage" xml:space="preserve">
    <value>Sort data order</value>
  </data>
  <data name="PivotView_SortAscending_TooltipMessage" xml:space="preserve">
    <value>Sort ascending order</value>
  </data>
  <data name="PivotView_SortDescending_TooltipMessage" xml:space="preserve">
    <value>Sort descending order</value>
  </data>
  <data name="PivotView_ReplaceReport_BeforeMessage" xml:space="preserve">
    <value>A report named</value>
  </data>
  <data name="PivotView_ReplaceReport_AfterMessage" xml:space="preserve">
    <value>already exists. Do you want to replace it?</value>
  </data>
  <data name="PivotView_StaticFieldList" xml:space="preserve">
    <value>Pivot Field List</value>
  </data>
  <data name="PivotView_FieldList" xml:space="preserve">
    <value>Field List</value>
  </data>
  <data name="PivotView_AddFieldMessage" xml:space="preserve">
    <value>Add field here</value>
  </data>
  <data name="PivotView_ChooseFieldMessage" xml:space="preserve">
    <value>Choose field</value>
  </data>
  <data name="PivotView_DragFieldsMessage" xml:space="preserve">
    <value>Drag fields between axes below:</value>
  </data>
  <data name="PivotView_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="PivotView_DeferLayoutUpdate" xml:space="preserve">
    <value>Defer Layout Update</value>
  </data>
  <data name="PivotView_Pie" xml:space="preserve">
    <value>Pie</value>
  </data>
  <data name="PivotView_Pyramid" xml:space="preserve">
    <value>Pyramid</value>
  </data>
  <data name="PivotView_Doughnut" xml:space="preserve">
    <value>Doughnut</value>
  </data>
  <data name="PivotView_Funnel" xml:space="preserve">
    <value>Funnel</value>
  </data>
  <data name="PivotView_Stacked" xml:space="preserve">
    <value>Stacked</value>
  </data>
  <data name="PivotView_MultipleAxisMode" xml:space="preserve">
    <value>Multiple Axis Mode</value>
  </data>
  <data name="PivotView_Median" xml:space="preserve">
    <value>Median</value>
  </data>
  <data name="PivotView_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="PivotView_GrandTotalPositions" xml:space="preserve">
    <value>Grand totals position</value>
  </data>
  <data name="PivotView_Top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="Maps_ImageNotFound" xml:space="preserve">
    <value>Image Not Found</value>
  </data>
  <data name="Maps_Pan" xml:space="preserve">
    <value>Pan</value>
  </data>
  <data name="Maps_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Maps_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Maps_ZoomIn" xml:space="preserve">
    <value>Zoom in</value>
  </data>
  <data name="Maps_ZoomOut" xml:space="preserve">
    <value>Zoom out</value>
  </data>
  <data name="Chart_Pan" xml:space="preserve">
    <value>Pan</value>
  </data>
  <data name="Chart_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Chart_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Chart_ZoomIn" xml:space="preserve">
    <value>Zoom in</value>
  </data>
  <data name="Chart_ZoomOut" xml:space="preserve">
    <value>Zoom out</value>
  </data>
  <data name="Chart_ResetZoom" xml:space="preserve">
    <value>Reset Zoom</value>
  </data>
  <data name="ColorPicker_Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ColorPicker_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ColorPicker_ModeSwitcher" xml:space="preserve">
    <value>Switch Mode</value>
  </data>
  <data name="ColorPicker_FormatSwitcher" xml:space="preserve">
    <value>Toggle format</value>
  </data>
  <data name="ColorPicker" xml:space="preserve">
    <value>ColorPicker</value>
  </data>
  <data name="SplitButton" xml:space="preserve">
    <value>SplitButton</value>
  </data>
  <data name="ListBox_ActionFailureTemplate" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="ListBox_MoveAllFrom" xml:space="preserve">
    <value>Move All From</value>
  </data>
  <data name="ListBox_MoveAllTo" xml:space="preserve">
    <value>Move All To</value>
  </data>
  <data name="ListBox_MoveDown" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="ListBox_MoveFrom" xml:space="preserve">
    <value>Move From</value>
  </data>
  <data name="ListBox_MoveTo" xml:space="preserve">
    <value>Move To</value>
  </data>
  <data name="ListBox_MoveUp" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="ListBox_NoRecordsTemplate" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="ListBox_SelectAllText" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="ListBox_UnSelectAllText" xml:space="preserve">
    <value>Unselect All</value>
  </data>
  <data name="ListBox_FilterLabel" xml:space="preserve">
    <value>Search List Item</value>
  </data>
  <data name="QueryBuilder_AddCondition" xml:space="preserve">
    <value>Add Condition</value>
  </data>
  <data name="QueryBuilder_AddGroup" xml:space="preserve">
    <value>Add Group</value>
  </data>
  <data name="QueryBuilder_AND" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="QueryBuilder_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="QueryBuilder_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="QueryBuilder_DeleteGroup" xml:space="preserve">
    <value>Delete group</value>
  </data>
  <data name="QueryBuilder_DeleteRule" xml:space="preserve">
    <value>Remove this condition</value>
  </data>
  <data name="QueryBuilder_Edit" xml:space="preserve">
    <value>EDIT</value>
  </data>
  <data name="QueryBuilder_Empty" xml:space="preserve">
    <value>Empty</value>
  </data>
  <data name="QueryBuilder_EndsWith" xml:space="preserve">
    <value>Ends With</value>
  </data>
  <data name="QueryBuilder_Equal" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="QueryBuilder_GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="QueryBuilder_GreaterThanOrEqual" xml:space="preserve">
    <value>Greater Than Or Equal</value>
  </data>
  <data name="QueryBuilder_In" xml:space="preserve">
    <value>In</value>
  </data>
  <data name="QueryBuilder_LessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="QueryBuilder_LessThanOrEqual" xml:space="preserve">
    <value>Less Than Or Equal</value>
  </data>
  <data name="QueryBuilder_NotBetween" xml:space="preserve">
    <value>Not Between</value>
  </data>
  <data name="QueryBuilder_NotEmpty" xml:space="preserve">
    <value>Not Empty</value>
  </data>
  <data name="QueryBuilder_NotEqual" xml:space="preserve">
    <value>Not Equal</value>
  </data>
  <data name="QueryBuilder_NotIn" xml:space="preserve">
    <value>Not In</value>
  </data>
  <data name="QueryBuilder_OR" xml:space="preserve">
    <value>OR</value>
  </data>
  <data name="QueryBuilder_OtherFields" xml:space="preserve">
    <value>Other Fields</value>
  </data>
  <data name="QueryBuilder_Remove" xml:space="preserve">
    <value>REMOVE</value>
  </data>
  <data name="QueryBuilder_SelectField" xml:space="preserve">
    <value>Select a field</value>
  </data>
  <data name="QueryBuilder_SelectOperator" xml:space="preserve">
    <value>Select operator</value>
  </data>
  <data name="QueryBuilder_SelectValue" xml:space="preserve">
    <value>Enter Value</value>
  </data>
  <data name="QueryBuilder_PlaceholderValue" xml:space="preserve">
    <value>Select Value</value>
  </data>
  <data name="QueryBuilder_PlaceholderDateRangeValue" xml:space="preserve">
    <value>Choose a Range</value>
  </data>
  <data name="QueryBuilder_PlaceholderDateValue" xml:space="preserve">
    <value>Select a date</value>
  </data>
  <data name="QueryBuilder_StartsWith" xml:space="preserve">
    <value>Starts With</value>
  </data>
  <data name="QueryBuilder_SummaryViewTitle" xml:space="preserve">
    <value>Summary View</value>
  </data>
  <data name="QueryBuilder_ValidationMessage" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <data name="QueryBuilder_Null" xml:space="preserve">
    <value>Is Null</value>
  </data>
  <data name="QueryBuilder_NotNull" xml:space="preserve">
    <value>Is Not Null</value>
  </data>
  <data name="QueryBuilder_NotStartsWith" xml:space="preserve">
    <value>Does Not Start With</value>
  </data>
  <data name="QueryBuilder_NotEndsWith" xml:space="preserve">
    <value>Does Not End With</value>
  </data>
  <data name="QueryBuilder_NotContains" xml:space="preserve">
    <value>Does Not Contain</value>
  </data>
  <data name="QueryBuilder_LockGroup" xml:space="preserve">
    <value>Lock Group</value>
  </data>
  <data name="QueryBuilder_UnlockGroup" xml:space="preserve">
    <value>Unlock Group</value>
  </data>
  <data name="QueryBuilder_LockRule" xml:space="preserve">
    <value>Lock Rule</value>
  </data>
  <data name="QueryBuilder_UnlockRule" xml:space="preserve">
    <value>Unlock Rule</value>
  </data>
  <data name="QueryBuilder_CloneGroup" xml:space="preserve">
    <value>Clone Group</value>
  </data>
  <data name="QueryBuilder_CloneRule" xml:space="preserve">
    <value>Clone Rule</value>
  </data>
  <data name="DiagramComponent_X" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="DiagramComponent_Y" xml:space="preserve">
    <value>Y</value>
  </data>
  <data name="DiagramComponent_W" xml:space="preserve">
    <value>W</value>
  </data>
  <data name="DiagramComponent_H" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="Diagram_BringToFront" xml:space="preserve">
    <value>Bring To Front</value>
  </data>
  <data name="Diagram_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="Diagram_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="Diagram_Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="Diagram_Grouping" xml:space="preserve">
    <value>Grouping</value>
  </data>
  <data name="Diagram_MoveForward" xml:space="preserve">
    <value>Move Forward</value>
  </data>
  <data name="Diagram_Order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="Diagram_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="Diagram_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="Diagram_SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="Diagram_SendBackward" xml:space="preserve">
    <value>Send Backward</value>
  </data>
  <data name="Diagram_SendToBack" xml:space="preserve">
    <value>Send To Back</value>
  </data>
  <data name="Diagram_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="Diagram_UnGroup" xml:space="preserve">
    <value>Ungroup</value>
  </data>
  <data name="Dialog_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Message_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Toast_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="InPlaceEditor_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="InPlaceEditor_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="InPlaceEditor_LoadingText" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="InPlaceEditor_EditIcon" xml:space="preserve">
    <value>Click to edit</value>
  </data>
  <data name="InPlaceEditor_EditAreaClick" xml:space="preserve">
    <value>Click to edit</value>
  </data>
  <data name="InPlaceEditor_EditAreaDoubleClick" xml:space="preserve">
    <value>Double click to edit</value>
  </data>
  <data name="ImageEditor_ShapeStrokeContent" xml:space="preserve">
    <value>Small</value>
  </data>
  <data name="ImageEditor_PenStrokeContent" xml:space="preserve">
    <value>Small</value>
  </data>
  <data name="ImageEditor_SliderLabel" xml:space="preserve">
    <value>Brightness</value>
  </data>
  <data name="ImageEditor_UploaderTooltip" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="ImageEditor_ZoomOutTooltip" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="ImageEditor_ZoomInTooltip" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="ImageEditor_CropTooltip" xml:space="preserve">
    <value>Crop and Transform</value>
  </data>
  <data name="ImageEditor_CropSelectionTooltip" xml:space="preserve">
    <value>Crop Selection</value>
  </data>
  <data name="ImageEditor_AnnotationTooltip" xml:space="preserve">
    <value>Annotation</value>
  </data>
  <data name="ImageEditor_TransformTooltip" xml:space="preserve">
    <value>Transform</value>
  </data>
  <data name="ImageEditor_FinetuneTooltip" xml:space="preserve">
    <value>Finetune</value>
  </data>
  <data name="ImageEditor_FilterTooltip" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="ImageEditor_BrightnessTooltip" xml:space="preserve">
    <value>Brightness</value>
  </data>
  <data name="ImageEditor_ContrastTooltip" xml:space="preserve">
    <value>Contrast</value>
  </data>
  <data name="ImageEditor_HueTooltip" xml:space="preserve">
    <value>Hue</value>
  </data>
  <data name="ImageEditor_SaturationTooltip" xml:space="preserve">
    <value>Saturation</value>
  </data>
  <data name="ImageEditor_ExposureTooltip" xml:space="preserve">
    <value>Exposure</value>
  </data>
  <data name="ImageEditor_OpacityTooltip" xml:space="preserve">
    <value>Opacity</value>
  </data>
  <data name="ImageEditor_BlurTooltip" xml:space="preserve">
    <value>Blur</value>
  </data>
  <data name="ImageEditor_FontFamilyTooltip" xml:space="preserve">
    <value>Font Family</value>
  </data>
  <data name="ImageEditor_FontSizeTooltip" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="ImageEditor_FontColorTooltip" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="ImageEditor_StrokeColorTooltip" xml:space="preserve">
    <value>Stroke Color</value>
  </data>
  <data name="ImageEditor_StrokeWidthTooltip" xml:space="preserve">
    <value>Stroke Width</value>
  </data>
  <data name="ImageEditor_FillColorTooltip" xml:space="preserve">
    <value>Fill Color</value>
  </data>
  <data name="ImageEditor_OkBtnTooltip" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ImageEditor_CancelBtnTooltip" xml:space="preserve">
    <value>Discard</value>
  </data>
  <data name="ImageEditor_ResetBtnTooltip" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ImageEditor_SaveBtnTooltip" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ImageEditor_UndoTooltip" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="ImageEditor_RedoTooltip" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="ImageEditor_FilterDefaultTooltip" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="ImageEditor_FilterChromeTooltip" xml:space="preserve">
    <value>Chrome</value>
  </data>
  <data name="ImageEditor_FilterColdTooltip" xml:space="preserve">
    <value>Cold</value>
  </data>
  <data name="ImageEditor_FilterWarmTooltip" xml:space="preserve">
    <value>Warm</value>
  </data>
  <data name="ImageEditor_FilterGrayscaleTooltip" xml:space="preserve">
    <value>Grayscale</value>
  </data>
  <data name="ImageEditor_FilterSepiaTooltip" xml:space="preserve">
    <value>Sepia</value>
  </data>
  <data name="ImageEditor_FilterInvertTooltip" xml:space="preserve">
    <value>Invert</value>
  </data>
  <data name="ImageEditor_CropCustomText" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="ImageEditor_CropSquareText" xml:space="preserve">
    <value>Square</value>
  </data>
  <data name="ImageEditor_CropCircleText" xml:space="preserve">
    <value>Circle</value>
  </data>
  <data name="ImageEditor_AnnotationPenText" xml:space="preserve">
    <value>Pen</value>
  </data>
  <data name="ImageEditor_AnnotationLineText" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="ImageEditor_AnnotationRectangleText" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="ImageEditor_AnnotationEllipseText" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="ImageEditor_AnnotationPathText" xml:space="preserve">
    <value>Path</value>
  </data>
  <data name="ImageEditor_AnnotationText" xml:space="preserve">
    <value>Add Text</value>
  </data>
  <data name="ImageEditor_TransformRotateLeftText" xml:space="preserve">
    <value>Rotate Left</value>
  </data>
  <data name="ImageEditor_TransformRotateRightText" xml:space="preserve">
    <value>Rotate Right</value>
  </data>
  <data name="ImageEditor_TransformFlipHorizontalText" xml:space="preserve">
    <value>Horizontal Flip</value>
  </data>
  <data name="ImageEditor_TransformFlipVerticalText" xml:space="preserve">
    <value>Vertical Flip</value>
  </data>
  <data name="ImageEditor_FontStyleBoldText" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="ImageEditor_FontStyleItalicText" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="ImageEditor_ShapeStrokeXSmallText" xml:space="preserve">
    <value>X-Small</value>
  </data>
  <data name="ImageEditor_ShapeStrokeSmallText" xml:space="preserve">
    <value>Small</value>
  </data>
  <data name="ImageEditor_ShapeStrokeMediumText" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="ImageEditor_ShapeStrokeLargeText" xml:space="preserve">
    <value>Large</value>
  </data>
  <data name="ImageEditor_ShapeStrokeXLargeText" xml:space="preserve">
    <value>X-Large</value>
  </data>
  <data name="ImageEditor_CloneBtnTooltip" xml:space="preserve">
    <value>Duplicate</value>
  </data>
  <data name="ImageEditor_DeleteBtnTooltip" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="ImageEditor_EditTextBtnTooltip" xml:space="preserve">
    <value>Edit Text</value>
  </data>
  <data name="ImageEditor_ArrowStart" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="ImageEditor_ArrowEnd" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="ImageEditor_ArrowStartContent" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ImageEditor_ArrowEndContent" xml:space="preserve">
    <value>Arrow Solid</value>
  </data>
  <data name="ImageEditor_ArrowNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ImageEditor_ArrowBar" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="ImageEditor_Arrow" xml:space="preserve">
    <value>Arrow</value>
  </data>
  <data name="ImageEditor_ArrowSolid" xml:space="preserve">
    <value>Arrow Solid</value>
  </data>
  <data name="ImageEditor_ArrowCircle" xml:space="preserve">
    <value>Circle</value>
  </data>
  <data name="ImageEditor_ArrowCircleSolid" xml:space="preserve">
    <value>Circle Solid</value>
  </data>
  <data name="ImageEditor_ArrowSquare" xml:space="preserve">
    <value>Square</value>
  </data>
  <data name="ImageEditor_ArrowSquareSolid" xml:space="preserve">
    <value>Square Solid</value>
  </data>
  <data name="ImageEditor_DialogHeaderContent" xml:space="preserve">
    <value>Confirm Save Changes</value>
  </data>
  <data name="ImageEditor_DialogContent" xml:space="preserve">
    <value>Do you want to save the changes you made to the image?</value>
  </data>
  <data name="ImageEditor_DialogYesContent" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="ImageEditor_DialogNoContent" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="ImageEditor_UnSupportedDialogHeaderContent" xml:space="preserve">
    <value>Unsupported file</value>
  </data>
  <data name="ImageEditor_UnSupportedDialogContent" xml:space="preserve">
    <value>The dropped file is unsupported.</value>
  </data>
  <data name="ImageEditor_UnSupportedDialogOkContent" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ImageEditor_DragText" xml:space="preserve">
    <value>Drag and drop your image here or</value>
  </data>
  <data name="ImageEditor_DropText" xml:space="preserve">
    <value>Drop your image here or</value>
  </data>
  <data name="ImageEditor_BrowseText" xml:space="preserve">
    <value>Browse here...</value>
  </data>
  <data name="ImageEditor_SupportText" xml:space="preserve">
    <value>Supports:</value>
  </data>
  <data name="ImageEditor_ResizeText" xml:space="preserve">
    <value>Resize</value>
  </data>
  <data name="ImageEditor_ResizeWidth" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="ImageEditor_ResizeHeight" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="ImageEditor_NonAspect_Tooltip" xml:space="preserve">
    <value>Maintain non aspect ratio</value>
  </data>
  <data name="ImageEditor_Aspect_Tooltip" xml:space="preserve">
    <value>Maintain aspect ratio</value>
  </data>
  <data name="ImageEditor_HorizontalFlipBtnTooltip" xml:space="preserve">
    <value>Horizontal Flip</value>
  </data>
  <data name="ImageEditor_VerticalFlipBtnTooltip" xml:space="preserve">
    <value>Vertical Flip</value>
  </data>
  <data name="ImageEditor_TransparencyBtnTooltip" xml:space="preserve">
    <value>Transparency</value>
  </data>
  <data name="ImageEditor_FrameText" xml:space="preserve">
    <value>Frame</value>
  </data>
  <data name="ImageEditor_FrameNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ImageEditor_FrameMat" xml:space="preserve">
    <value>Mat</value>
  </data>
  <data name="ImageEditor_FrameBevel" xml:space="preserve">
    <value>Bevel</value>
  </data>
  <data name="ImageEditor_FrameLine" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="ImageEditor_FrameInset" xml:space="preserve">
    <value>Inset</value>
  </data>
  <data name="ImageEditor_FrameHook" xml:space="preserve">
    <value>Hook</value>
  </data>
  <data name="ImageEditor_FrameColorTooltip" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="ImageEditor_FrameSizeTooltip" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="ImageEditor_FrameOffsetTooltip" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="ImageEditor_FrameRadiusTooltip" xml:space="preserve">
    <value>Radius</value>
  </data>
  <data name="ImageEditor_FrameAmountTooltip" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="ImageEditor_ResizeWidthText" xml:space="preserve">
    <value>W</value>
  </data>
  <data name="ImageEditor_ResizeHeightText" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="ImageEditor_FrameBorderTooltip" xml:space="preserve">
    <value>Border</value>
  </data>
  <data name="ImageEditor_FrameBorderSolid" xml:space="preserve">
    <value>Solid</value>
  </data>
  <data name="ImageEditor_FrameBorderDashed" xml:space="preserve">
    <value>Dashed</value>
  </data>
  <data name="ImageEditor_FrameBorderDotted" xml:space="preserve">
    <value>Dotted</value>
  </data>
  <data name="ImageEditor_FrameGradientTooltip" xml:space="preserve">
    <value>Gradient Color</value>
  </data>
  <data name="ImageEditor_StraightenTooltip" xml:space="preserve">
    <value>Straighten</value>
  </data>
  <data name="ImageEditor_Annotation" xml:space="preserve">
    <value>Annotation</value>
  </data>
  <data name="Kanban_Items" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="Kanban_Item" xml:space="preserve">
    <value>item</value>
  </data>
  <data name="Kanban_No_Cards" xml:space="preserve">
    <value>No cards to display</value>
  </data>
  <data name="Kanban_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="Kanban_Max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="Kanban_CardsSelected" xml:space="preserve">
    <value>Cards Selected</value>
  </data>
  <data name="Kanban_AddTitle" xml:space="preserve">
    <value>Add New Card</value>
  </data>
  <data name="Kanban_EditTitle" xml:space="preserve">
    <value>Edit Card Details</value>
  </data>
  <data name="Kanban_DeleteTitle" xml:space="preserve">
    <value>Delete Card</value>
  </data>
  <data name="Kanban_DeleteContent" xml:space="preserve">
    <value>Are you sure you want to delete this card?</value>
  </data>
  <data name="Kanban_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Kanban_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Kanban_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Kanban_Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Kanban_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Rich_Text_Editor" xml:space="preserve">
    <value>Rich Text Editor</value>
  </data>
  <data name="RichTextEditor_Markdown_Editor" xml:space="preserve">
    <value>Markdown Editor</value>
  </data>
  <data name="RichTextEditor_NumberFormatList" xml:space="preserve">
    <value>Numbered List</value>
  </data>
  <data name="RichTextEditor_NumberFormatListNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="RichTextEditor_NumberFormatListNumber" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="RichTextEditor_NumberFormatListUpperAlpha" xml:space="preserve">
    <value>Upper Alpha</value>
  </data>
  <data name="RichTextEditor_NumberFormatListLowerAlpha" xml:space="preserve">
    <value>Lower Alpha</value>
  </data>
  <data name="RichTextEditor_NumberFormatListUpperRoman" xml:space="preserve">
    <value>Upper Roman</value>
  </data>
  <data name="RichTextEditor_NumberFormatListLowerRoman" xml:space="preserve">
    <value>Lower Roman</value>
  </data>
  <data name="RichTextEditor_NumberFormatListLowerGreek" xml:space="preserve">
    <value>Lower Greek</value>
  </data>
  <data name="RichTextEditor_BulletFormatList" xml:space="preserve">
    <value>Bulleted List</value>
  </data>
  <data name="RichTextEditor_BulletFormatListNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="RichTextEditor_BulletFormatListCircle" xml:space="preserve">
    <value>Circle</value>
  </data>
  <data name="RichTextEditor_BulletFormatListSquare" xml:space="preserve">
    <value>Square</value>
  </data>
  <data name="RichTextEditor_BulletFormatListDisc" xml:space="preserve">
    <value>Disc</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownParagraph" xml:space="preserve">
    <value>Paragraph</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownCode" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownQuotation" xml:space="preserve">
    <value>Quotation</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading1" xml:space="preserve">
    <value>Heading 1</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading2" xml:space="preserve">
    <value>Heading 2</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading3" xml:space="preserve">
    <value>Heading 3</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading4" xml:space="preserve">
    <value>Heading 4</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading5" xml:space="preserve">
    <value>Heading 5</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading6" xml:space="preserve">
    <value>Heading  6</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading7" xml:space="preserve">
    <value>Heading 7</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading8" xml:space="preserve">
    <value>Heading 8</value>
  </data>
  <data name="RichTextEditor_FormatsDropDownHeading9" xml:space="preserve">
    <value>Heading  9</value>
  </data>
  <data name="RichTextEditor_FontNameSegoeUI" xml:space="preserve">
    <value>Segoe UI</value>
  </data>
  <data name="RichTextEditor_FontNameArial" xml:space="preserve">
    <value>Arial</value>
  </data>
  <data name="RichTextEditor_FontNameGeorgia" xml:space="preserve">
    <value>Georgia</value>
  </data>
  <data name="RichTextEditor_FontNameImpact" xml:space="preserve">
    <value>Impact</value>
  </data>
  <data name="RichTextEditor_FontNameTahoma" xml:space="preserve">
    <value>Tahoma</value>
  </data>
  <data name="RichTextEditor_FontNameTimesNewRoman" xml:space="preserve">
    <value>Times New Roman</value>
  </data>
  <data name="RichTextEditor_FontNameVerdana" xml:space="preserve">
    <value>Verdana</value>
  </data>
  <data name="RichTextEditor_Alignments" xml:space="preserve">
    <value>Alignments</value>
  </data>
  <data name="RichTextEditor_JustifyLeft" xml:space="preserve">
    <value>Align Left</value>
  </data>
  <data name="RichTextEditor_JustifyCenter" xml:space="preserve">
    <value>Align Center</value>
  </data>
  <data name="RichTextEditor_JustifyRight" xml:space="preserve">
    <value>Align Right</value>
  </data>
  <data name="RichTextEditor_JustifyFull" xml:space="preserve">
    <value>Align Justify</value>
  </data>
  <data name="RichTextEditor_FontName" xml:space="preserve">
    <value>Font Name</value>
  </data>
  <data name="RichTextEditor_FontSize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="RichTextEditor_FontColor" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="RichTextEditor_BackgroundColor" xml:space="preserve">
    <value>Background Color</value>
  </data>
  <data name="RichTextEditor_Bold" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="RichTextEditor_Italic" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="RichTextEditor_Underline" xml:space="preserve">
    <value>Underline</value>
  </data>
  <data name="RichTextEditor_Strikethrough" xml:space="preserve">
    <value>Strikethrough</value>
  </data>
  <data name="RichTextEditor_ClearFormat" xml:space="preserve">
    <value>Clear Format</value>
  </data>
  <data name="RichTextEditor_ClearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  <data name="RichTextEditor_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="RichTextEditor_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="RichTextEditor_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="RichTextEditor_UnorderedList" xml:space="preserve">
    <value>Bulleted List</value>
  </data>
  <data name="RichTextEditor_OrderedList" xml:space="preserve">
    <value>Numbered List</value>
  </data>
  <data name="RichTextEditor_Indent" xml:space="preserve">
    <value>Increase Indent</value>
  </data>
  <data name="RichTextEditor_Outdent" xml:space="preserve">
    <value>Decrease Indent</value>
  </data>
  <data name="RichTextEditor_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="RichTextEditor_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="RichTextEditor_Superscript" xml:space="preserve">
    <value>Superscript</value>
  </data>
  <data name="RichTextEditor_Subscript" xml:space="preserve">
    <value>Subscript</value>
  </data>
  <data name="RichTextEditor_CreateLink" xml:space="preserve">
    <value>Insert Hyperlink</value>
  </data>
  <data name="RichTextEditor_OpenLink" xml:space="preserve">
    <value>Open Link</value>
  </data>
  <data name="RichTextEditor_EditLink" xml:space="preserve">
    <value>Edit Link</value>
  </data>
  <data name="RichTextEditor_RemoveLink" xml:space="preserve">
    <value>Remove Link</value>
  </data>
  <data name="RichTextEditor_Image" xml:space="preserve">
    <value>Insert Image</value>
  </data>
  <data name="RichTextEditor_Replace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="RichTextEditor_Align" xml:space="preserve">
    <value>Align</value>
  </data>
  <data name="RichTextEditor_Caption" xml:space="preserve">
    <value>Image Caption</value>
  </data>
  <data name="RichTextEditor_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="RichTextEditor_InsertLink" xml:space="preserve">
    <value>Insert Link</value>
  </data>
  <data name="RichTextEditor_Display" xml:space="preserve">
    <value>Display</value>
  </data>
  <data name="RichTextEditor_AltText" xml:space="preserve">
    <value>Alternative text</value>
  </data>
  <data name="RichTextEditor_Dimension" xml:space="preserve">
    <value>Change Size</value>
  </data>
  <data name="RichTextEditor_Fullscreen" xml:space="preserve">
    <value>Maximize</value>
  </data>
  <data name="RichTextEditor_Maximize" xml:space="preserve">
    <value>Maximize</value>
  </data>
  <data name="RichTextEditor_Minimize" xml:space="preserve">
    <value>Minimize</value>
  </data>
  <data name="RichTextEditor_LowerCase" xml:space="preserve">
    <value>Lower Case</value>
  </data>
  <data name="RichTextEditor_UpperCase" xml:space="preserve">
    <value>Upper Case</value>
  </data>
  <data name="RichTextEditor_Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="RichTextEditor_Formats" xml:space="preserve">
    <value>Formats</value>
  </data>
  <data name="RichTextEditor_Sourcecode" xml:space="preserve">
    <value>Code View</value>
  </data>
  <data name="RichTextEditor_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="RichTextEditor_Viewside" xml:space="preserve">
    <value>ViewSide</value>
  </data>
  <data name="RichTextEditor_InsertCode" xml:space="preserve">
    <value>Insert Code</value>
  </data>
  <data name="RichTextEditor_LinkText" xml:space="preserve">
    <value>Display text</value>
  </data>
  <data name="RichTextEditor_LinkTooltipLabel" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="RichTextEditor_LinkWebUrl" xml:space="preserve">
    <value>Web address</value>
  </data>
  <data name="RichTextEditor_LinkTitle" xml:space="preserve">
    <value>Enter a title</value>
  </data>
  <data name="RichTextEditor_LinkUrl" xml:space="preserve">
    <value>https://example.com</value>
  </data>
  <data name="RichTextEditor_LinkOpenInNewWindow" xml:space="preserve">
    <value>Open link in new window</value>
  </data>
  <data name="RichTextEditor_LinkHeader" xml:space="preserve">
    <value>Insert Link</value>
  </data>
  <data name="RichTextEditor_DialogInsert" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="RichTextEditor_DialogCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="RichTextEditor_DialogUpdate" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="RichTextEditor_ImageHeader" xml:space="preserve">
    <value>Insert Image</value>
  </data>
  <data name="RichTextEditor_ImageLinkHeader" xml:space="preserve">
    <value>You can also provide a link from the web</value>
  </data>
  <data name="RichTextEditor_MdImageLink" xml:space="preserve">
    <value>Please provide a URL for your image</value>
  </data>
  <data name="RichTextEditor_ImageUploadMessage" xml:space="preserve">
    <value>Drop image here or browse to upload</value>
  </data>
  <data name="RichTextEditor_ImageDeviceUploadMessage" xml:space="preserve">
    <value>Click here to upload</value>
  </data>
  <data name="RichTextEditor_ImageAlternateText" xml:space="preserve">
    <value>Alternate Text</value>
  </data>
  <data name="RichTextEditor_AlternateHeader" xml:space="preserve">
    <value>Alternative Text</value>
  </data>
  <data name="RichTextEditor_Browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="RichTextEditor_VideoDeviceUploadMessage" xml:space="preserve">
    <value>Click here to upload</value>
  </data>
  <data name="RichTextEditor_VideoUploadMessage" xml:space="preserve">
    <value>Drop a video file or browse to upload</value>
  </data>
  <data name="RichTextEditor_EmbedVideoUploadMessage" xml:space="preserve">
    <value>Paste embedded code here</value>
  </data>
  <data name="RichTextEditor_WebUrl" xml:space="preserve">
    <value>Web URL</value>
  </data>
  <data name="RichTextEditor_EmbedUrl" xml:space="preserve">
    <value>Embedded code</value>
  </data>
  <data name="RichTextEditor_VideoWidth" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="RichTextEditor_VideoHeight" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="RichTextEditor_AudioDeviceUploadMessage" xml:space="preserve">
    <value>Click here to upload</value>
  </data>
  <data name="RichTextEditor_AudioUploadMessage" xml:space="preserve">
    <value>Drop a audio file or browse to upload</value>
  </data>
  <data name="RichTextEditor_ImageUrl" xml:space="preserve">
    <value>https://example.com/image.png</value>
  </data>
  <data name="RichTextEditor_ImageCaption" xml:space="preserve">
    <value>Caption</value>
  </data>
  <data name="RichTextEditor_ImageSizeHeader" xml:space="preserve">
    <value>Image Size</value>
  </data>
  <data name="RichTextEditor_ImageHeight" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="RichTextEditor_ImageWidth" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="RichTextEditor_TextPlaceholder" xml:space="preserve">
    <value>Enter text</value>
  </data>
  <data name="RichTextEditor_InsertTableBtn" xml:space="preserve">
    <value>Insert table</value>
  </data>
  <data name="RichTextEditor_TableDialogHeader" xml:space="preserve">
    <value>Insert Table</value>
  </data>
  <data name="RichTextEditor_TableWidth" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="RichTextEditor_Cellpadding" xml:space="preserve">
    <value>Cell Padding</value>
  </data>
  <data name="RichTextEditor_Cellspacing" xml:space="preserve">
    <value>Cell Spacing</value>
  </data>
  <data name="RichTextEditor_Columns" xml:space="preserve">
    <value>Number of columns</value>
  </data>
  <data name="RichTextEditor_Rows" xml:space="preserve">
    <value>Number of rows</value>
  </data>
  <data name="RichTextEditor_TableRows" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="RichTextEditor_TableColumns" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="RichTextEditor_TableCellHorizontalAlign" xml:space="preserve">
    <value>Table Cell Horizontal Align</value>
  </data>
  <data name="RichTextEditor_TableCellVerticalAlign" xml:space="preserve">
    <value>Vertical Align</value>
  </data>
  <data name="RichTextEditor_CreateTable" xml:space="preserve">
    <value>Create Table</value>
  </data>
  <data name="RichTextEditor_RemoveTable" xml:space="preserve">
    <value>Remove Table</value>
  </data>
  <data name="RichTextEditor_TableHeader" xml:space="preserve">
    <value>Header Row</value>
  </data>
  <data name="RichTextEditor_TableRemove" xml:space="preserve">
    <value>Delete Table</value>
  </data>
  <data name="RichTextEditor_TableCellBackground" xml:space="preserve">
    <value>Table Cell Background</value>
  </data>
  <data name="RichTextEditor_TableEditProperties" xml:space="preserve">
    <value>Table Edit Properties</value>
  </data>
  <data name="RichTextEditor_Styles" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="RichTextEditor_InsertColumnLeft" xml:space="preserve">
    <value>Insert Column Left</value>
  </data>
  <data name="RichTextEditor_InsertColumnRight" xml:space="preserve">
    <value>Insert Column Right</value>
  </data>
  <data name="RichTextEditor_DeleteColumn" xml:space="preserve">
    <value>Delete Column</value>
  </data>
  <data name="RichTextEditor_InsertRowBefore" xml:space="preserve">
    <value>Insert Row Before</value>
  </data>
  <data name="RichTextEditor_InsertRowAfter" xml:space="preserve">
    <value>Insert Row After</value>
  </data>
  <data name="RichTextEditor_DeleteRow" xml:space="preserve">
    <value>Delete Row</value>
  </data>
  <data name="RichTextEditor_TableEditHeader" xml:space="preserve">
    <value>Edit Table</value>
  </data>
  <data name="RichTextEditor_TableHeadingText" xml:space="preserve">
    <value>Heading</value>
  </data>
  <data name="RichTextEditor_TableColText" xml:space="preserve">
    <value>Col</value>
  </data>
  <data name="RichTextEditor_ImageInsertLinkHeader" xml:space="preserve">
    <value>Insert Link</value>
  </data>
  <data name="RichTextEditor_EditImageHeader" xml:space="preserve">
    <value>Edit Image</value>
  </data>
  <data name="RichTextEditor_AlignmentsDropDownLeft" xml:space="preserve">
    <value>Align Left</value>
  </data>
  <data name="RichTextEditor_AlignmentsDropDownCenter" xml:space="preserve">
    <value>Align Center</value>
  </data>
  <data name="RichTextEditor_AlignmentsDropDownRight" xml:space="preserve">
    <value>Align Right</value>
  </data>
  <data name="RichTextEditor_AlignmentsDropDownJustify" xml:space="preserve">
    <value>Align Justify</value>
  </data>
  <data name="RichTextEditor_ImageDisplayDropDownInline" xml:space="preserve">
    <value>Inline</value>
  </data>
  <data name="RichTextEditor_ImageDisplayDropDownBreak" xml:space="preserve">
    <value>Break</value>
  </data>
  <data name="RichTextEditor_TableInsertRowDropDownBefore" xml:space="preserve">
    <value>Insert row before</value>
  </data>
  <data name="RichTextEditor_TableInsertRowDropDownAfter" xml:space="preserve">
    <value>Insert row after</value>
  </data>
  <data name="RichTextEditor_TableInsertRowDropDownDelete" xml:space="preserve">
    <value>Delete row</value>
  </data>
  <data name="RichTextEditor_TableInsertColumnDropDownLeft" xml:space="preserve">
    <value>Insert column left</value>
  </data>
  <data name="RichTextEditor_TableInsertColumnDropDownRight" xml:space="preserve">
    <value>Insert column right</value>
  </data>
  <data name="RichTextEditor_TableInsertColumnDropDownDelete" xml:space="preserve">
    <value>Delete column</value>
  </data>
  <data name="RichTextEditor_TableVerticalAlignDropDownTop" xml:space="preserve">
    <value>Align Top</value>
  </data>
  <data name="RichTextEditor_TableVerticalAlignDropDownMiddle" xml:space="preserve">
    <value>Align Middle</value>
  </data>
  <data name="RichTextEditor_TableVerticalAlignDropDownBottom" xml:space="preserve">
    <value>Align Bottom</value>
  </data>
  <data name="RichTextEditor_TableStylesDropDownDashedBorder" xml:space="preserve">
    <value>Dashed Borders</value>
  </data>
  <data name="RichTextEditor_TableStylesDropDownAlternateRows" xml:space="preserve">
    <value>Alternate Rows</value>
  </data>
  <data name="RichTextEditor_PasteFormat" xml:space="preserve">
    <value>Paste Options</value>
  </data>
  <data name="RichTextEditor_PasteFormatContent" xml:space="preserve">
    <value>Choose the formatting action</value>
  </data>
  <data name="RichTextEditor_PlainText" xml:space="preserve">
    <value>Plain Text</value>
  </data>
  <data name="RichTextEditor_CleanFormat" xml:space="preserve">
    <value>Clean Format</value>
  </data>
  <data name="RichTextEditor_KeepFormat" xml:space="preserve">
    <value>Keep Format</value>
  </data>
  <data name="RichTextEditor_PasteDialogOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="RichTextEditor_PasteDialogCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="RichTextEditor_Audio_Replace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="RichTextEditor_Audio_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="RichTextEditor_Audio_Display" xml:space="preserve">
    <value>Display</value>
  </data>
  <data name="RichTextEditor_Video_Replace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="RichTextEditor_Video_Align" xml:space="preserve">
    <value>Align</value>
  </data>
  <data name="RichTextEditor_Video_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="RichTextEditor_Video_Display" xml:space="preserve">
    <value>Display</value>
  </data>
  <data name="RichTextEditor_Video_Dimension" xml:space="preserve">
    <value>Dimension</value>
  </data>
  <data name="RichTextEditor_Link_AriaLabel" xml:space="preserve">
    <value>Open in new window</value>
  </data>
  <data name="RichTextEditor_Image_Link_AriaLabel" xml:space="preserve">
    <value>Open in new window</value>
  </data>
  <data name="RichTextEditor_Img_AltText_Unsupported" xml:space="preserve">
    <value>Unsupported file format</value>
  </data>
  <data name="PdfViewer" xml:space="preserve">
    <value>PDF Viewer</value>
  </data>
  <data name="PdfViewer_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PdfViewer_DownloadFile" xml:space="preserve">
    <value>Download File</value>
  </data>
  <data name="PdfViewer_Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="PdfViewer_EnterPassword" xml:space="preserve">
    <value>This document is password protected. Please enter a password.</value>
  </data>
  <data name="PdfViewer_FileCorrupted" xml:space="preserve">
    <value>File Corrupted</value>
  </data>
  <data name="PdfViewer_FileCorruptedContent" xml:space="preserve">
    <value>The file is corrupted and cannot be opened.</value>
  </data>
  <data name="PdfViewer_FitHeight" xml:space="preserve">
    <value>Fit Height</value>
  </data>
  <data name="PdfViewer_FitPage" xml:space="preserve">
    <value>Fit Page</value>
  </data>
  <data name="PdfViewer_FitWidth" xml:space="preserve">
    <value>Fit Width</value>
  </data>
  <data name="PdfViewer_Automatic" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="PdfViewer_GoToFirstPage" xml:space="preserve">
    <value>Show First Page</value>
  </data>
  <data name="PdfViewer_InvalidPassword" xml:space="preserve">
    <value>Incorrect Password. Please try again.</value>
  </data>
  <data name="PdfViewer_NextPage" xml:space="preserve">
    <value>Show Next Page</value>
  </data>
  <data name="PdfViewer_PreviousPage" xml:space="preserve">
    <value>Show Previous Page</value>
  </data>
  <data name="PdfViewer_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PdfViewer_Open" xml:space="preserve">
    <value>Open File</value>
  </data>
  <data name="PdfViewer_PageNumber" xml:space="preserve">
    <value>Current Page Number</value>
  </data>
  <data name="PdfViewer_GoToLastPage" xml:space="preserve">
    <value>Show Last Page</value>
  </data>
  <data name="PdfViewer_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="PdfViewer_ZoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="PdfViewer_ZoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="PdfViewer_PageThumbnails" xml:space="preserve">
    <value>Page Thumbnails</value>
  </data>
  <data name="PdfViewer_Bookmarks" xml:space="preserve">
    <value>Bookmarks</value>
  </data>
  <data name="PdfViewer_Print" xml:space="preserve">
    <value>Print File</value>
  </data>
  <data name="PdfViewer_PasswordProtected" xml:space="preserve">
    <value>Password Required</value>
  </data>
  <data name="PdfViewer_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="PdfViewer_TextSelection" xml:space="preserve">
    <value>Text Selection Tool</value>
  </data>
  <data name="PdfViewer_Panning" xml:space="preserve">
    <value>Pan Mode</value>
  </data>
  <data name="PdfViewer_TextSearch" xml:space="preserve">
    <value>Find Text</value>
  </data>
  <data name="PdfViewer_Findindocument" xml:space="preserve">
    <value>Find in document</value>
  </data>
  <data name="PdfViewer_Matchcase" xml:space="preserve">
    <value>Match case</value>
  </data>
  <data name="PdfViewer_Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="PdfViewer_GoToPage" xml:space="preserve">
    <value>Go To Page</value>
  </data>
  <data name="PdfViewer_Nomatches" xml:space="preserve">
    <value>Viewer has finished searching the document. No more matches were found</value>
  </data>
  <data name="PdfViewer_NoTextFound" xml:space="preserve">
    <value>No Text Found</value>
  </data>
  <data name="PdfViewer_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="PdfViewer_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="PdfViewer_Annotation" xml:space="preserve">
    <value>Add or Edit annotations</value>
  </data>
  <data name="PdfViewer_Highlight" xml:space="preserve">
    <value>Highlight Text</value>
  </data>
  <data name="PdfViewer_Underline" xml:space="preserve">
    <value>Underline Text</value>
  </data>
  <data name="PdfViewer_Strikethrough" xml:space="preserve">
    <value>Strikethrough Text</value>
  </data>
  <data name="PdfViewer_Delete" xml:space="preserve">
    <value>Delete Annotation</value>
  </data>
  <data name="PdfViewer_Opacity" xml:space="preserve">
    <value>Opacity</value>
  </data>
  <data name="PdfViewer_ColorEdit" xml:space="preserve">
    <value>Change Color</value>
  </data>
  <data name="PdfViewer_OpacityEdit" xml:space="preserve">
    <value>Change Opacity</value>
  </data>
  <data name="PdfViewer_HighlightContext" xml:space="preserve">
    <value>Highlight</value>
  </data>
  <data name="PdfViewer_UnderlineContext" xml:space="preserve">
    <value>Underline</value>
  </data>
  <data name="PdfViewer_StrikethroughContext" xml:space="preserve">
    <value>Strikethrough</value>
  </data>
  <data name="PdfViewer_Servererror" xml:space="preserve">
    <value>Web-service is not listening. PDF Viewer depends on web-service for all its features. Please start the web service to continue.</value>
  </data>
  <data name="PdfViewer_Clienterror" xml:space="preserve">
    <value>Client-side error is found. Please check the custom headers provided in the AjaxRequestSettings property and web action methods in the ServerActionSettings property.</value>
  </data>
  <data name="PdfViewer_OpenText" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="PdfViewer_FirstText" xml:space="preserve">
    <value>First Page</value>
  </data>
  <data name="PdfViewer_PreviousText" xml:space="preserve">
    <value>Previous Page</value>
  </data>
  <data name="PdfViewer_NextText" xml:space="preserve">
    <value>Next Page</value>
  </data>
  <data name="PdfViewer_LastText" xml:space="preserve">
    <value>Last Page</value>
  </data>
  <data name="PdfViewer_ZoomInText" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="PdfViewer_ZoomOutText" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="PdfViewer_SelectionText" xml:space="preserve">
    <value>Selection</value>
  </data>
  <data name="PdfViewer_PanText" xml:space="preserve">
    <value>Pan</value>
  </data>
  <data name="PdfViewer_PrintText" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="PdfViewer_SearchText" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="PdfViewer_AnnotationEditText" xml:space="preserve">
    <value>Edit Annotation</value>
  </data>
  <data name="PdfViewer_LineThickness" xml:space="preserve">
    <value>Line Thickness</value>
  </data>
  <data name="PdfViewer_LineProperties" xml:space="preserve">
    <value>Line Properties</value>
  </data>
  <data name="PdfViewer_StartArrow" xml:space="preserve">
    <value>Start Arrow</value>
  </data>
  <data name="PdfViewer_EndArrow" xml:space="preserve">
    <value>End Arrow</value>
  </data>
  <data name="PdfViewer_LineStyle" xml:space="preserve">
    <value>Line Style</value>
  </data>
  <data name="PdfViewer_FillColor" xml:space="preserve">
    <value>Fill Color</value>
  </data>
  <data name="PdfViewer_LineColor" xml:space="preserve">
    <value>Line Color</value>
  </data>
  <data name="PdfViewer_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="PdfViewer_OpenArrow" xml:space="preserve">
    <value>Open Arrow</value>
  </data>
  <data name="PdfViewer_ClosedArrow" xml:space="preserve">
    <value>Closed Arrow</value>
  </data>
  <data name="PdfViewer_RoundArrow" xml:space="preserve">
    <value>Round Arrow</value>
  </data>
  <data name="PdfViewer_SquareArrow" xml:space="preserve">
    <value>Square Arrow</value>
  </data>
  <data name="PdfViewer_DiamondArrow" xml:space="preserve">
    <value>Diamond Arrow</value>
  </data>
  <data name="PdfViewer_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="PdfViewer_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="PdfViewer_DeleteContext" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="PdfViewer_Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="PdfViewer_AddStamp" xml:space="preserve">
    <value>Add Stamp</value>
  </data>
  <data name="PdfViewer_AddShapes" xml:space="preserve">
    <value>Add Shapes</value>
  </data>
  <data name="PdfViewer_StrokeEdit" xml:space="preserve">
    <value>Change Stroke Color</value>
  </data>
  <data name="PdfViewer_ChangeThickness" xml:space="preserve">
    <value>Change Border Thickness</value>
  </data>
  <data name="PdfViewer_Addline" xml:space="preserve">
    <value>Add Line</value>
  </data>
  <data name="PdfViewer_AddArrow" xml:space="preserve">
    <value>Add Arrow</value>
  </data>
  <data name="PdfViewer_AddRectangle" xml:space="preserve">
    <value>Add Rectangle</value>
  </data>
  <data name="PdfViewer_AddCircle" xml:space="preserve">
    <value>Add Circle</value>
  </data>
  <data name="PdfViewer_AddPolygon" xml:space="preserve">
    <value>Add Polygon</value>
  </data>
  <data name="PdfViewer_AddComments" xml:space="preserve">
    <value>Add Comments</value>
  </data>
  <data name="PdfViewer_Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="PdfViewer_NoCommentsYet" xml:space="preserve">
    <value>No Comments Yet</value>
  </data>
  <data name="PdfViewer_Accepted" xml:space="preserve">
    <value>Accepted</value>
  </data>
  <data name="PdfViewer_Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="PdfViewer_Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="PdfViewer_Rejected" xml:space="preserve">
    <value>Rejected</value>
  </data>
  <data name="PdfViewer_LeaderLength" xml:space="preserve">
    <value>Leader Length</value>
  </data>
  <data name="PdfViewer_ScaleRatio" xml:space="preserve">
    <value>Scale Ratio</value>
  </data>
  <data name="PdfViewer_Calibrate" xml:space="preserve">
    <value>Calibrate</value>
  </data>
  <data name="PdfViewer_CalibrateDistance" xml:space="preserve">
    <value>Calibrate Distance</value>
  </data>
  <data name="PdfViewer_CalibratePerimeter" xml:space="preserve">
    <value>Calibrate Perimeter</value>
  </data>
  <data name="PdfViewer_CalibrateArea" xml:space="preserve">
    <value>Calibrate Area</value>
  </data>
  <data name="PdfViewer_CalibrateRadius" xml:space="preserve">
    <value>Calibrate Radius</value>
  </data>
  <data name="PdfViewer_CalibrateVolume" xml:space="preserve">
    <value>Calibrate Volume</value>
  </data>
  <data name="PdfViewer_Depth" xml:space="preserve">
    <value>Depth</value>
  </data>
  <data name="PdfViewer_Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="PdfViewer_Round" xml:space="preserve">
    <value>Round</value>
  </data>
  <data name="PdfViewer_Square" xml:space="preserve">
    <value>Square</value>
  </data>
  <data name="PdfViewer_Diamond" xml:space="preserve">
    <value>Diamond</value>
  </data>
  <data name="PdfViewer_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="PdfViewer_Comment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="PdfViewer_CommentPanel" xml:space="preserve">
    <value>Comment Panel</value>
  </data>
  <data name="PdfViewer_SetStatus" xml:space="preserve">
    <value>Set Status</value>
  </data>
  <data name="PdfViewer_Post" xml:space="preserve">
    <value>Post</value>
  </data>
  <data name="PdfViewer_Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="PdfViewer_AddComment" xml:space="preserve">
    <value>Add a Comment</value>
  </data>
  <data name="PdfViewer_AddReply" xml:space="preserve">
    <value>Add a Reply</value>
  </data>
  <data name="PdfViewer_ImportAnnotations" xml:space="preserve">
    <value>Import Annotations</value>
  </data>
  <data name="PdfViewer_ExportAnnotations" xml:space="preserve">
    <value>Export Annotations</value>
  </data>
  <data name="PdfViewer_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="PdfViewer_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="PdfViewer_Bold" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="PdfViewer_Italic" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="PdfViewer_Strikethroughs" xml:space="preserve">
    <value>Strikethroughs</value>
  </data>
  <data name="PdfViewer_Underlines" xml:space="preserve">
    <value>Underlines</value>
  </data>
  <data name="PdfViewer_Superscript" xml:space="preserve">
    <value>Superscript</value>
  </data>
  <data name="PdfViewer_Subscript" xml:space="preserve">
    <value>Subscript</value>
  </data>
  <data name="PdfViewer_AlignLeft" xml:space="preserve">
    <value>Align Left</value>
  </data>
  <data name="PdfViewer_AlignRight" xml:space="preserve">
    <value>Align Right</value>
  </data>
  <data name="PdfViewer_Center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="PdfViewer_Justify" xml:space="preserve">
    <value>Justify</value>
  </data>
  <data name="PdfViewer_FontColor" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="PdfViewer_TextAlign" xml:space="preserve">
    <value>Text Align</value>
  </data>
  <data name="PdfViewer_TextProperties" xml:space="preserve">
    <value>Font Style</value>
  </data>
  <data name="PdfViewer_DrawSignature" xml:space="preserve">
    <value>Draw Signature</value>
  </data>
  <data name="PdfViewer_Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="PdfViewer_FontFamily" xml:space="preserve">
    <value>Font Family</value>
  </data>
  <data name="PdfViewer_FontSize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="PdfViewer_FreeText" xml:space="preserve">
    <value>Free Text</value>
  </data>
  <data name="PdfViewer_ImportFailed" xml:space="preserve">
    <value>Invalid JSON file type or file name; please select a valid JSON file</value>
  </data>
  <data name="PdfViewer_FileNotFound" xml:space="preserve">
    <value>Imported JSON file is not found in the desired location</value>
  </data>
  <data name="PdfViewer_ExportFailed" xml:space="preserve">
    <value>Export annotations action has failed; please ensure annotations are added properly</value>
  </data>
  <data name="PdfViewer_Draw_hand_Signature" xml:space="preserve">
    <value>DRAW</value>
  </data>
  <data name="PdfViewer_Type_Signature" xml:space="preserve">
    <value>TYPE</value>
  </data>
  <data name="PdfViewer_Upload_Signature" xml:space="preserve">
    <value>UPLOAD</value>
  </data>
  <data name="PdfViewer_Browse_Signature_Image" xml:space="preserve">
    <value>BROWSE</value>
  </data>
  <data name="PdfViewer_Save_Signature" xml:space="preserve">
    <value>Save Signature</value>
  </data>
  <data name="PdfViewer_Enter_Signature_as_Name" xml:space="preserve">
    <value>Enter your name</value>
  </data>
  <data name="PdfViewer_Dynamic" xml:space="preserve">
    <value>Dynamic</value>
  </data>
  <data name="PdfViewer_StandardBusiness" xml:space="preserve">
    <value>Standard Business</value>
  </data>
  <data name="PdfViewer_CustomStamp" xml:space="preserve">
    <value>Custom Stamp</value>
  </data>
  <data name="PdfViewer_Revised" xml:space="preserve">
    <value>Revised</value>
  </data>
  <data name="PdfViewer_Reviewed" xml:space="preserve">
    <value>Reviewed</value>
  </data>
  <data name="PdfViewer_Received" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="PdfViewer_Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="PdfViewer_Confidential" xml:space="preserve">
    <value>Confidential</value>
  </data>
  <data name="PdfViewer_NotApproved" xml:space="preserve">
    <value>Not Approved</value>
  </data>
  <data name="PdfViewer_Witness" xml:space="preserve">
    <value>Witness</value>
  </data>
  <data name="PdfViewer_InitialHere" xml:space="preserve">
    <value>Initial Here</value>
  </data>
  <data name="PdfViewer_SignHere" xml:space="preserve">
    <value>Sign Here</value>
  </data>
  <data name="PdfViewer_Draft" xml:space="preserve">
    <value>Draft</value>
  </data>
  <data name="PdfViewer_Final" xml:space="preserve">
    <value>Final</value>
  </data>
  <data name="PdfViewer_ForPublicRelease" xml:space="preserve">
    <value>For Public Release</value>
  </data>
  <data name="PdfViewer_NotForPublicRelease" xml:space="preserve">
    <value>Not For Public Release</value>
  </data>
  <data name="PdfViewer_ForComment" xml:space="preserve">
    <value>For Comment</value>
  </data>
  <data name="PdfViewer_Void" xml:space="preserve">
    <value>Void</value>
  </data>
  <data name="PdfViewer_PreliminaryResults" xml:space="preserve">
    <value>Preliminary Results</value>
  </data>
  <data name="PdfViewer_InformationOnly" xml:space="preserve">
    <value>Information Only</value>
  </data>
  <data name="PdfViewer_SubmitForm" xml:space="preserve">
    <value>Submit Form</value>
  </data>
  <data name="PdfViewer_Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="PdfViewer_Required_ServiceUrl_Error_Message" xml:space="preserve">
    <value>This PDF Viewer requires Server-side processing to render the PDF files through the web service. You must configure the ServiceURL to proceed with PDF Viewer</value>
  </data>
  <data name="PdfViewer_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="PdfViewer_HandwrittenSignatureDialogHeaderText" xml:space="preserve">
    <value>Add Signature</value>
  </data>
  <data name="PdfViewer_DrawInk" xml:space="preserve">
    <value>Draw Ink</value>
  </data>
  <data name="FileManager_NewFolder" xml:space="preserve">
    <value>New folder</value>
  </data>
  <data name="FileManager_Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="FileManager_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="FileManager_Rename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="FileManager_Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="FileManager_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="FileManager_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="FileManager_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="FileManager_SortBy" xml:space="preserve">
    <value>Sort by</value>
  </data>
  <data name="FileManager_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="FileManager_ItemSelection" xml:space="preserve">
    <value>item selected</value>
  </data>
  <data name="FileManager_ItemsSelection" xml:space="preserve">
    <value>items selected</value>
  </data>
  <data name="FileManager_View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="FileManager_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FileManager_SelectAll" xml:space="preserve">
    <value>Select all</value>
  </data>
  <data name="FileManager_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="FileManager_TooltipNewFolder" xml:space="preserve">
    <value>New folder</value>
  </data>
  <data name="FileManager_TooltipUpload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="FileManager_TooltipDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="FileManager_TooltipRename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="FileManager_TooltipDownload" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="FileManager_TooltipCut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="FileManager_TooltipCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="FileManager_TooltipPaste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="FileManager_TooltipSortBy" xml:space="preserve">
    <value>Sort by</value>
  </data>
  <data name="FileManager_TooltipRefresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="FileManager_TooltipSelection" xml:space="preserve">
    <value>Clear selection</value>
  </data>
  <data name="FileManager_TooltipView" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="FileManager_TooltipDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FileManager_TooltipSelectAll" xml:space="preserve">
    <value>Select all</value>
  </data>
  <data name="FileManager_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FileManager_Size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="FileManager_DateModified" xml:space="preserve">
    <value>Modified</value>
  </data>
  <data name="FileManager_DateCreated" xml:space="preserve">
    <value>Date created</value>
  </data>
  <data name="FileManager_Path" xml:space="preserve">
    <value>Path</value>
  </data>
  <data name="FileManager_Modified" xml:space="preserve">
    <value>Modified</value>
  </data>
  <data name="FileManager_Created" xml:space="preserve">
    <value>Created</value>
  </data>
  <data name="FileManager_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="FileManager_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="FileManager_Permission" xml:space="preserve">
    <value>Permission</value>
  </data>
  <data name="FileManager_Ascending" xml:space="preserve">
    <value>Ascending</value>
  </data>
  <data name="FileManager_Descending" xml:space="preserve">
    <value>Descending</value>
  </data>
  <data name="FileManager_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="FileManager_ViewLargeIcons" xml:space="preserve">
    <value>Large icons</value>
  </data>
  <data name="FileManager_ViewDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FileManager_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="FileManager_ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="FileManager_ButtonCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FileManager_ButtonYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="FileManager_ButtonNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="FileManager_ButtonCreate" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="FileManager_ButtonSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="FileManager_HeaderNewFolder" xml:space="preserve">
    <value>Folder</value>
  </data>
  <data name="FileManager_ContentNewFolder" xml:space="preserve">
    <value>Enter your folder name</value>
  </data>
  <data name="FileManager_HeaderRename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="FileManager_ContentRename" xml:space="preserve">
    <value>Enter your new name</value>
  </data>
  <data name="FileManager_HeaderRenameConfirmation" xml:space="preserve">
    <value>Rename Confirmation</value>
  </data>
  <data name="FileManager_ContentRenameConfirmation" xml:space="preserve">
    <value>If you change a file name extension, the file might become unstable. Are you sure you want to change it?</value>
  </data>
  <data name="FileManager_HeaderDelete" xml:space="preserve">
    <value>Delete File</value>
  </data>
  <data name="FileManager_ContentDelete" xml:space="preserve">
    <value>Are you sure you want to delete this file?</value>
  </data>
  <data name="FileManager_HeaderFolderDelete" xml:space="preserve">
    <value>Delete Folder</value>
  </data>
  <data name="FileManager_ContentFolderDelete" xml:space="preserve">
    <value>Are you sure you want to delete this folder?</value>
  </data>
  <data name="FileManager_HeaderMultipleDelete" xml:space="preserve">
    <value>Delete Multiple Items</value>
  </data>
  <data name="FileManager_ContentMultipleDelete" xml:space="preserve">
    <value>Are you sure you want to delete these {0} items?</value>
  </data>
  <data name="FileManager_HeaderDuplicate" xml:space="preserve">
    <value>File/Folder exists</value>
  </data>
  <data name="FileManager_ContentDuplicate" xml:space="preserve">
    <value>{0} already exists. Do you want to rename and paste?</value>
  </data>
  <data name="FileManager_HeaderUpload" xml:space="preserve">
    <value>Upload Files</value>
  </data>
  <data name="FileManager_Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FileManager_ValidationEmpty" xml:space="preserve">
    <value>The file or folder name cannot be empty.</value>
  </data>
  <data name="FileManager_ValidationInvalid" xml:space="preserve">
    <value>The file or folder name {0} contains invalid characters. Please use a different name. Valid file or folder names cannot end with a dot or space.</value>
  </data>
  <data name="FileManager_ValidationNewFolderExists" xml:space="preserve">
    <value>A file or folder with the name {0} already exists.</value>
  </data>
  <data name="FileManager_ValidationRenameExists" xml:space="preserve">
    <value>Cannot rename {0} to {1}: destination already exists.</value>
  </data>
  <data name="FileManager_FolderEmpty" xml:space="preserve">
    <value>This folder is empty</value>
  </data>
  <data name="FileManager_FileUpload" xml:space="preserve">
    <value>Drag files here to upload</value>
  </data>
  <data name="FileManager_SearchEmpty" xml:space="preserve">
    <value>No results found</value>
  </data>
  <data name="FileManager_SearchKey" xml:space="preserve">
    <value>Try with different keywords</value>
  </data>
  <data name="FileManager_FilterEmpty" xml:space="preserve">
    <value>No results found</value>
  </data>
  <data name="FileManager_FilterKey" xml:space="preserve">
    <value>Try with different filter</value>
  </data>
  <data name="FileManager_SubFolderError" xml:space="preserve">
    <value>The destination folder is the subfolder of the source folder.</value>
  </data>
  <data name="FileManager_AccessDenied" xml:space="preserve">
    <value>Access Denied</value>
  </data>
  <data name="FileManager_AccessDetails" xml:space="preserve">
    <value>You don't have permission to access this folder.</value>
  </data>
  <data name="FileManager_HeaderRetry" xml:space="preserve">
    <value>File Already Exists</value>
  </data>
  <data name="FileManager_ContentRetry" xml:space="preserve">
    <value>A file with this name already exists in this folder. What would you like to do?</value>
  </data>
  <data name="FileManager_ButtonKeepBoth" xml:space="preserve">
    <value>Keep both</value>
  </data>
  <data name="FileManager_ButtonReplace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="FileManager_ButtonSkip" xml:space="preserve">
    <value>Skip</value>
  </data>
  <data name="FileManager_ApplyAllLabel" xml:space="preserve">
    <value>Do this for all current items</value>
  </data>
  <data name="FileManager_KB" xml:space="preserve">
    <value>KB</value>
  </data>
  <data name="FileManager_AccessMessage" xml:space="preserve">
    <value>{0} is not accessible. you need permission to perform the {1} action.</value>
  </data>
  <data name="FileManager_NetworkError" xml:space="preserve">
    <value>NetworkError: Failed to send on XMLHTTPRequest: Failed to load</value>
  </data>
  <data name="FileManager_ServerError" xml:space="preserve">
    <value>ServerError: Invalid response from</value>
  </data>
  <data name="Gantt_Above" xml:space="preserve">
    <value>Above</value>
  </data>
  <data name="Gantt_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Gantt_AddDialogTitle" xml:space="preserve">
    <value>New Task</value>
  </data>
  <data name="Gantt_BaselineEndDate" xml:space="preserve">
    <value>Baseline End Date</value>
  </data>
  <data name="Gantt_BaselineStartDate" xml:space="preserve">
    <value>Baseline Start Date</value>
  </data>
  <data name="Gantt_Below" xml:space="preserve">
    <value>Below</value>
  </data>
  <data name="Gantt_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Gantt_Child" xml:space="preserve">
    <value>Child</value>
  </data>
  <data name="Gantt_CollapseAll" xml:space="preserve">
    <value>Collapse All</value>
  </data>
  <data name="Gantt_ConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to Delete Record?</value>
  </data>
  <data name="Gantt_ConfirmPredecessorDelete" xml:space="preserve">
    <value>Are you sure you want to remove dependency link?</value>
  </data>
  <data name="Gantt_Indent" xml:space="preserve">
    <value>Indent</value>
  </data>
  <data name="Gantt_Outdent" xml:space="preserve">
    <value>Outdent</value>
  </data>
  <data name="Gantt_Convert" xml:space="preserve">
    <value>Convert</value>
  </data>
  <data name="Gantt_CsvExport" xml:space="preserve">
    <value>CSV Export</value>
  </data>
  <data name="Gantt_CustomTab" xml:space="preserve">
    <value>Custom Columns</value>
  </data>
  <data name="Gantt_Day" xml:space="preserve">
    <value>day</value>
  </data>
  <data name="Gantt_Days" xml:space="preserve">
    <value>days</value>
  </data>
  <data name="Gantt_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Gantt_DeleteDependency" xml:space="preserve">
    <value>Delete Dependency</value>
  </data>
  <data name="Gantt_DeleteTask" xml:space="preserve">
    <value>Delete Task</value>
  </data>
  <data name="Gantt_Dependency" xml:space="preserve">
    <value>Dependency</value>
  </data>
  <data name="Gantt_Duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="Gantt_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Gantt_EditDialogTitle" xml:space="preserve">
    <value>Task Information</value>
  </data>
  <data name="Gantt_EmptyRecord" xml:space="preserve">
    <value>No records to display</value>
  </data>
  <data name="Gantt_EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="Gantt_EnterValue" xml:space="preserve">
    <value>Enter the value</value>
  </data>
  <data name="Gantt_EventMarkers" xml:space="preserve">
    <value>Event markers</value>
  </data>
  <data name="Gantt_ExcelExport" xml:space="preserve">
    <value>Excel Export</value>
  </data>
  <data name="Gantt_ExpandAll" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="Gantt_Finish" xml:space="preserve">
    <value>Finish</value>
  </data>
  <data name="Gantt_From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Gantt_GeneralTab" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="Gantt_Hour" xml:space="preserve">
    <value>hour</value>
  </data>
  <data name="Gantt_Hours" xml:space="preserve">
    <value>hours</value>
  </data>
  <data name="Gantt_Id" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Gantt_Lag" xml:space="preserve">
    <value>Lag</value>
  </data>
  <data name="Gantt_LeftTaskLabel" xml:space="preserve">
    <value>Left task label</value>
  </data>
  <data name="Gantt_Milestone" xml:space="preserve">
    <value>Milestone</value>
  </data>
  <data name="Gantt_Minute" xml:space="preserve">
    <value>minute</value>
  </data>
  <data name="Gantt_Minutes" xml:space="preserve">
    <value>minutes</value>
  </data>
  <data name="Gantt_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Gantt_NextTimeSpan" xml:space="preserve">
    <value>Next Timespan</value>
  </data>
  <data name="Gantt_Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Gantt_Offset" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="Gantt_OkText" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="Gantt_PrevTimeSpan" xml:space="preserve">
    <value>Previous Timespan</value>
  </data>
  <data name="Gantt_Progress" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="Gantt_ResourceID" xml:space="preserve">
    <value>Resource ID</value>
  </data>
  <data name="Gantt_ResourceName" xml:space="preserve">
    <value>Resources</value>
  </data>
  <data name="Gantt_RightTaskLabel" xml:space="preserve">
    <value>Right task label</value>
  </data>
  <data name="Gantt_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Gantt_SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Gantt_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Gantt_Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="Gantt_StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="Gantt_Task" xml:space="preserve">
    <value>task</value>
  </data>
  <data name="Gantt_TaskAfterPredecessor_FF" xml:space="preserve">
    <value>You moved '{0}' to finish after '{1}' finishes and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskAfterPredecessor_FS" xml:space="preserve">
    <value>You moved '{0}' away from '{1}' and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskAfterPredecessor_SF" xml:space="preserve">
    <value>You moved '{0}' to finish after '{1}'starts and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskAfterPredecessor_SS" xml:space="preserve">
    <value>You moved '{0}' to start after '{1}' starts and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskBeforePredecessor_FF" xml:space="preserve">
    <value>You moved '{0}' to finish before '{1}' finishes and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskBeforePredecessor_FS" xml:space="preserve">
    <value>You moved '{0}' to start before '{1}' finishes and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskBeforePredecessor_SF" xml:space="preserve">
    <value>You moved '{0}' away from '{1}' to starts and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskBeforePredecessor_SS" xml:space="preserve">
    <value>You moved '{0}' to start before '{1}' starts and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_TaskInformation" xml:space="preserve">
    <value>Task Information</value>
  </data>
  <data name="Gantt_TaskLink" xml:space="preserve">
    <value>Task Link</value>
  </data>
  <data name="Gantt_Tasks" xml:space="preserve">
    <value>tasks</value>
  </data>
  <data name="Gantt_TimelineCell" xml:space="preserve">
    <value>Timeline cell</value>
  </data>
  <data name="Gantt_To" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Gantt_ToMilestone" xml:space="preserve">
    <value>To Milestone</value>
  </data>
  <data name="Gantt_ToTask" xml:space="preserve">
    <value>To Task</value>
  </data>
  <data name="Gantt_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Gantt_Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Gantt_WriteNotes" xml:space="preserve">
    <value>Write Notes</value>
  </data>
  <data name="Gantt_ZoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="Gantt_ZoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="Gantt_ZoomToFit" xml:space="preserve">
    <value>Zoom To Fit</value>
  </data>
  <data name="Gantt_AutoFit" xml:space="preserve">
    <value>AutoFit Column</value>
  </data>
  <data name="Gantt_AutoFitAll" xml:space="preserve">
    <value>AutoFit All Columns</value>
  </data>
  <data name="Gantt_SortAscending" xml:space="preserve">
    <value>Sort Ascending</value>
  </data>
  <data name="Gantt_SortDescending" xml:space="preserve">
    <value>Sort Descending</value>
  </data>
  <data name="Gantt_TaskMode" xml:space="preserve">
    <value>Task Mode</value>
  </data>
  <data name="Gantt_TaskMode_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="Gantt_TaskMode_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="Gantt_SubTaskStartDate" xml:space="preserve">
    <value>SubTasks Start Date</value>
  </data>
  <data name="Gantt_SubTaskEndDate" xml:space="preserve">
    <value>SubTasks End Date</value>
  </data>
  <data name="Gantt_AutoDuration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="Gantt_Work" xml:space="preserve">
    <value>Work</value>
  </data>
  <data name="Gantt_H" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="Gantt_D" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="Gantt_M" xml:space="preserve">
    <value>M</value>
  </data>
  <data name="Gantt_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="Gantt_CustomColumn" xml:space="preserve">
    <value>Custom Columns</value>
  </data>
  <data name="Gantt_TaskType" xml:space="preserve">
    <value>Task Type</value>
  </data>
  <data name="Gantt_CriticalPath" xml:space="preserve">
    <value>Critical Path</value>
  </data>
  <data name="Gantt_Segment" xml:space="preserve">
    <value>Segments</value>
  </data>
  <data name="Gantt_SplitTask" xml:space="preserve">
    <value>Split Task</value>
  </data>
  <data name="Gantt_MergeTask" xml:space="preserve">
    <value>Merge Task</value>
  </data>
  <data name="Grid_EmptyRecord" xml:space="preserve">
    <value>No records to display</value>
  </data>
  <data name="Grid_True" xml:space="preserve">
    <value>true</value>
  </data>
  <data name="Grid_False" xml:space="preserve">
    <value>false</value>
  </data>
  <data name="Grid_InvalidFilterMessage" xml:space="preserve">
    <value>Invalid Filter Data</value>
  </data>
  <data name="Grid_GroupDropArea" xml:space="preserve">
    <value>Drag a column header here to group its column</value>
  </data>
  <data name="Grid_UnGroupButton" xml:space="preserve">
    <value>Click here to ungroup</value>
  </data>
  <data name="Grid_GroupDisable" xml:space="preserve">
    <value>Grouping is disabled for this column</value>
  </data>
  <data name="Grid_FilterbarTitle" xml:space="preserve">
    <value>\'s filter bar cell</value>
  </data>
  <data name="Grid_EmptyDataSourceError" xml:space="preserve">
    <value>DataSource must not be empty at initial load since columns are generated from dataSource in AutoGenerate Column Grid</value>
  </data>
  <data name="Grid_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Grid_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Grid_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Grid_Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Grid_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Grid_Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Grid_Pdfexport" xml:space="preserve">
    <value>PDF Export</value>
  </data>
  <data name="Grid_Excelexport" xml:space="preserve">
    <value>Excel Export</value>
  </data>
  <data name="Grid_Wordexport" xml:space="preserve">
    <value>Word Export</value>
  </data>
  <data name="Grid_Csvexport" xml:space="preserve">
    <value>CSV Export</value>
  </data>
  <data name="Grid_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Grid_Columnchooser" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="Grid_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Grid_Item" xml:space="preserve">
    <value>item</value>
  </data>
  <data name="Grid_Items" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="Grid_EditOperationAlert" xml:space="preserve">
    <value>No records selected for edit operation</value>
  </data>
  <data name="Grid_DeleteOperationAlert" xml:space="preserve">
    <value>No records selected for delete operation</value>
  </data>
  <data name="Grid_SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Grid_OKButton" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Grid_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Grid_EditFormTitle" xml:space="preserve">
    <value>Details of </value>
  </data>
  <data name="Grid_AddFormTitle" xml:space="preserve">
    <value>Add New Record</value>
  </data>
  <data name="Grid_BatchSaveConfirm" xml:space="preserve">
    <value>Are you sure you want to save changes?</value>
  </data>
  <data name="Grid_BatchSaveLostChanges" xml:space="preserve">
    <value>Unsaved changes will be lost. Are you sure you want to continue?</value>
  </data>
  <data name="Grid_ConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to Delete Record?</value>
  </data>
  <data name="Grid_CancelEdit" xml:space="preserve">
    <value>Are you sure you want to Cancel the changes?</value>
  </data>
  <data name="Grid_ChooseColumns" xml:space="preserve">
    <value>Choose Column</value>
  </data>
  <data name="Grid_SearchColumns" xml:space="preserve">
    <value>search columns</value>
  </data>
  <data name="Grid_Matchs" xml:space="preserve">
    <value>No Matches Found</value>
  </data>
  <data name="Grid_FilterButton" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Grid_ClearButton" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Grid_IsNull" xml:space="preserve">
    <value>Null</value>
  </data>
  <data name="Grid_IsNotNull" xml:space="preserve">
    <value>Not Null</value>
  </data>
  <data name="Grid_IsEmpty" xml:space="preserve">
    <value>Empty</value>
  </data>
  <data name="Grid_IsNotEmpty" xml:space="preserve">
    <value>Not Empty</value>
  </data>
  <data name="Grid_Like" xml:space="preserve">
    <value>Like</value>
  </data>
  <data name="Grid_StartsWith" xml:space="preserve">
    <value>Starts With</value>
  </data>
  <data name="Grid_EndsWith" xml:space="preserve">
    <value>Ends With</value>
  </data>
  <data name="Grid_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="Grid_DoesNotStartWith" xml:space="preserve">
    <value>Does Not Start With</value>
  </data>
  <data name="Grid_DoesNotEndWith" xml:space="preserve">
    <value>Does Not End With</value>
  </data>
  <data name="Grid_DoesNotContain" xml:space="preserve">
    <value>Does Not Contain</value>
  </data>
  <data name="Grid_Equal" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="Grid_NotEqual" xml:space="preserve">
    <value>Not Equal</value>
  </data>
  <data name="Grid_LessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="Grid_LessThanOrEqual" xml:space="preserve">
    <value>Less Than Or Equal</value>
  </data>
  <data name="Grid_GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="Grid_GreaterThanOrEqual" xml:space="preserve">
    <value>Greater Than Or Equal</value>
  </data>
  <data name="Grid_ChooseDate" xml:space="preserve">
    <value>Choose a Date</value>
  </data>
  <data name="Grid_EnterValue" xml:space="preserve">
    <value>Enter the value</value>
  </data>
  <data name="Grid_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="Grid_Group" xml:space="preserve">
    <value>Group by this column</value>
  </data>
  <data name="Grid_Ungroup" xml:space="preserve">
    <value>Ungroup by this column</value>
  </data>
  <data name="Grid_AutoFitAll" xml:space="preserve">
    <value>Autofit all columns</value>
  </data>
  <data name="Grid_AutoFit" xml:space="preserve">
    <value>Autofit this column</value>
  </data>
  <data name="Grid_Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Grid_FirstPage" xml:space="preserve">
    <value>First Page</value>
  </data>
  <data name="Grid_LastPage" xml:space="preserve">
    <value>Last Page</value>
  </data>
  <data name="Grid_PreviousPage" xml:space="preserve">
    <value>Previous Page</value>
  </data>
  <data name="Grid_NextPage" xml:space="preserve">
    <value>Next Page</value>
  </data>
  <data name="Grid_SortAscending" xml:space="preserve">
    <value>Sort Ascending</value>
  </data>
  <data name="Grid_SortDescending" xml:space="preserve">
    <value>Sort Descending</value>
  </data>
  <data name="Grid_SortedAscending" xml:space="preserve">
    <value>Sorted in ascending order</value>
  </data>
  <data name="Grid_SortedDescending" xml:space="preserve">
    <value>Sorted in descending order</value>
  </data>
  <data name="Grid_EditRecord" xml:space="preserve">
    <value>Edit Record</value>
  </data>
  <data name="Grid_DeleteRecord" xml:space="preserve">
    <value>Delete Record</value>
  </data>
  <data name="Grid_FilterMenu" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Grid_SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="Grid_AddCurrentSelection" xml:space="preserve">
    <value>Add current selection to filter</value>
  </data>
  <data name="Grid_Blanks" xml:space="preserve">
    <value>Blanks</value>
  </data>
  <data name="Grid_FilterTrue" xml:space="preserve">
    <value>True</value>
  </data>
  <data name="Grid_FilterFalse" xml:space="preserve">
    <value>False</value>
  </data>
  <data name="Grid_NoResult" xml:space="preserve">
    <value>No Matches Found</value>
  </data>
  <data name="Grid_ClearFilter" xml:space="preserve">
    <value>Clear Filter</value>
  </data>
  <data name="Grid_NumberFilter" xml:space="preserve">
    <value>Number Filters</value>
  </data>
  <data name="Grid_TextFilter" xml:space="preserve">
    <value>Text Filters</value>
  </data>
  <data name="Grid_DateFilter" xml:space="preserve">
    <value>Date Filters</value>
  </data>
  <data name="Grid_DateTimeFilter" xml:space="preserve">
    <value>DateTime Filters</value>
  </data>
  <data name="Grid_MatchCase" xml:space="preserve">
    <value>Match Case</value>
  </data>
  <data name="Grid_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="Grid_CustomFilter" xml:space="preserve">
    <value>Custom Filter</value>
  </data>
  <data name="Grid_CustomFilterPlaceHolder" xml:space="preserve">
    <value>Enter the value</value>
  </data>
  <data name="Grid_CustomFilterDatePlaceHolder" xml:space="preserve">
    <value>Choose a date</value>
  </data>
  <data name="Grid_AND" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="Grid_OR" xml:space="preserve">
    <value>OR</value>
  </data>
  <data name="Grid_ShowRowsWhere" xml:space="preserve">
    <value>Show rows where:</value>
  </data>
  <data name="Grid_RowSelectionCheckBoxARIA" xml:space="preserve">
    <value>Row checkbox</value>
  </data>
  <data name="Grid_FilterMenuIconARIA" xml:space="preserve">
    <value>Filter Icon</value>
  </data>
  <data name="Grid_ColumnMenuIconARIA" xml:space="preserve">
    <value>Column Menu Icon</value>
  </data>
  <data name="Grid_GroupButtonARIA" xml:space="preserve">
    <value>Group Button</value>
  </data>
  <data name="Grid_UnGroupButtonARIA" xml:space="preserve">
    <value>Ungroup Button</value>
  </data>
  <data name="Grid_ColumnHeaderARIA" xml:space="preserve">
    <value>Column Header</value>
  </data>
  <data name="Grid_FilterCheckboxARIA" xml:space="preserve">
    <value>Filter checkbox</value>
  </data>
  <data name="Grid_HeaderSelectionCheckBoxARIA" xml:space="preserve">
    <value>Header checkbox</value>
  </data>
  <data name="Grid_Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="Grid_Ascending" xml:space="preserve">
    <value>Ascending</value>
  </data>
  <data name="Grid_Descending" xml:space="preserve">
    <value>Descending</value>
  </data>
  <data name="Grid_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Grid_TemplateColumnARIA" xml:space="preserve">
    <value>is template cell</value>
  </data>
  <data name="Grid_SortDescription" xml:space="preserve">
    <value>Press Enter to Sort</value>
  </data>
  <data name="Grid_FilterDescription" xml:space="preserve">
    <value>Press Alt Down to open filter Menu</value>
  </data>
  <data name="Grid_ColumnMenuDescription" xml:space="preserve">
    <value>Press Alt Down to open Column Menu</value>
  </data>
  <data name="Grid_GroupDescription" xml:space="preserve">
    <value>Press Ctrl space to group</value>
  </data>
  <data name="Grid_GroupCaption" xml:space="preserve">
    <value>is group caption cell</value>
  </data>
  <data name="Grid_ColumnHeaderUndefinedARIA" xml:space="preserve">
    <value>column header undefined</value>
  </data>
  <data name="Grid_CommandColumnARIA" xml:space="preserve">
    <value>Is Command Column</value>
  </data>
  <data name="Grid_GroupedSortIcon" xml:space="preserve">
    <value>sort the grouped column</value>
  </data>
  <data name="Grid_EmptyColumnHeaderUndefinedARIA" xml:space="preserve">
    <value>empty Column Header undefined</value>
  </data>
  <data name="Grid_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Grid_FilterOperator" xml:space="preserve">
    <value>Filter Operator</value>
  </data>
  <data name="Grid_FilterValue" xml:space="preserve">
    <value>Filter Value</value>
  </data>
  <data name="Grid_SelectRowARIA" xml:space="preserve">
    <value>Select row</value>
  </data>
  <data name="Pager_Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="Pager_Pages" xml:space="preserve">
    <value>Pages</value>
  </data>
  <data name="Pager_Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="Pager_ExternalMessage" xml:space="preserve">
    <value>Pager external message</value>
  </data>
  <data name="Pager_ShowPages" xml:space="preserve">
    <value>pages</value>
  </data>
  <data name="Pager_PagerDropDownARIA" xml:space="preserve">
    <value>Pager dropdown</value>
  </data>
  <data name="Pager_GotoPageARIA" xml:space="preserve">
    <value>Goto Page</value>
  </data>
  <data name="Pager_CurrentPageInfo" xml:space="preserve">
    <value>{0} of {1} pages</value>
  </data>
  <data name="Pager_TotalItemsInfo" xml:space="preserve">
    <value>({0} items)</value>
  </data>
  <data name="Pager_FirstPageTooltip" xml:space="preserve">
    <value>Go to first page</value>
  </data>
  <data name="Pager_LastPageTooltip" xml:space="preserve">
    <value>Go to last page</value>
  </data>
  <data name="Pager_NextPageTooltip" xml:space="preserve">
    <value>Go to next page</value>
  </data>
  <data name="Pager_PreviousPageTooltip" xml:space="preserve">
    <value>Go to previous page</value>
  </data>
  <data name="Pager_NextPagerTooltip" xml:space="preserve">
    <value>Go to next pager</value>
  </data>
  <data name="Pager_PreviousPagerTooltip" xml:space="preserve">
    <value>Go to previous pager</value>
  </data>
  <data name="Pager_PagerDropDown" xml:space="preserve">
    <value>Items per page</value>
  </data>
  <data name="Pager_PagerAllDropDown" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="Pager_All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Pager_Items" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="DocumentEditorContainer_FillingInForms" xml:space="preserve">
    <value>Filling in forms</value>
  </data>
  <data name="DocumentEditorContainer_Trackedchanges" xml:space="preserve">
    <value>Tracked changes</value>
  </data>
  <data name="DocumentEditorContainer_RemovedIgnoreExceptions" xml:space="preserve">
    <value>If you make this change in document protection, Word will ignore all the exceptions in this document.</value>
  </data>
  <data name="DocumentEditor_RevisionsOnly" xml:space="preserve">
    <value>You may edit in this region, but all change will be tracked</value>
  </data>
  <data name="DocumentEditor_UnsupportedFormat" xml:space="preserve">
    <value>The file format you have selected isn't supported. Please choose valid format.</value>
  </data>
  <data name="DocumentEditor_InvalidFile" xml:space="preserve">
    <value>There was a problem in opening this document</value>
  </data>
  <data name="DocumentEditorContainer_RemovedIgnore" xml:space="preserve">
    <value>Do you want to remove the ignored exceptions?</value>
  </data>
  <data name="DocumentEditorContainer_Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="DocumentEditorContainer_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="DocumentEditorContainer_ShowPropertiesPane" xml:space="preserve">
    <value>Show properties pane</value>
  </data>
  <data name="DocumentEditorContainer_HidePropertiesPane" xml:space="preserve">
    <value>Hide properties pane</value>
  </data>
  <data name="DocumentEditorContainer_SectionBreakContinuous" xml:space="preserve">
    <value>Continuous</value>
  </data>
  <data name="DocumentEditorContainer_ColumnBreak" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="DocumentEditorContainer_PageBreak" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="DocumentEditorContainer_SectionBreak" xml:space="preserve">
    <value>Next Page</value>
  </data>
  <data name="DocumentEditorContainer_PageBreaks" xml:space="preserve">
    <value>Page Breaks</value>
  </data>
  <data name="DocumentEditorContainer_SectionBreaks" xml:space="preserve">
    <value>Section Breaks</value>
  </data>
  <data name="DocumentEditorContainer_UnsupportedHyperLink" xml:space="preserve">
    <value>The address of this site is not valid. Check the address and try again.</value>
  </data>
  <data name="DocumentEditorContainer_Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Gantt_Touch_DialogTextOnClickingConnectorPoint" xml:space="preserve">
    <value>Choose the another task</value>
  </data>
  <data name="Gantt_Touch_DialogTextOnSelectingTaskbar" xml:space="preserve">
    <value>Select the connector position</value>
  </data>
  <data name="Gantt_Touch_LeftButton" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="Gantt_Touch_RightButton" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="PdfViewer_ExportXFDF" xml:space="preserve">
    <value>Export annotations to XFDF file</value>
  </data>
  <data name="PdfViewer_ImportXFDF" xml:space="preserve">
    <value>Import annotations from XFDF file</value>
  </data>
  <data name="Predefined_Dialog_Title" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="PivotView_StackedLine" xml:space="preserve">
    <value>Stacked Line</value>
  </data>
  <data name="PivotView_StackedLine100" xml:space="preserve">
    <value>100% Stacked Line</value>
  </data>
  <data name="PivotView_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Skeleton_Label" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="Dialog_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Dialog_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PivotView_DoNotShowSubtotals" xml:space="preserve">
    <value>Do not show subtotals</value>
  </data>
  <data name="PivotView_ShowColumnSubtotalsOnly" xml:space="preserve">
    <value>Show subtotals columns only</value>
  </data>
  <data name="PivotView_ShowRowSubtotalsOnly" xml:space="preserve">
    <value>Show subtotals rows only</value>
  </data>
  <data name="PivotView_ShowSubtotals" xml:space="preserve">
    <value>Show subtotals</value>
  </data>
  <data name="PivotView_SubtotalsPosition" xml:space="preserve">
    <value>Subtotals position</value>
  </data>
  <data name="PivotView_Single" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="PivotView_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="PivotView_RowPager" xml:space="preserve">
    <value>Row pager</value>
  </data>
  <data name="PivotView_RowsPerPage" xml:space="preserve">
    <value>Rows per page</value>
  </data>
  <data name="PivotView_ColumnPager" xml:space="preserve">
    <value>Column pager</value>
  </data>
  <data name="PivotView_ColumnsPerPage" xml:space="preserve">
    <value>Columns per page</value>
  </data>
  <data name="PivotView_GoToFirstPage" xml:space="preserve">
    <value>Go to first page</value>
  </data>
  <data name="PivotView_GoToLastPage" xml:space="preserve">
    <value>Go to last page</value>
  </data>
  <data name="PivotView_GoToNextPage" xml:space="preserve">
    <value>Go to next page</value>
  </data>
  <data name="PivotView_GoToPreviousPage" xml:space="preserve">
    <value>Go to previous page</value>
  </data>
  <data name="Grid_ChooseTime" xml:space="preserve">
    <value>Choose a Time</value>
  </data>
  <data name="Grid_TimeFilter" xml:space="preserve">
    <value>Time Filters</value>
  </data>
  <data name="DocumentEditor_SameAsPrevious" xml:space="preserve">
    <value>Same as Previous</value>
  </data>
  <data name="DocumentEditor_Section" xml:space="preserve">
    <value>Section</value>
  </data>
  <data name="DocumentEditor_OddPageHeader" xml:space="preserve">
    <value>Odd Page Header</value>
  </data>
  <data name="DocumentEditor_OddPageFooter" xml:space="preserve">
    <value>Odd Page Footer</value>
  </data>
  <data name="DocumentEditor_EvenPageHeader" xml:space="preserve">
    <value>Even Page Header</value>
  </data>
  <data name="DocumentEditor_EvenPageFooter" xml:space="preserve">
    <value>Even Page Footer</value>
  </data>
  <data name="DocumentEditor_FirstPageHeader" xml:space="preserve">
    <value>First Page Header</value>
  </data>
  <data name="DocumentEditor_FirstPageFooter" xml:space="preserve">
    <value>First Page Footer</value>
  </data>
  <data name="DocumentEditor_And" xml:space="preserve">
    <value>and</value>
  </data>
  <data name="DocumentEditor_Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="DocumentEditor_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="DocumentEditor_AltText" xml:space="preserve">
    <value>Alt Text</value>
  </data>
  <data name="DocumentEditorContainer_TabStopPosition" xml:space="preserve">
    <value>Tab stop position</value>
  </data>
  <data name="DocumentEditorContainer_DefaultTabStops" xml:space="preserve">
    <value>Default tab stops</value>
  </data>
  <data name="DocumentEditorContainer_TabStopsCleared" xml:space="preserve">
    <value>Tab stops to be cleared</value>
  </data>
  <data name="DocumentEditorContainer_Tabs" xml:space="preserve">
    <value>Tabs</value>
  </data>
  <data name="DocumentEditor_Set" xml:space="preserve">
    <value>Set</value>
  </data>
  <data name="DocumentEditor_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="DocumentEditor_ClearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  <data name="DocumentEditor_Decimal" xml:space="preserve">
    <value>Decimal</value>
  </data>
  <data name="DocumentEditor_Bar" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="DocumentEditorContainer_Alignment" xml:space="preserve">
    <value>Alignment</value>
  </data>
  <data name="DocumentEditorContainer_Leader" xml:space="preserve">
    <value>Leader</value>
  </data>
  <data name="DocumentEditorContainer_All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="DocumentEditor_BottomMargin" xml:space="preserve">
    <value>Bottom Margin</value>
  </data>
  <data name="DocumentEditor_FirstLineIndent" xml:space="preserve">
    <value>First Line Indent</value>
  </data>
  <data name="DocumentEditor_HangingIndent" xml:space="preserve">
    <value>Hanging Indent</value>
  </data>
  <data name="DocumentEditor_LeftIndent" xml:space="preserve">
    <value>Left Indent</value>
  </data>
  <data name="DocumentEditor_LeftMargin" xml:space="preserve">
    <value>Left Margin</value>
  </data>
  <data name="DocumentEditor_MoveTableColumn" xml:space="preserve">
    <value>Move Table Column</value>
  </data>
  <data name="DocumentEditor_RightIndent" xml:space="preserve">
    <value>Right Indent</value>
  </data>
  <data name="DocumentEditor_RightMargin" xml:space="preserve">
    <value>Right Margin</value>
  </data>
  <data name="DocumentEditor_TopMargin" xml:space="preserve">
    <value>Top Margin</value>
  </data>
  <data name="DocumentEditor_LeftTab" xml:space="preserve">
    <value>Left Tab</value>
  </data>
  <data name="DocumentEditor_CenterTab" xml:space="preserve">
    <value>Center Tab</value>
  </data>
  <data name="DocumentEditor_RightTab" xml:space="preserve">
    <value>Right Tab</value>
  </data>
  <data name="DocumentEditor_DecimalTab" xml:space="preserve">
    <value>Decimal Tab</value>
  </data>
  <data name="DocumentEditor_BarTab" xml:space="preserve">
    <value>Bar Tab</value>
  </data>
  <data name="PivotView_Combined" xml:space="preserve">
    <value>Combined</value>
  </data>
</root>