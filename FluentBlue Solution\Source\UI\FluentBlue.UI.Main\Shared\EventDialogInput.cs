﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.WebApi.Shared.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Shared
{
    public class EventDialogInput
    {
        public required Event Event { get; set; }
        public RecurrentEventHandlingType RecurrentEventHandlingType { get; set; } = RecurrentEventHandlingType.Current;
    }
}
