﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DBOs.Tenants
{
    [Table("Company", Schema = "Tenants")]
    public class Company
    {
        [Key]
        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.CompanyDisplayResource), Name = "CompanyId")]
        public Guid CompanyId { get; set; }

        public Guid TenantId { get; set; }

        [Required]
        [ForeignKey("TenantId")]
        public Tenant? Tenant { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.CompanyDisplayResource), Name = "Name")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Name { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.CompanyDisplayResource), Name = "BrandName")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string BrandName { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.CompanyDisplayResource), Name = "Website")]
        [MaxLength(100)]
        public string Website { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.TenantDisplayResource), Name = "Summary")]
        public string Summary
        {
            get
            {
                return BrandName;
            }
        }

        [Display(ResourceType = typeof(Data.Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; } = new byte[0];


        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;
    }
}
