﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Authorization
{
    public class JwtHelper
    {
        public static IEnumerable<Claim> ParseClaimsFromJwt(string jwt, out Dictionary<string, Claim> claimsDictionary)
        {
            var claims = new List<Claim>();
            var payload = jwt.Split('.')[1];
            var jsonBytes = ParseBase64WithoutPadding(payload);
            var tokenContentDictionary = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonBytes);
            claimsDictionary = new Dictionary<string, Claim>();

            tokenContentDictionary!.TryGetValue(ClaimTypes.Role, out object? roles);

            if (roles != null)
            {
                claimsDictionary.Add(ClaimTypes.Role, new Claim(ClaimTypes.Role, roles.ToString()!));

                if (roles.ToString()!.Trim().StartsWith("["))
                {
                    string[] parsedRoles = JsonSerializer.Deserialize<string[]>(roles.ToString()!)!;

                    foreach (var parsedRole in parsedRoles)
                    {
                        claims.Add(new Claim(ClaimTypes.Role, parsedRole));
                    }
                }
                else
                {
                    claims.Add(new Claim(ClaimTypes.Role, roles.ToString()!));

                }

                tokenContentDictionary.Remove(ClaimTypes.Role);
            }

            claims.AddRange(tokenContentDictionary.Select(kvp => new Claim(kvp.Key, kvp.Value.ToString()!)));
            foreach (KeyValuePair<string, object> pair in tokenContentDictionary)
            {
                claimsDictionary.Add(pair.Key, new Claim(pair.Key, pair.Value.ToString()!));
            }
            return claims;
        }


        //private Dictionary<string, Claim> ParseClaimsFromJwt(string jwt)
        //{
        //    var claims = new Dictionary<string, Claim>();
        //    var payload = jwt.Split('.')[1];
        //    var jsonBytes = ParseBase64WithoutPadding(payload);
        //    Dictionary<string, object> keyValuePairs = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonBytes);

        //    keyValuePairs.TryGetValue(ClaimTypes.Role, out object roles);

        //    if (roles != null)
        //    {
        //        if (roles.ToString().Trim().StartsWith("["))
        //        {
        //            var parsedRoles = JsonSerializer.Deserialize<string[]>(roles.ToString());

        //            foreach (var parsedRole in parsedRoles)
        //            {
        //                claims.Add("Role", new Claim(ClaimTypes.Role, parsedRole));
        //            }
        //        }
        //        else
        //        {
        //            claims.Add("Role", new Claim(ClaimTypes.Role, roles.ToString()));
        //        }

        //        keyValuePairs.Remove(ClaimTypes.Role);
        //    }
        //    ////Claim newClain = new Claim(keyValuePairs.ToList()[0].Key, keyValuePairs.ToList()[0].Value.ToString());
        //    //claims.AddRange(keyValuePairs.Select(kvp => new Claim(kvp.Key, kvp.Value.ToString())));

        //    foreach (KeyValuePair<string, object> pair in keyValuePairs)
        //    {
        //        claims.Add(pair.Key, (Claim)pair.Value);
        //    }

        //    return claims;
        //}


        private static byte[] ParseBase64WithoutPadding(string base64)
        {
            switch (base64.Length % 4)
            {
                case 2: base64 += "=="; break;
                case 3: base64 += "="; break;
            }
            return Convert.FromBase64String(base64);
        }
    }
}
