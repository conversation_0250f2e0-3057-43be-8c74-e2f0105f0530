﻿CREATE TABLE [Contacts].[ContactEmail] (
    [ContactEmailId]  UNIQUEIDENTIFIER CONSTRAINT [DF_ContactEmail_ContactEmailId1] DEFAULT (newsequentialid()) NOT NULL,
    [ContactId]       UNIQUEIDENTIFIER NOT NULL,
    [Type]            TINYINT          NULL,
    [EmailAddress]    NVARCHAR (30)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_ContactEmail_EmailAddress] DEFAULT (N'') NOT NULL,
    [DateCreatedUtc]  DATETIME         CONSTRAINT [DF_ContactEmail_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc] DATETIME         CONSTRAINT [DF_ContactEmail_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_ContactEmail] PRIMARY KEY CLUSTERED ([ContactEmailId] ASC),
    CONSTRAINT [FK_ContactEmail_Contact] FOREIGN KEY ([ContactId]) REFERENCES [Contacts].[Contact] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE
);










GO



GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueEmailAddress]
    ON [Contacts].[ContactEmail]([ContactId] ASC, [EmailAddress] ASC);


GO
CREATE NONCLUSTERED INDEX [ContactIdIndex]
    ON [Contacts].[ContactEmail]([ContactId] ASC);

