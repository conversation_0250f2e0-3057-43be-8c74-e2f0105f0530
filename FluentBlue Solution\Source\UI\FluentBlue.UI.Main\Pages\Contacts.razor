﻿@page "/contacts"
@layout MainLayout
@attribute [Authorize]

@using FluentBlue.Shared.Utilities
@using FluentBlue.UI.Main.Components
@using FluentBlue.Data.Model.DTOs;
@using FluentBlue.Data.Model.DBOs.Contacts;
@using FluentBlue.UI.Main.Helpers

@inject HttpClient httpClient
@inject IConfiguration configuration
@inject NavigationManager navManager
@inject ILogger<Contacts> logger
@inject ILogger<FluentBlue.WebApi.Client.ContactsWebApiClient> contactsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.GeneralWebApiClient> generalWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.ContactCategoriesWebApiClient> contactCategoriesWebApiClientLogger
@inject IDialogService dialogService
@inject IFormFactor formFactor

@* @if (formFactor.GetPlatform() == "Web")
{
    @rendermode @(new InteractiveWebAssemblyRenderMode(false));
} *@

<div class="flex flex-col max-h-full">
    <div id="pageTop" class="px-3 pt-3">
    @* <FluentLabel Typo="Typography.H1" Class="mb-2">@Resources.ContactsResource.Title</FluentLabel> *@
        <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
            <FluentButton IconStart="@(new Icons.Regular.Size20.Person())" Appearance="Appearance.Accent" OnClick="NewContactBtnOnClick"><span class="">@Resources.ContactsResource.NewContactBtn_Text</span></FluentButton>
            <FluentButton id="filtersBtn" IconStart="@(new Icons.Regular.Size20.Filter())" OnClick="@(() => filtersMenuOpened = !filtersMenuOpened)"><span class="xs:hidden sm:hidden">@Resources.ContactsResource.filtersBtn_Text</span></FluentButton>
            <FluentMenu UseMenuService="true" Anchor="filtersBtn" Anchored="true" @bind-Open="filtersMenuOpened">
                <FluentMenuItem Checked=@noContactCategoryMenuItemChecked OnClick="@(async () => await ContactCategoryFilterOnClick(new Guid("00000000-0000-0000-0000-000000000000")))">@Resources.ContactsResource.NoContactCategory</FluentMenuItem>
                <FluentDivider />
                @foreach (SelectableContactCategory selectableContactCategory in this.selectableContactCategories!)
                {
                    <FluentIcon Icon="Icons.Filled.Size20.Checkmark" Color=@Color.Custom CustomColor="@selectableContactCategory.Color" Slot="checkbox-indicator" />
                    <FluentMenuItem Checked="@(selectableContactCategory.IsSelected)" OnClick="@(async () => await ContactCategoryFilterOnClick(selectableContactCategory.ContactCategoryId))">@selectableContactCategory.Name</FluentMenuItem>
                }
            </FluentMenu>
            @* <FluentButton IconStart="@(new Icons.Regular.Size20.ArrowForwardDownPerson())" OnClick="ExportContactsBtnOnClick"><span class="xs:hidden sm:hidden">@Resources.ContactsResource.ExportContactsBtn_Text</span></FluentButton> *@
            <div class="xs:w-14 sm:w-14 md:w-48 lg:w-48 xl:w-48 hover:w-48" slot="end">
                <FluentSearch slot="end" @ref="searchTxtBox" Maxlength="30" @onfocus="@(()=>{this.searchTxtBox.Class="w-64";})" @onkeyup="Search_OnKeyUp" Placeholder="@Resources.ContactsResource.SearchPlaceholder" ValueChanged="Search_ValueChanged" AutoComplete="off"></FluentSearch>
            </div>
        </FluentToolbar>
    </div>

    <div class="flex-1 overflow-x-hidden overflow-y-auto">
        @if (ScreenSizeTracker.CurrentBreakpoint == "md" || ScreenSizeTracker.CurrentBreakpoint == "lg" || ScreenSizeTracker.CurrentBreakpoint == "xl")
        {
            <FluentDataGrid Id="contactsDataGrid" @ref="contactsDataGrid" Items="@this.contacts" AutoFocus="false" ResizableColumns=true ItemSize="30" TGridItem="Data.Model.DTOs.ContactView" OnRowClick="ContactsDataGridOnRowClick" OnCellClick="ContactsDataGridOnCellClick" OnRowDoubleClick="ContactsDataGridOnRowDoubleClick">
                <TemplateColumn Align="@Align.Center" Class="h-10" Width="60px">
                    <FluentPersona ImageSize="30px" Initials="@context.Initials" Class="mx-auto" Style=@("background-color: "+FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(context.Initials))></FluentPersona>
                </TemplateColumn>
                @if (this.userSetting?.FullNameDisplay == null || this.userSetting.FullNameDisplay == Data.Model.FullNameDisplay.FirstLast)
                {
                    <PropertyColumn Property="@(x => x.FirstLastName)" Sortable="true" IsDefaultSortColumn="true" InitialSortDirection="SortDirection.Ascending" Tooltip="true" Width="2fr" Class="h-10 truncate" Style="vertical-align:middle" />
                }
                else if (this.userSetting.FullNameDisplay == Data.Model.FullNameDisplay.LastFirst)
                {
                    <PropertyColumn Property="@(x => x.LastFirstName)" Sortable="true" IsDefaultSortColumn="true" InitialSortDirection="SortDirection.Ascending" Tooltip="true" Width="2fr" Class="h-10 truncate" Style="vertical-align:middle" />
                }
                <PropertyColumn Property="@(x => x.MiddleName)" Sortable="true" Tooltip="true" Class="h-10" Width="1fr" />
                <TemplateColumn Title="@Resources.ContactsResource.ContactCategory" Align="@Align.Start" Class="h-10" Sortable="false" Width="125px">
                    @foreach (ContactCategoryView contactCategory in context.ContactCategoryViews)
                    {
                        <FluentIcon id=@("contactCategoryMapping"+contactCategory.ContactCategoryMappingId.ToString()) Icon="Icons.Filled.Size20.Square" Color=@Color.Custom CustomColor="@contactCategory.Color" Title="@contactCategory.Name" />
                        <FluentTooltip Anchor=@("contactCategoryMapping"+contactCategory.ContactCategoryMappingId.ToString())>@contactCategory.Name</FluentTooltip>
                    }
                </TemplateColumn>
                <PropertyColumn Property="@(x => x.Occupation)" Sortable="true" Tooltip="true" Class="h-10" Width="1fr" />
                <TemplateColumn Align="@Align.Center" Class="p-0 h-10" Width="90px">
                    <div class="w-fit">
                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnEditContact(context.ContactId))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteContact(context.ContactId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton>
                    </div>
                </TemplateColumn>
            </FluentDataGrid>
            @*  <FluentMenu @ref="contactsDataGridMenu" Anchor="contactsDataGrid" UseMenuService="true" Trigger="MouseButton.Right" Anchored="false" VerticalThreshold="170">
                <FluentMenuItem OnClick="EditContactMenuItemOnClick">
                    <span slot="start">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Edit())" Color="Color.Neutral" Slot="start" />
                    </span>
                    Edit
                </FluentMenuItem>
                <FluentMenuItem OnClick="DeleteContactMenuItemOnClick">
                    <span slot="start">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Color="Color.Neutral" Slot="start" />
                    </span>
                    Delete
                </FluentMenuItem>
            </FluentMenu> *@
        }
        @if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
        {
            <FluentDataGrid Id="contactsDataGrid" Items="@this.contacts" AutoFocus="false" GenerateHeader="GenerateHeaderOption.None" ResizableColumns=false ItemSize="30" TGridItem="Data.Model.DTOs.ContactView" OnRowClick="ContactsDataGridOnRowClick">
                <TemplateColumn Align="@Align.Center" Class="p-1 h-14" Width="50px">
                    <FluentPersona ImageSize="30px" Initials="@context.Initials" Class="mx-auto" Style=@("background-color: " + FluentBlue.Shared.Utilities.Colors.GetColorFromInitials(context.Initials))></FluentPersona>
                </TemplateColumn>
                <TemplateColumn Class="h-14" Width="1fr">
                    @if (this.userSetting == null || this.userSetting!.FullNameDisplay == Data.Model.FullNameDisplay.FirstLast)
                    {
                        <FluentLabel Typo="Typography.H6">@context.FirstLastName</FluentLabel>
                    }
                    else if (this.userSetting.FullNameDisplay == Data.Model.FullNameDisplay.LastFirst)
                    {
                        <FluentLabel Typo="Typography.H6">@context.LastFirstName</FluentLabel>
                    }
                    <FluentLabel>@(string.IsNullOrEmpty(context.MiddleName) ? " " : context.MiddleName)</FluentLabel>
                </TemplateColumn>
                <TemplateColumn Align="@Align.Center" Class="p-0 h-14" Width="50px">
                    <div class="w-fit mx-auto">
                        <FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnEditContact(context.ContactId))" IconStart="@(new Icons.Regular.Size16.Edit())"></FluentButton>
                        @*<FluentButton Appearance="Appearance.Stealth" @onclick="@(() => OnDeleteContact(context.ContactId))" IconStart="@(new Icons.Regular.Size16.Delete())"></FluentButton> *@
                    </div>
                </TemplateColumn>
            </FluentDataGrid>
        }
        <div id="pageBottom" class="px-3 pb-3">
            <FluentPaginator State="@pagination" CurrentPageIndexChanged="@Pagination_CurrentPageIndexChanged" Class="mx-4" />
        </div>
    </div>
</div>

<FluentOverlay @bind-Visible=@loading FullScreen="false" Transparent="true" Dismissable="false" Opacity="0" Alignment="Align.Center" Justification="JustifyContent.Center">
    <FluentProgressRing />
</FluentOverlay>
