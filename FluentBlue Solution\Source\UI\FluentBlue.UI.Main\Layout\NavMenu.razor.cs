﻿using Microsoft.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace FluentBlue.UI.Main.Layout
{
    public partial class NavMenu
    {
        [Parameter]
        public bool AlwaysExpanded { get; set; } = false;

        private bool menuExpanded = true;

        [Parameter(CaptureUnmatchedValues = true)]
        public Dictionary<string, object> CustomAttributes { get; set; } = new Dictionary<string, object>();

        private void ToggleMenuExpanded()
        {
            menuExpanded = !menuExpanded;
        }

        private void OnBreakpointChanged(string breakpoint)
        {
            if (breakpoint == "sm" || breakpoint == "xs")
            {
                if (AlwaysExpanded == false)
                {
                    this.menuExpanded = false;
                }
            }
            else
            {
                this.menuExpanded = true;
            }
        }

        private void OnClose(EventArgs args)
        {
            int i = 0;
        }

    }
}
