﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using FluentValidation;
using FluentValidation.Validators;


namespace FluentBlue.Data.Model.Extensions
{
   
    public static class FluentValidationExtensions
    {
       public static IRuleBuilderOptions<T, TProperty> WithLocalizedMessage<T, TProperty>(
       this IRuleBuilderOptions<T, TProperty> ruleBuilder,
       string errorMessage,
       string propertyName)
        {
            return ruleBuilder
                .WithMessage(errorMessage)
                .WithName(model =>
                {
                    // Get the property info for the given property name
                    var propertyInfo = typeof(T).GetProperty(propertyName);

                    // Retrieve the Display attribute, if present, to use as the display name
                    var displayAttribute = propertyInfo?.GetCustomAttributes(typeof(DisplayAttribute), true)
                                                        .Cast<DisplayAttribute>()
                                                        .FirstOrDefault();

                    // Return the localized name or fall back to the property name
                    return displayAttribute?.GetName() ?? propertyName;
                });
        }
    }
}
