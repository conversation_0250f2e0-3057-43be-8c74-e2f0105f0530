﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;

namespace FluentBlue.Application.Business
{
    public interface IEventCategoriesBusiness
    {
        Task<List<FluentBlue.Data.Model.DBOs.Calendar.EventCategory>> GetEventCategories(Guid tenantId);
        Task<Data.Model.DBOs.Calendar.EventCategory?> GetEventCategory(Guid eventCategoryId);
        Task CreateOrUpdateEventCategory(EventCategory eventObj);
        Task DeleteEventCategory(Guid eventCategoryId);
    }
}