﻿using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.Extensions.Options;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DBOs.Calendar
{
    [Table("EventState", Schema = "Calendar")]
    public class EventState : IObjectState, INotifyPropertyChanged
    {
        private Guid eventStateId;
        private Guid? tenantId;
        private string name = string.Empty;
        private string backColor = string.Empty;
        private int? sortIndex;

        public EventState()
        {
            eventStateId = Guid.CreateVersion7();
            name = string.Empty;
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            RowVersion = new byte[0];
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = "EventStateId")]
        public Guid EventStateId
        {
            get
            {
                return eventStateId;
            }
            set
            {
                eventStateId = value;
            }
        }

        [Required]
        public Guid? TenantId
        {
            get
            {
                return tenantId;
            }
            set
            {
                tenantId = value;
            }
        }

        [ForeignKey("TenantId")]
        public Tenant? Tenant { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventStateResource), Name = nameof(Model.Resources.Calendar.EventStateResource.Name))]
        [MaxLength(50, ErrorMessageResourceType = typeof(Model.Resources.Calendar.EventResource), ErrorMessageResourceName = nameof(Model.Resources.Calendar.EventStateResource.NameMaxLengthError))]
        public string Name
        {
            get
            {
                return name;
            }
            set
            {
                name = value;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventStateResource), Name = nameof(Model.Resources.Calendar.EventStateResource.BackColor))]
        [MaxLength(10, ErrorMessageResourceType = typeof(Model.Resources.Calendar.EventResource), ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.MaximumAllowedLength10))]
        public string BackColor
        {
            get
            {
                return backColor;
            }
            set
            {
                backColor = value;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.SortIndex))]
        public int? SortIndex
        {
            get
            {
                return sortIndex;
            }
            set
            {
                sortIndex = value; 
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        [Column(TypeName = "datetime")]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        [Column(TypeName = "datetime")]
        public DateTime DateModifiedUtc { get; set; } = DateTime.UtcNow;

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
                NotifyPropertyChanged();
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }


        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            //DateModifiedUtc = DateTime.UtcNow;

            //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
            //if (ObjectState == ObjectState.Unchanged)
            //{
            //    ObjectState = ObjectState.Modified;
            //}

            if (PropertyChanged != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }

        }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        public static EventState Empty
        {
            get
            {
                return new EventState() { Name = "", EventStateId = Guid.Empty };
            }
        }
    }
}
