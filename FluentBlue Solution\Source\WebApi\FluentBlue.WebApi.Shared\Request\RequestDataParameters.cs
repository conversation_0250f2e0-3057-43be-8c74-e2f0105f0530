﻿using FluentBlue.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Shared.Request
{
    public class RequestDataParameters
    {
        public Guid TenantId { get; set; }  //TODO: πρέπει να φύγει το TenantId να πάει στο jwt
        public string? Filter { get; set; } = string.Empty;
        public UInt16 PageIndex { get; set; } = 1;  //Valid values >= 1
        public UInt16 PageSize { get; set; } = 10;
        public Dictionary<string, SortOrder> SortColumns { get; set; } = new Dictionary<string, SortOrder>();
    }
}
