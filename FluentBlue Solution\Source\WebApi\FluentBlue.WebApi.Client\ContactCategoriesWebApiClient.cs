﻿using Azure;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Client
{
    public class ContactCategoriesWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<ContactCategoriesWebApiClient> logger;

        public ContactCategoriesWebApiClient(HttpClient httpClient, ILogger<ContactCategoriesWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory>> GetAllContactCategories(Guid tenantId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/ContactCategories/GetAll";

                //Εκτελούμε το GET request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DBOs.Contacts.ContactCategory>>? response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DBOs.Contacts.ContactCategory>>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        this.logger.LogError(response.ExceptionMessage, "Error in GetContactCategories({@tenantId},{fiter})", tenantId);
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                throw new Exception("Not identified ResultCode: " + response.ResultCode.ToString());
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAllContactCategories({@tenantId})", tenantId);
                throw;
            }
        }


        public async Task<ContactCategory?> GetContactCategory(Guid contactCategoryId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/ContactCategory/Get?ContactCategoryId=" + contactCategoryId.ToString();

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<ContactCategory> response = JsonConvert.DeserializeObject<ApiResponse<ContactCategory>>(responseString)!;
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetContactCategory({@contactCategoryId})", contactCategoryId);
                throw;
            }
        }


        public async Task<ContactCategory?> CreateOrUpdateContactCategory(ContactCategory contactCategoryObj)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/ContactCategory/CreateOrUpdate";

                string contactJson = System.Text.Json.JsonSerializer.Serialize(contactCategoryObj);  //Μετατρέπουμε το ContactCategory object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(contactJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<ContactCategory> response = JsonConvert.DeserializeObject<ApiResponse<ContactCategory>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateContactCategory({@contactCategoryObj})", contactCategoryObj);
                throw;
            }
        }

        public async Task DeleteContactCategory(Guid contactCategoryId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/ContactCategory/Delete?ContactCategoryId=" + contactCategoryId;

                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString)!;
                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in DeleteContactCategory({@contactCategoryId})", contactCategoryId);
                throw;
            }
        }
    }
}
