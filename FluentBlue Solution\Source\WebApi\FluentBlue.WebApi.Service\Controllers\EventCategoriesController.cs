﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Shared;
using FluentBlue.WebApi.Service.Resources;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class EventCategoriesController : ControllerBase
    {
        private IEventCategoriesBusiness eventCategoriesBusiness;
        private ILogger<EventCategoriesController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public EventCategoriesController(IEventCategoriesBusiness eventCategoriesBusiness, ILogger<EventCategoriesController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            try
            {
                this.eventCategoriesBusiness = eventCategoriesBusiness;
                this.logger = logger;
                this.mapper = mapper;
                this.webApiCallerInfo = webApiCallerInfo;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        /// <summary>
        /// Retrieves all EventCategories.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("EventCategories/GetAll")]
        public async Task<ApiResponse<List<Data.Model.DBOs.Calendar.EventCategory>>> GetAllEventCategories()
        {
            try
            {
                //Validation

                //Query
                List<FluentBlue.Data.Model.DBOs.Calendar.EventCategory> data = await this.eventCategoriesBusiness.GetEventCategories(this.webApiCallerInfo.TenantId!.Value);

                //Response
                return new ApiResponse<List<Data.Model.DBOs.Calendar.EventCategory>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<Data.Model.DBOs.Calendar.EventCategory>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(ex);
                return new ApiResponse<List<Data.Model.DBOs.Calendar.EventCategory>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }


        [HttpPost]
        [Route("EventCategory/CreateOrUpdate")]
        public async Task<ApiResponse<EventCategory>> CreateOrUpdateEvent([FromBody] EventCategory eventObj)
        {
            try
            {
                //Validation
                if (eventObj == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Query
                await this.eventCategoriesBusiness.CreateOrUpdateEventCategory(eventObj);

                //Response
                EventCategory? eventCategory = await this.eventCategoriesBusiness.GetEventCategory(eventObj.EventCategoryId);
                return new ApiResponse<EventCategory>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = eventCategory };
            }
            catch (ApplicationException ex)
            {
                this.logger.LogError(ex, ex.Message, eventObj);  //TODO: Κανονικά τα ApplicationExceptions δεν πρέπει να γίνονται log γιατί είναι αναμενόμενα από το πρόγραμμα για το χρήστη.
                return new ApiResponse<EventCategory>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, eventObj);
                return new ApiResponse<EventCategory>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("EventCategory/Delete")]
        public async Task<ApiResponse> DeleteEventCategory([FromQuery] Guid eventCategoryId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                await this.eventCategoriesBusiness.DeleteEventCategory(eventCategoryId);

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<EventCategory>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "EventCategoryId=" + eventCategoryId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}