﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.SignalR
{
    public class NotificationData
    {
        public string NotificationType { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Subtitle { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        /// <summary>
        /// The ID of the sender of notification. This can be an application or a user.
        /// </summary>
        public string SenderId { get; set; } = string.Empty;
    }
}
