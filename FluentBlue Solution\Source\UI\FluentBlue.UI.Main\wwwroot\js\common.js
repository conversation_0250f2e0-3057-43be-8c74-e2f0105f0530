﻿document.addEventListener('DOMContentLoaded', function () {
    // Detect if it's a mobile device
    //const isMobile = window.innerWidth <= 640 ||
    //    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    //if (isMobile) {

    setupMutationObserver();
   
    //}
});

// Function to set up the MutationObserver to execute when DOM is changed.
function setupMutationObserver() {
    // Create a new observer
    const observer = new MutationObserver(function (mutations) {
        //Apply style for devices
        window.applyStyleForDevices();
    });

    // Start observing with these configuration parameters
    observer.observe(document.body, {
        childList: true,    // Watch for added/removed nodes
        subtree: true       // Watch the entire subtree
    });
}


window.defaultCultureInfo = {
    language: navigator.language,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
};



/* ScreenSize code =================================================== */
var width;
window.screenSizeHelper = {
    dotNetRef: null,

    registerResizeCallback: function (dotNetObject) {
        if (window.screenSizeHelper.dotNetRef) {
            window.screenSizeHelper.unregisterResizeCallback();
        }

        window.screenSizeHelper.dotNetRef = dotNetObject;

        // Add resize event listener
        window.addEventListener("resize", window.screenSizeHelper.onResize);

        // Trigger the initial screen size
        window.screenSizeHelper.onResize();
    },

    onResize: function () {
        width = window.innerWidth;
        if (window.screenSizeHelper.dotNetRef) {
            window.screenSizeHelper.dotNetRef.invokeMethodAsync("UpdateScreenWidth", width);
        }
    },

    unregisterResizeCallback: function () {
        if (window.screenSizeHelper.dotNetRef) {
            window.removeEventListener("resize", window.screenSizeHelper.onResize);
            window.screenSizeHelper.dotNetRef = null;
        }
    }
};


/* Customize FluentUI style =================================================== */

window.applyStyleForDevices = () => {
    if (width <= 640) {
        window.applyFluentDialogStyleForDevices();
        window.applyFluentTabsStyleForDevices();
        window.applyButtonStyleForDevices();
    }
}

window.applyFluentDialogStyleForDevices = () => {
    /*Reduces the margin around the screen (used for small devices) */
    let dialogs = document.getElementsByTagName("fluent-dialog");

    for (let i = 0; i < dialogs.length; i++) {
        let positioningRegion = dialogs[i].shadowRoot?.querySelector(".positioning-region");
        if (positioningRegion) {
            positioningRegion.style.margin = "-12px";
        }
    }
};

window.applyFluentTabsStyleForDevices = () => {
    /*Reduces the margin around the screen (used for small devices) */
    let tabs = document.getElementsByTagName("fluent-tabs");

    for (let i = 0; i < tabs.length; i++) {
        tabs[i].style.padding = "12px 0px 12px 0px";
    }
};

window.applyButtonStyleForDevices = () => {
    /*Reduces the margin around the start icon (used for small devices) */
    let buttons = document.querySelectorAll("button.control span");

    for (let i = 0; i < buttons.length; i++) {
        buttons[i].style.margin = "0px 0px 0px 0px !important";
    }
};




/* WebUpdateChecker ===================================================== */
/* This script is responsible for checking if the web application is up to date. It is supposed to be used only from FluentUI.Web project.
   It listens for visibility changes and clears the browser cache on load. It also provides a method to force reload the page from the server. */
window.webUpdateChecker = {
    dotNetRef: null,

    initialize: function (dotNetReference) {
        this.dotNetRef = dotNetReference;

        // Listen for visibility changes
        document.addEventListener('visibilitychange', () => {
            this.dotNetRef.invokeMethodAsync('NotifyVisibilityChange',
                document.visibilityState === 'visible');
        });

        // Clear browser cache on load to ensure fresh resources
        this.clearCache();
    },

    forceReload: function () {
        // Use true to force reload from server, not from cache
        window.location.reload(true);
    },

    clearCache: async function () {
        if ('caches' in window) {
            try {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)));
                console.log('Browser caches cleared');
                return true;
            } catch (error) {
                console.error('Failed to clear cache:', error);
                return false;
            }
        }
        return false;
    }
};