﻿using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main
{
    public class Keywords
    {
        public static String SummaryData => "SummaryData";  // Used for Dashboard data, general data about the whole app.
        public static String UserSetting => "UserSetting";
        public static string UserRole => "UserRole";  //Used for storing in LocalStorage the user's role.
        public static String InitialUISetting => "InitialUISetting";  //Used for storing in HybridCache/LocalStorage the initial UI settings before user logs in.
        public static String ThemeUpdated => "ThemeUpdated";  //Means that theme (dark/light) is changed.
        public static String SettingsUpdated => "SettingsUpdated";  //Means that settings should are changed.
        public static String ReloadEvents => "ReloadEvents";  //Means that Events should be reloaded.
        public static String ReloadContacts => "ReloadContacts";  //Means that Contacts should be reloaded.
        public static String CalendarLastView => "CalendarLastView";
        public static String EventCategories => "EventCategories";
        public static String EventStates => "EventStates";
        public static String LastSelectedCalendarDisplayUsers => "LastSelectedCalendarDisplayUsers";
        public static String Users => "Users";
        public static String BreakpointChanged => "BreakpointChanged";
        public static String ContactCategories => "ContactCategories";
        public static String Language => "Language";
        public static String TimeZones => "TimeZones";  //Used for storing the timezones in LocalStorage.
        public static String DeploymentGuid => "DeploymentGuid";  //Used for storing the deployment guid.
    }
}
