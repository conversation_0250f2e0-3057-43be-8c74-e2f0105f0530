﻿using FluentBlue.WebApi.Shared;
using FluentBlue.Shared;
using System.Text;
using Newtonsoft.Json;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using static System.Runtime.InteropServices.JavaScript.JSType;
using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.Extensions.Logging;


namespace FluentBlue.WebApi.Client
{
    public class TenantsWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<TenantsWebApiClient> logger;

        public TenantsWebApiClient(HttpClient httpClient, ILogger<TenantsWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        //public async Task<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Tenant>>> GetTenants(string filter, UInt16 pageIndex, UInt16 pageSize)
        //{
        //    try
        //    {
        //        string requestUri = httpClient.BaseAddress + apiVersion + "/" + "tenants";

        //        RequestDataParameters requestDataParams = new RequestDataParameters();
        //        //queryDataParams.TenantId = tenantId;
        //        requestDataParams.Filter = filter.Trim();
        //        requestDataParams.PageIndex = pageIndex;
        //        requestDataParams.PageSize = pageSize;
        //        requestDataParams.SortColumns = new Dictionary<string, SortOrder>();
        //        //queryDataParams.SortColumns.Add("TenantId", SortOrder.Ascending);
        //        requestDataParams.SortColumns.Add("FullName", SortOrder.Ascending);
        //        string getEntitiesParamsJson = JsonConvert.SerializeObject(requestDataParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

        //        //Ετοιμάζουμε το HttpContext με τα json data.
        //        HttpContent httpContext = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

        //        string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
        //        requestUri = requestUri + "?" + urlParameters;

        //        //Εκτελούμε το POST request.
        //        HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
        //                                                                                        //HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
        //        string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
        //        ApiResponse<PagedData<List<Data.Model.DBOs.Tenants.Tenant>>> response = JsonConvert.DeserializeObject<ApiResponse<PagedData<List<Data.Model.DBOs.Tenants.Tenant>>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

        //        if (response.ResultCode == ApiResponseResultCode.Ok)
        //        {
        //            return response.ResponseContent!;
        //        }
        //        else if (response.ResultCode == ApiResponseResultCode.Exception)
        //        {
        //            throw new ApplicationException(response.ExceptionMessage ?? "");
        //        }
        //        return new PagedData<List<Data.Model.DBOs.Tenants.Tenant>> { Data = null, DataTotalCount = 0 };
        //    }
        //    catch (Exception ex)
        //    {
        //        this.logger.LogError(ex, "Error in GetTenants({fiter},{@pageIndex}, {@pageSize})", filter, pageIndex, pageSize);
        //        throw;
        //    }
        //}
        
    }
}
