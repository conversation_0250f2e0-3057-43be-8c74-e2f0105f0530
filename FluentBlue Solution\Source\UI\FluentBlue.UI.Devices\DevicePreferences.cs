﻿using FluentBlue.UI.Main;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{
    //Η κλάση αυτή φτιάχτηκε για να κάνει wrapping τα Preferences του MAUI, αλλά να γίνεται instanciate μόνο στην εφαρμογή του MAUI γιατί για το web δεν υπάρχουν τα Preferences.
    public class DevicePreferences : IDevicePreferences
    {
        public string Get(string keyword, string defaultValue)
        {
            return Preferences.Get(keyword, defaultValue);
        }
        public void Set(string keyword, string value)
        {
            Preferences.Set(keyword, value);
        }
    }
}
