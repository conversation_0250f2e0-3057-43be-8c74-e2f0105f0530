﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources.Calendar {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EventResource_el1 {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EventResource_el1() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.Calendar.EventResource.el1", typeof(EventResource_el1).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ολοήμερο.
        /// </summary>
        public static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εμποδίζει άλλα συμβάντα.
        /// </summary>
        public static string Block {
            get {
                return ResourceManager.GetString("Block", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δημιουργήθηκε.
        /// </summary>
        public static string DateCreated {
            get {
                return ResourceManager.GetString("DateCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τροποποιήθηκε.
        /// </summary>
        public static string DateModified {
            get {
                return ResourceManager.GetString("DateModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Περιγραφή.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πολλοί χαρακτήρες (όριο 2000 χαρακτήρων).
        /// </summary>
        public static string DescriptionMaxLengthError {
            get {
                return ResourceManager.GetString("DescriptionMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Λήξη.
        /// </summary>
        public static string EndTime {
            get {
                return ResourceManager.GetString("EndTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η λήξη πρέπει να είναι μετά την έναρξη..
        /// </summary>
        public static string EndTimeMustBeAfterStartTime {
            get {
                return ResourceManager.GetString("EndTimeMustBeAfterStartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ζώνη Ώρας Λήξης.
        /// </summary>
        public static string EndTimeZone {
            get {
                return ResourceManager.GetString("EndTimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κατηγορία Συμβάντος.
        /// </summary>
        public static string EventCategoryId {
            get {
                return ResourceManager.GetString("EventCategoryId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID Συμβάντος.
        /// </summary>
        public static string EventId {
            get {
                return ResourceManager.GetString("EventId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κατάσταση Συμβάντος.
        /// </summary>
        public static string EventStateId {
            get {
                return ResourceManager.GetString("EventStateId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τοποθεσία.
        /// </summary>
        public static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Περιγραφή.
        /// </summary>
        public static string PlainDescription {
            get {
                return ResourceManager.GetString("PlainDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μόνο για Ανάγνωση.
        /// </summary>
        public static string ReadOnly {
            get {
                return ResourceManager.GetString("ReadOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Έναρξη.
        /// </summary>
        public static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ζώνη Ώρας Έναρξης.
        /// </summary>
        public static string StartTimeZone {
            get {
                return ResourceManager.GetString("StartTimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Θέμα.
        /// </summary>
        public static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πολλοί χαρακτήρες (όριο 300 χαρακτήρων).
        /// </summary>
        public static string SubjectMaxLengthError {
            get {
                return ResourceManager.GetString("SubjectMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Περίληψη.
        /// </summary>
        public static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
    }
}
