﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using FluentBlue.WebApi.Shared.Request;

namespace FluentBlue.UI.Main.Components
{
    public enum RecurrentEventHandlingType
    {
        Update,
        Delete
    }

    partial class RecurrentEventHandlingDialog
    {
        protected string selectedUpdateAction = string.Empty;
        private FluentDialogHeader dialogHeader = new FluentDialogHeader();
        [CascadingParameter] public FluentDialog CurrentDialog { get; set; } = default!;
        
        [Parameter] public RecurrentEventHandlingType? Content { get; set; } = null;
        private string title = string.Empty;
        private string message = string.Empty;

        protected override void OnAfterRender(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    if (Content == RecurrentEventHandlingType.Delete)
                    {
                        this.title =Resources.RecurrentEventHandlingDialogResource.DeleteRucurrentEventTitle;
                        this.message = Resources.RecurrentEventHandlingDialogResource.SelectRecurrentEventDeleteType;
                    }
                    else
                    {
                        this.title = Resources.RecurrentEventHandlingDialogResource.UpdateRucurrentEventTitle;
                        this.message = Resources.RecurrentEventHandlingDialogResource.SelectRecurrentEventUpdateType;
                    }

                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CancelBtnOnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task CurrentEventBtnOnClick()
        {
            try
            {
                WebApi.Shared.Request.RecurrentEventHandlingType updateType = Enum.Parse<WebApi.Shared.Request.RecurrentEventHandlingType>(WebApi.Shared.Request.RecurrentEventHandlingType.Current.ToString());
                await this.CurrentDialog.CloseAsync(updateType);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CurrentAndFollowigEventsBtnOnClick()
        {
            try
            {
                WebApi.Shared.Request.RecurrentEventHandlingType updateType = Enum.Parse<WebApi.Shared.Request.RecurrentEventHandlingType>(WebApi.Shared.Request.RecurrentEventHandlingType.CurrentAndFollowing.ToString());
                await this.CurrentDialog.CloseAsync(updateType);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task AllEventsBtnOnClick()
        {
            try
            {
                WebApi.Shared.Request.RecurrentEventHandlingType updateType = Enum.Parse<WebApi.Shared.Request.RecurrentEventHandlingType>(WebApi.Shared.Request.RecurrentEventHandlingType.All.ToString());
                await this.CurrentDialog.CloseAsync(updateType);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                if (this.selectedUpdateAction == string.Empty)
                {
                    dialogService.ShowInfo(Main.GlobalResource.SelectValue);
                }
                else
                {
                    WebApi.Shared.Request.RecurrentEventHandlingType updateType = Enum.Parse<WebApi.Shared.Request.RecurrentEventHandlingType>(this.selectedUpdateAction);
                    await this.CurrentDialog.CloseAsync(updateType);
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

    }
}
