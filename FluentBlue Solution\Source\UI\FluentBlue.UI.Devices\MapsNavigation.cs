﻿using FluentBlue.Shared.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{
    class MapsNavigation : IMapsNavigation
    {
        public async Task NavigateToAddress(string address)
        {
            try
            {
                var encodedAddress = Uri.EscapeDataString(address);
                Uri uri;

                if (DeviceInfo.Platform == DevicePlatform.iOS)
                {
                    // iOS: Uses maps:// protocol or Apple Maps
                    uri = new Uri($"maps://?daddr={encodedAddress}&dirflg=d");

                    // Alternative: force Google Maps if installed
                    // uri = new Uri($"comgooglemaps://?daddr={encodedAddress}&directionsmode=driving");
                }
                else // Android
                {
                    // Android: Uses google.navigation protocol
                    uri = new Uri($"google.navigation:q={encodedAddress}");
                }

                // Check if can be handled
                if (await Launcher.CanOpenAsync(uri))
                {
                    await Launcher.OpenAsync(uri);
                }
                else
                {
                    // Fallback to browser with Google Maps
                    uri = new Uri($"https://www.google.com/maps/dir/?api=1&destination={encodedAddress}");
                    await Launcher.OpenAsync(uri);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
