﻿CREATE TABLE [Tenants].[Branch] (
    [BranchId]        UNIQUEIDENTIFIER CONSTRAINT [DF_Branch_BranchId1] DEFAULT (newsequentialid()) NOT NULL,
    [CompanyId]       UNIQUEIDENTIFIER NOT NULL,
    [Name]            NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Branch_Name] DEFAULT (N'') NOT NULL,
    [AddressLine1]    NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Branch_AddressLine1] DEFAULT (N'') NOT NULL,
    [City]            NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Branch_City] DEFAULT (N'') NOT NULL,
    [StateProvinceId] INT              NOT NULL,
    [PostalCode]      NVARCHAR (10)    COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Branch_PostalCode] DEFAULT (N'') NOT NULL,
    [DateModifiedUtc] DATETIME2 (7)    CONSTRAINT [DF_Branch_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_Branch] PRIMARY KEY CLUSTERED ([BranchId] ASC),
    CONSTRAINT [FK_Branch_Company] FOREIGN KEY ([CompanyId]) REFERENCES [Tenants].[Company] ([CompanyId]) ON DELETE CASCADE ON UPDATE CASCADE
);








GO
