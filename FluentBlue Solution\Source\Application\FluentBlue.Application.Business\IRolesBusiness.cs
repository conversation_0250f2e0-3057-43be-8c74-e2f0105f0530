﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;

namespace FluentBlue.Application.Business
{
    public interface IRolesBusiness
    {
        Task<Role?> GetRole(Guid roleId);
        Task<PagedData<List<Role>>> GetRoles(ReadPagedDataParameters parameters);
        Task<List<RoleLI>> GetRolesLI(Guid tenantId);
        Task CreateOrUpdateRole(Role role);
        Task DeleteRole(Guid roleId);
        Task<Role?> CheckRoleExists(Guid tenantId, Guid roleId, string name);
        Task<Role?> GetRoleOfUser(Guid tenantId, Guid userId);
    }
}