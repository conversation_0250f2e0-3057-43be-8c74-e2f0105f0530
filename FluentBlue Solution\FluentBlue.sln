﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Source", "Source", "{C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{26A6F362-FE49-4D69-B30A-4E79083F833A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UI", "UI", "{82CFC85D-8173-4718-9714-8AF885DC34BD}"
	ProjectSection(SolutionItems) = preProject
		Notes.txt = Notes.txt
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data", "Data", "{B04324A3-9678-4A2C-B460-BF453D3FE01B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebApi", "WebApi", "{959D0C93-2CA3-424E-B983-34F7B0D965A5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{69E38B84-6BCA-44FB-91D0-D0DD94D7E9EC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Application", "Application", "{256633AC-994C-4B88-9712-C31711AC5F31}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.UI.Main", "Source\UI\FluentBlue.UI.Main\FluentBlue.UI.Main.csproj", "{B4287B78-84BA-4451-8B66-95FE8307627E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.UI.Web", "Source\UI\FluentBlue.UI.Web\FluentBlue.UI.Web.csproj", "{1A4FD0BD-003A-4732-9FEA-8D37EA9E7AE0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.UI.Devices", "Source\UI\FluentBlue.UI.Devices\FluentBlue.UI.Devices.csproj", "{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.Data.Model", "Source\Data\FluentBlue.Data.Model\FluentBlue.Data.Model.csproj", "{5A324583-2CDE-4CC0-8997-620D652D2FE6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.WebApi.Service", "Source\WebApi\FluentBlue.WebApi.Service\FluentBlue.WebApi.Service.csproj", "{18043B19-5181-4307-A6C6-1F6076677E22}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.Shared", "Source\Shared\FluentBlue.Shared\FluentBlue.Shared.csproj", "{1E200BB8-5D66-478B-A4C2-A272BF863710}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.Application.Business", "Source\Application\FluentBlue.Application.Business\FluentBlue.Application.Business.csproj", "{CA1D819F-D2EC-4CD9-9EDA-2C5099139DB6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.WebApi.Shared", "Source\WebApi\FluentBlue.WebApi.Shared\FluentBlue.WebApi.Shared.csproj", "{F0C8F3C6-23F4-4758-A448-C17B04E09D3A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.WebApi.Client", "Source\WebApi\FluentBlue.WebApi.Client\FluentBlue.WebApi.Client.csproj", "{2FBE18F6-37E9-4E83-AAA4-BCC98FAEB144}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentBlue.Tests.WebApi.Service", "Tests\FluentBlue.Tests.WebApi.Service\FluentBlue.Tests.WebApi.Service.csproj", "{69D94E53-7B09-4339-A82D-88E70D1B3928}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "FluentBlue.Data.Database", "Source\Data\FluentBlue.Data.Database\FluentBlue.Data.Database.sqlproj", "{2B4C57DB-9399-4608-B763-9A605C0713C0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B4287B78-84BA-4451-8B66-95FE8307627E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4287B78-84BA-4451-8B66-95FE8307627E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4287B78-84BA-4451-8B66-95FE8307627E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4287B78-84BA-4451-8B66-95FE8307627E}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A4FD0BD-003A-4732-9FEA-8D37EA9E7AE0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A4FD0BD-003A-4732-9FEA-8D37EA9E7AE0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A4FD0BD-003A-4732-9FEA-8D37EA9E7AE0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A4FD0BD-003A-4732-9FEA-8D37EA9E7AE0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{5A324583-2CDE-4CC0-8997-620D652D2FE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A324583-2CDE-4CC0-8997-620D652D2FE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A324583-2CDE-4CC0-8997-620D652D2FE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A324583-2CDE-4CC0-8997-620D652D2FE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{18043B19-5181-4307-A6C6-1F6076677E22}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18043B19-5181-4307-A6C6-1F6076677E22}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18043B19-5181-4307-A6C6-1F6076677E22}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18043B19-5181-4307-A6C6-1F6076677E22}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E200BB8-5D66-478B-A4C2-A272BF863710}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E200BB8-5D66-478B-A4C2-A272BF863710}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E200BB8-5D66-478B-A4C2-A272BF863710}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E200BB8-5D66-478B-A4C2-A272BF863710}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA1D819F-D2EC-4CD9-9EDA-2C5099139DB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA1D819F-D2EC-4CD9-9EDA-2C5099139DB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA1D819F-D2EC-4CD9-9EDA-2C5099139DB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA1D819F-D2EC-4CD9-9EDA-2C5099139DB6}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0C8F3C6-23F4-4758-A448-C17B04E09D3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0C8F3C6-23F4-4758-A448-C17B04E09D3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0C8F3C6-23F4-4758-A448-C17B04E09D3A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0C8F3C6-23F4-4758-A448-C17B04E09D3A}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FBE18F6-37E9-4E83-AAA4-BCC98FAEB144}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FBE18F6-37E9-4E83-AAA4-BCC98FAEB144}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FBE18F6-37E9-4E83-AAA4-BCC98FAEB144}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FBE18F6-37E9-4E83-AAA4-BCC98FAEB144}.Release|Any CPU.Build.0 = Release|Any CPU
		{69D94E53-7B09-4339-A82D-88E70D1B3928}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{69D94E53-7B09-4339-A82D-88E70D1B3928}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{69D94E53-7B09-4339-A82D-88E70D1B3928}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{69D94E53-7B09-4339-A82D-88E70D1B3928}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B4C57DB-9399-4608-B763-9A605C0713C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B4C57DB-9399-4608-B763-9A605C0713C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B4C57DB-9399-4608-B763-9A605C0713C0}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{2B4C57DB-9399-4608-B763-9A605C0713C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B4C57DB-9399-4608-B763-9A605C0713C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B4C57DB-9399-4608-B763-9A605C0713C0}.Release|Any CPU.Deploy.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{82CFC85D-8173-4718-9714-8AF885DC34BD} = {C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}
		{B04324A3-9678-4A2C-B460-BF453D3FE01B} = {C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}
		{959D0C93-2CA3-424E-B983-34F7B0D965A5} = {C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}
		{69E38B84-6BCA-44FB-91D0-D0DD94D7E9EC} = {C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}
		{256633AC-994C-4B88-9712-C31711AC5F31} = {C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}
		{B4287B78-84BA-4451-8B66-95FE8307627E} = {82CFC85D-8173-4718-9714-8AF885DC34BD}
		{1A4FD0BD-003A-4732-9FEA-8D37EA9E7AE0} = {82CFC85D-8173-4718-9714-8AF885DC34BD}
		{2DCE6D2B-662A-40C8-9EA0-9A86B8B0B3A1} = {82CFC85D-8173-4718-9714-8AF885DC34BD}
		{5A324583-2CDE-4CC0-8997-620D652D2FE6} = {B04324A3-9678-4A2C-B460-BF453D3FE01B}
		{18043B19-5181-4307-A6C6-1F6076677E22} = {959D0C93-2CA3-424E-B983-34F7B0D965A5}
		{1E200BB8-5D66-478B-A4C2-A272BF863710} = {C00E5BD1-4A8C-4C07-B757-EA23D5F31E6E}
		{CA1D819F-D2EC-4CD9-9EDA-2C5099139DB6} = {256633AC-994C-4B88-9712-C31711AC5F31}
		{F0C8F3C6-23F4-4758-A448-C17B04E09D3A} = {959D0C93-2CA3-424E-B983-34F7B0D965A5}
		{2FBE18F6-37E9-4E83-AAA4-BCC98FAEB144} = {959D0C93-2CA3-424E-B983-34F7B0D965A5}
		{69D94E53-7B09-4339-A82D-88E70D1B3928} = {26A6F362-FE49-4D69-B30A-4E79083F833A}
		{2B4C57DB-9399-4608-B763-9A605C0713C0} = {B04324A3-9678-4A2C-B460-BF453D3FE01B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A99D66CE-B480-4652-B7E3-3AB820ED6DF4}
	EndGlobalSection
EndGlobal
