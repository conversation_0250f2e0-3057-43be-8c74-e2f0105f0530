﻿@implements IDialogContentComponent<UserDialogInput>

@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject ILogger<FluentBlue.UI.Main.Components.UserDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.RolesWebApiClient> rolesWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS
@inject HybridCache cache

@using Blazored.FluentValidation
@using FluentBlue.Data.Model.DBOs.Tenants
@using FluentBlue.Data.Model.DTOs
@using FluentBlue.Shared.Utilities
@using FluentBlue.UI.Main.Auth
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@using Microsoft.Extensions.Caching.Hybrid
<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@(this.Content!.AccountMode ? Resources.UserDialogResource.AccountTitle : Resources.UserDialogResource.Title)</FluentLabel>
    <EditForm id="editForm" Model="@Content.User">
        <ChildContent Context="context2">
            @* <FluentValidationSummary></FluentValidationSummary> *@
            <FluentValidationValidator @ref="fluentValidationValidator" />

            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.UserDialogResource.SaveBtn_Text</span></FluentButton>
                @if (this.Content!.AccountMode == false && this.Content!.User.UserId != AuthenticatedUserData.UserId)  //Η φόρμα να μην τρέχει σε AccountMode και να μη μπορεί ο χρήστης να διαγράψει τον εαυτό του.
                {
                    <FluentButton OnClick="DeleteBtn_OnClick"><FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Color="Color.Error" Slot="start" /><span class="xs:hidden sm:hidden">@Resources.UserDialogResource.DeleteBtn_Text</span></FluentButton>   @* Color="var(--error)" *@
                }
                <FluentButton IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.UserDialogResource.CancelBtn_Text</span></FluentButton>
            </FluentToolbar>
            <FluentGrid Spacing="1">
                <FluentGridItem xs="12" md="6">
                    <FluentTextField @bind-Value="@Content!.User.FirstName" Label="@Resources.UserDialogResource.FirstName" Maxlength="100" Class="w-full" Required="true"  AutoComplete="off"/>
                    <FluentValidationMessage For="@(() => Content.User.FirstName)" />
                </FluentGridItem>

                <FluentGridItem xs="12" md="6">
                    <FluentTextField @bind-Value="@Content!.User.LastName" Maxlength="100" Label="@Resources.UserDialogResource.LastName" Class="w-full" Required="true" AutoComplete="off" />
                    <FluentValidationMessage For="@(() => Content.User.LastName)" />
                </FluentGridItem>

                @*   <FluentGridItem xs="12" md="6">
                    <FluentTextField @bind-Value="@Content!.MiddleName" Maxlength="100" Label="@Resources.UserDialogResource.MiddleName" Class="w-full" />
                    <FluentValidationMessage For="@(() => Content.MiddleName)" />
                </FluentGridItem> *@

                <FluentGridItem xs="12" md="6">
                    <FluentGrid Spacing="1">
                        <FluentGridItem xs="12">
                            <FluentTextField @ref="emailTxtField" Required="true" @bind-Value="@Content!.User.Email" Maxlength="50" Label="@Resources.UserDialogResource.Email" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.User.Email)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12">
                            <FluentLabel Class="mb-1">&nbsp;</FluentLabel>
                            <FluentButton IconStart="@(new Icons.Regular.Size16.Password())" OnClick="SetPassword"><span>@Resources.UserDialogResource.SetPasswordBtn_Text</span></FluentButton>
                        </FluentGridItem>
                    </FluentGrid>
                </FluentGridItem>

                <FluentGridItem xs="12" md="6">
                    <FluentGrid Spacing="1">
                        <FluentGridItem xs="12">
                            <FluentTextField @bind-Value="@Content!.User.Mobile" Maxlength="30" Label="@Resources.UserDialogResource.Mobile" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.User.Mobile)" />
                        </FluentGridItem>

                        <FluentGridItem xs="12">
                            <FluentTextField @bind-Value="@Content!.User.Phone" Maxlength="30" Label="@Resources.UserDialogResource.Phone" Class="w-full" AutoComplete="off" />
                            <FluentValidationMessage For="@(() => Content.User.Phone)" />
                        </FluentGridItem>
                    </FluentGrid>
                </FluentGridItem>

                @* Αν δεν θέλουμε η φόρμα να τα εμφανίζει όλα τα πεδία *@
                @if (this.Content!.AccountMode == false)
                {
                    <FluentGridItem xs="12" sm="6">

                        <FluentSelect Label="@Resources.UserDialogResource.Role" TOption="RoleLI" @bind-Value="@selectedRoleId" ReadOnly=@(this.Content!.User.Role!= null && this.Content!.User.Role!.Name == "SuperAdmin") Class="w-full max-w-80">
                            <FluentOption Value="@Guid.Empty.ToString()" Selected="@(Guid.Empty.ToString() == selectedRoleId)"><FluentLabel Color="@Color.Disabled">@("(" + GlobalResource.SelectValue + ")")</FluentLabel></FluentOption>
                            @foreach (var item in this.roles!)
                            {
                                <FluentOption TOption="RoleLI" Value="@(item.RoleId.ToString())" Selected="@(item.RoleId.ToString() == selectedRoleId)">
                                    @(item == null ? "" : item.Name)
                                </FluentOption>
                            }
                        </FluentSelect>

                    </FluentGridItem>

                    <FluentGridItem xs="12" sm="6">
                        <FluentLabel Class="mb-1">&nbsp;</FluentLabel>
                        <FluentSwitch @bind-Value="@Content!.User.AvailableInEventUsers" Label="@Resources.UserDialogResource.AvailableInEventUsers" Class="content-center" />
                    </FluentGridItem>
                }
            </FluentGrid>
        </ChildContent>
    </EditForm>
</FluentDialogBody>