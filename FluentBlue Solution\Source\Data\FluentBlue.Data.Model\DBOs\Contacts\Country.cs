﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace FluentBlue.Data.Model.DBOs.Contacts
{
    [Table("Country", Schema = "Contacts")]
    public class Country : IObjectState
    {
        public Country()
        {
            Name = string.Empty;
            //Currencies = new List<Currency>();
            DateModifiedUtc = DateTime.UtcNow;
            this.ObjectState = ObjectState.Unchanged;
            RowVersion = new byte[0];
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Contacts.CountryResource), Name = "CountryId")]
        public Guid CountryId { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.CountryResource), Name = "CountryCode")]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        [MaxLength(3)]
        public string CountryCode { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Contacts.CountryResource), Name = "Name")]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        //[ForeignKey("CurrencyId")]
        //public Currency Currency { get; set; }

        //[Column(TypeName = "datetime")]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateModified")]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;
    }
}
