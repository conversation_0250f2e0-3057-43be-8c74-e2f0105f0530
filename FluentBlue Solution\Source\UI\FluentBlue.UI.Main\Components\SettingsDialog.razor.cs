﻿using AKSoftware.Blazor.Utilities;
using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using NodaTime.TimeZones;
using System.Data;
using System.Globalization;
using Microsoft.Extensions.Caching.Hybrid;
using FluentBlue.Data.Model.DBOs.Settings;
using Syncfusion.Blazor.Popups;

namespace FluentBlue.UI.Main.Components
{
    public partial class SettingsDialog
    {
        private Language? initialLanguage;
        //private FluentDesignTheme fluentDesignTheme;
        private FluentSelect<ThemeColorMode> colorModeSelect;
        private DesignThemeModes currentDesignThemeMode;  //Χρησιμοποιείται από το FluentDesignTheme
        private ThemeColorMode currentThemeColorMode;
        private ThemeColor currentThemeColor;
        private string currentThemeColorText;  //Η τιμή π.χ. #dd23F0 του currentThemeColor
        private ThemeColorMode initialThemeColorMode;
        private ThemeColor initialThemeColor;
        private FluentSelect<string> dateFormatSelect = new();
        private FluentSelect<string> timeFormatSelect = new();
        private bool mondayChecked;
        private bool tuesdayChecked;
        private bool wednesdayChecked;
        private bool thursdayChecked;
        private bool fridayChecked;
        private bool saturdayChecked;
        private bool sundayChecked;
        private DateTime? workStart;
        private DateTime? workEnd;
        private DayOfWeek firstDayOfWeek;
        private TimeScaleInterval calendarTimeScaleInterval;

        //General
        private bool shouldRender;
        private Orientation settingsOrientation = Orientation.Horizontal;
        private FluentTabs settingsTabs = new();
        private string menuBtnText = @Resources.SettingsDialogResource.General;
        private FluentMenuButton menuBtn = new();
        IDialogReference? dialog;
        private string activeTab = "General";
        private FluentCombobox<FluentBlue.Shared.Utilities.TimeZone> timeZoneSelect = new();
        private bool isSaving = false;

        //Users
        FluentSearch? searchUsersTxtBox = new();
        IQueryable<Data.Model.DTOs.UserView> users = new List<UserView>().AsQueryable();
        PaginationState usersPagination = new PaginationState { ItemsPerPage = 10 };
        bool loadingUsers = false;

        //Roles
        Role userRole = new();  //To Role του τρέχοντος χρήστη.
        FluentDataGrid<Data.Model.DBOs.Tenants.Role> rolesDataGrid = new FluentDataGrid<Data.Model.DBOs.Tenants.Role>();
        FluentSearch? searchRolesTxtBox = new();
        IQueryable<Data.Model.DBOs.Tenants.Role> roles = new List<Role>().AsQueryable();
        PaginationState rolesPagination = new PaginationState { ItemsPerPage = 10 };
        bool loadingRoles = false;

        [Parameter]
        public Data.Model.DBOs.Settings.UserSetting Content { get; set; } = new Data.Model.DBOs.Settings.UserSetting();
        private EditContext userSettingsContext = new EditContext(typeof(Data.Model.DBOs.Settings.UserSetting));

        List<Option<string?>> colorModeOptions = Enum.GetValues<Data.Model.ThemeColorMode>().Cast<Data.Model.ThemeColorMode?>().Select(x => new Option<string?> { Text = x!.Value.ToString(), Value = x.Value.ToString() }).ToList();

        [CascadingParameter]
        public FluentDialog? Dialog { get; set; }

        private FluentValidationValidator? fluentValidationValidator;

        private List<string> timeZones = new List<string>();

        protected override bool ShouldRender()
        {
            bool tempShouldRender = this.shouldRender;
            this.shouldRender = false;
            return tempShouldRender;
        }

        protected void StateHasChangedOptimized()
        {
            this.shouldRender = true;
            StateHasChanged();
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                //this.timeZones = FluentBlue.Shared.Utilities.TimeZone.AllTimeZones.Select(x => x.ZoneId).ToList();

                UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);

                //Reads the TimeZones from cache.
                var timeZonesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };

                this.timeZones = await cache.GetOrCreateAsync(
                    Keywords.TimeZones,
                    async cancel =>
                    {
                        return FluentBlue.Shared.Utilities.TimeZone.AllTimeZones.Select(x => x.ZoneId).ToList();
                    },
                    timeZonesCacheOptions);

                //Reads the UserRole from the cache/database
                var userRoleCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(120) };

                this.userRole = await cache.GetOrCreateAsync(
                    Keywords.UserRole,
                    async cancel =>
                    {
                        return await usersWebApiClient.GetUserRole(AuthenticatedUserData.UserId);
                    },
                    userRoleCacheOptions);

                //Διαβάζει τα UserSettings από το cache/βάση δεδομένων
                //this.Content = await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                this.Content = await cache.GetOrCreateAsync(Keywords.UserSetting,
                    async cancel =>
                    {
                        UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                        return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                    }, userSettingCacheOptions);

                this.userSettingsContext = new EditContext(this.Content!);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    this.settingsOrientation = Orientation.Vertical;
                }
                else
                {
                    this.settingsOrientation = Orientation.Horizontal;
                }

                StateHasChangedOptimized();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    //if (formFactor.GetDeviceIdiom() == "Phone")
                    //{
                    //    await JS.InvokeVoidAsync("applyStyleForDevices");
                    //}

                    this.initialLanguage = this.Content!.Language;
                    this.initialThemeColorMode = this.Content!.ThemeColorMode;
                    this.initialThemeColor = this.Content!.ThemeColor;

                    this.currentThemeColorMode = this.Content!.ThemeColorMode;
                    this.currentDesignThemeMode = this.ConvertThemeColorModeToThemeMode(this.Content!.ThemeColorMode);
                    this.currentThemeColor = this.Content!.ThemeColor;
                    this.currentThemeColorText = this.Content!.ThemeColor.GetEnumDescription();

                    this.SetDataToControls();

                    StateHasChangedOptimized();
                }
                else
                {

                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task MenuBtnOnMenuChanged(MenuChangeEventArgs args)
        {
            try
            {
                //await AccentBaseColor.SetValueFor(this.menuBtn.Button!.Element, $"#{args.Id}".ToSwatch());
                if (args.Id != "")
                {
                    //this.settingsTabs.ActiveTabId = args.Id;
                    this.activeTab = args.Id!;
                    if (args.Id == "General")
                    {
                        this.menuBtnText = @Resources.SettingsDialogResource.General;
                    }
                    else if (args.Id == "Contacts")
                    {
                        this.menuBtnText = @Resources.SettingsDialogResource.Contacts;
                    }
                    else if (args.Id == "Calendar")
                    {
                        this.menuBtnText = @Resources.SettingsDialogResource.Calendar;
                    }
                    else if (args.Id == "UsersRoles")
                    {
                        this.menuBtnText = @Resources.SettingsDialogResource.UsersRoles;
                        await LoadUsers();
                    }

                    StateHasChangedOptimized();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void OnInvalidSubmit()
        {
        }

        public async Task OnValidSubmit()
        {
            try
            {
                await this.Save();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void CancelBtnOnClick()
        {
            try
            {
                //Επαναφέρει τις αρχικές τιμές.
                this.Content!.ThemeColorMode = this.initialThemeColorMode;
                this.Content!.ThemeColor = this.initialThemeColor;


                //this.currentDesignThemeMode = ConvertThemeColorModeToThemeMode(this.Content!.ThemeColorMode);  //Ρυθμίζει το τρέχον DesignThemeMode.
                //this.currentThemeColor = this.Content!.ThemeColor;
                //this.currentThemeColorText = this.Content!.ThemeColor.GetEnumDescription();

                MessagingCenter.Send(this, Keywords.ThemeUpdated, new object[] { this.currentDesignThemeMode.ToString(), (byte)this.currentThemeColor });
                MessagingCenter.Send(this, Keywords.SettingsUpdated);

                //StateHasChanged();
                //Render();

                await this.Dialog!.CloseAsync(this.Content);
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task Save()
        {
            try
            {
                this.isSaving = true;
                bool requiresRestart = false;  //Καθορίζει αν πρέπει να γίνει restart η εφαρμογή.

                this.GetDataFromControls();

                if (await this.ValidateData())
                {
                    //Αν έχει αλλάξει η γλώσσα πρέπει να κάνουμε restart την εφαρμογή
                    if (this.Content!.Language != this.initialLanguage || this.Content.Language.GetCultureInfo().Name != CultureInfo.CurrentUICulture.Name)
                    {
                        //Ρωτάει τον χρήστη αν σίγουρα θέλει να κάνει restart την εφαρμογή, ώστε να αλλάξει η γλώσσα.
                        //var dialog = await dialogService.ShowConfirmationAsync(Resources.SettingsDialogResource.LanguageChangeRequiresRestartConfirmation, GlobalResource.Yes, GlobalResource.No, "");
                        //DialogResult dlgResult = await dialog.Result;
                        //await dialog.CloseAsync();

                        //if (dlgResult.Cancelled != true)
                        //{
                        //Περνάει τις νέες τιμες των UserSetting στο devicePreferences.
                        devicePreferences.Set("TimeZone", this.Content.TimeZone!);
                        devicePreferences.Set("Language", this.Content.Language.GetCultureInfo().Name.Contains("el") ? "el" : "en");
                        //devicePreferences.Set("ShortTimeFormat", this.Content.TimeFormat!);
                        //devicePreferences.Set("LongTimeFormat", this.Content.TimeFormat!);
                        //devicePreferences.Set("ShortDateFormat", this.Content.DateFormat!);

                        //Περνάει τις νέες τιμες των UserSetting στο localStorage.
                        await localStorage.SetItemAsync<string>("TimeZone", this.Content.TimeZone!);
                        await localStorage.SetItemAsync<string>("Language", this.Content.Language.GetCultureInfo().Name.Contains("el") ? "el" : "en");
                        //await localStorage.SetItemAsync<string>("ShortTimeFormat", this.Content.TimeFormat!);
                        //await localStorage.SetItemAsync<string>("ShortDateFormat", this.Content.DateFormat!);

                        Thread.CurrentThread.CurrentCulture = this.Content!.Language.GetCultureInfo();
                        Thread.CurrentThread.CurrentUICulture = this.Content!.Language.GetCultureInfo();
                        CultureInfo.DefaultThreadCurrentCulture = this.Content!.Language.GetCultureInfo();
                        CultureInfo.DefaultThreadCurrentUICulture = this.Content!.Language.GetCultureInfo();

                        requiresRestart = true;
                        //}
                    }


                    CultureInfo.CurrentCulture.DateTimeFormat.ShortTimePattern = this.Content.TimeFormat;
                    CultureInfo.CurrentCulture.DateTimeFormat.LongTimePattern = this.Content.TimeFormat;
                    CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern = this.Content.DateFormat;
                    CultureInfo.CurrentUICulture.DateTimeFormat.ShortTimePattern = this.Content.TimeFormat;
                    CultureInfo.CurrentUICulture.DateTimeFormat.LongTimePattern = this.Content.TimeFormat;
                    CultureInfo.CurrentUICulture.DateTimeFormat.ShortDatePattern = this.Content.DateFormat;

                    List<string> result = this.userSettingsContext.GetValidationMessages().ToList();
                    if (this.Content!.ObjectState != ObjectState.Added)
                    {
                        this.Content.ObjectState = ObjectState.Modified;
                    }
                    this.Content!.DateModifiedUtc = DateTime.UtcNow;

                    //Saves the UserSettings in database.
                    SettingsWebApiClient settingsWebApiClient = new SettingsWebApiClient(httpClient, userSettingsWebApiClientLogger);
                    await settingsWebApiClient.CreateOrUpdateUserSettings(this.Content);

                    // Store UserSetting in cache.
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromHours(500) };
                    await cache.SetAsync(Keywords.UserSetting, this.Content, userSettingCacheOptions);

                    //Αν έχει αλλάξει το language τότε πρέπει να γίνει restart η εφαρμογή.
                    if (requiresRestart)
                    {
                        if (formFactor.GetDeviceIdiom() == "Phone" || formFactor.GetDeviceIdiom() == "Desktop")  //Αν είμαστε σε device.
                        {
                            //Thread.CurrentThread.CurrentCulture = this.Content!.Language.GetCultureInfo();
                            //Thread.CurrentThread.CurrentUICulture = this.Content!.Language.GetCultureInfo();
                            //CultureInfo.DefaultThreadCurrentCulture = this.Content!.Language.GetCultureInfo();
                            //CultureInfo.DefaultThreadCurrentUICulture = this.Content!.Language.GetCultureInfo();

                            navManager.NavigateTo("/", forceLoad: false);
                            navManager.NavigateTo("/contacts", forceLoad: false);
                            navManager.NavigateTo("/calendar", forceLoad: false);
                            navManager.NavigateTo("/", forceLoad: false);

                            return;
                            //navManager.NavigateTo("/", forceLoad: false);
                        }
                        else  //Αν είμαστε στο web.
                        {
                            navManager.NavigateTo("/", forceLoad: true);
                        }
                    }
                    else
                    {
                        MessagingCenter.Send(this, Keywords.SettingsUpdated);
                        await this.Dialog!.CloseAsync(this.Content);
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(ApplicationException))
                {
                    new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
                else { new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, ""); }
                logger.LogError(ex, ex.Message);
            }
            finally
            {
                this.isSaving = false;
            }
        }

        public async Task SaveBtnOnClick()
        {
            try
            {
                await Save();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void GetDataFromControls()
        {
            try
            {
                List<string> calendarWorkDays = new List<string>();
                if (this.mondayChecked) { calendarWorkDays.Add("1"); }
                if (this.tuesdayChecked) { calendarWorkDays.Add("2"); }
                if (this.wednesdayChecked) { calendarWorkDays.Add("3"); }
                if (this.thursdayChecked) { calendarWorkDays.Add("4"); }
                if (this.fridayChecked) { calendarWorkDays.Add("5"); }
                if (this.saturdayChecked) { calendarWorkDays.Add("6"); }
                if (this.sundayChecked) { calendarWorkDays.Add("7"); }
                this.Content!.FirstDayOfWeek = (DayOfWeek)this.firstDayOfWeek;
                this.Content!.CalendarWorkDays = String.Join(String.Empty, calendarWorkDays.ToArray());
                //this.Content!.CalendarWorkStart = this.workStart == null ? TimeSpan.Zero : this.workStart!.Value.TimeOfDay;
                this.Content!.CalendarWorkEnd = this.workEnd == null ? TimeSpan.Zero : this.workEnd!.Value.TimeOfDay;
                this.Content!.ThemeColor = this.currentThemeColor;
                this.Content!.ThemeColorMode = this.currentThemeColorMode;

                //if (this.Content.DateFormat == string.Empty)
                //{
                //    this.Content.DateFormat = this.dateFormatSelect.Items!.First();
                //}

                //if (this.Content.TimeFormat == string.Empty)
                //{
                //    this.Content.TimeFormat = this.timeFormatSelect.Items!.First();
                //}
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void SetDataToControls()
        {
            try
            {
                string calendarWorkDays = this.Content!.CalendarWorkDays;
                this.mondayChecked = calendarWorkDays.Contains("1");
                this.tuesdayChecked = calendarWorkDays.Contains("2");
                this.wednesdayChecked = calendarWorkDays.Contains("3");
                this.thursdayChecked = calendarWorkDays.Contains("4");
                this.fridayChecked = calendarWorkDays.Contains("5");
                this.saturdayChecked = calendarWorkDays.Contains("6");
                this.sundayChecked = calendarWorkDays.Contains("7");
                this.firstDayOfWeek = this.Content!.FirstDayOfWeek;
                this.workStart = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, this.Content!.CalendarWorkStart.Hours, this.Content!.CalendarWorkStart.Minutes, this.Content!.CalendarWorkStart.Seconds);
                this.workEnd = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, this.Content!.CalendarWorkEnd.Hours, this.Content!.CalendarWorkEnd.Minutes, this.Content!.CalendarWorkEnd.Seconds);
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task<bool> ValidateData()
        {
            try
            {
                UserSettingValidator validator = new UserSettingValidator();

                this.fluentValidationValidator.Validator = validator;
                bool valid = this.fluentValidationValidator!.Validate();
                if (valid == false)
                {
                    // Convert error messages to HTML bullet list
                    string errors = GlobalResource.CorrectInvalidFields;
                    errors += "<ul>" + string.Join("", this.fluentValidationValidator.GetFailuresFromLastValidation().Select(e => $"<li>{e.ErrorMessage}</li>")) + "</ul>";
                    RenderFragment errorRF = new RenderFragment(builder =>
                    {
                        builder.AddMarkupContent(0, errors);
                    });

                    // Show errors in dialog
                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = Microsoft.FluentUI.AspNetCore.Components.DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                return valid;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async void SettingsTabsOnTabChange(FluentTab tab)
        {
            try
            {
                if (tab != null)
                {
                    //this.settingsTabs!.ActiveTabId = tab!.Id!;
                    this.activeTab = tab!.Id!;

                    //Αν το active tab είναι το usersRolesTab.
                    if (tab.Id == "UsersRoles")
                    {
                        //if (formFactor.GetDeviceIdiom() == "Phone")
                        //{
                        //    await JS.InvokeVoidAsync("applyFluentDialogStyleForDevices");
                        //    await JS.InvokeVoidAsync("applyFluentTabsStyleForDevices");
                        //}

                        await LoadUsers();
                        await LoadRoles();
                    }

                    StateHasChangedOptimized();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        #region General settings
        private void OnThemeModeChanged(ThemeColorMode selectedValue)
        {
            try
            {
                this.currentThemeColorMode = selectedValue;
                this.currentDesignThemeMode = ConvertThemeColorModeToThemeMode(selectedValue);

                MessagingCenter.Send(this, Keywords.ThemeUpdated, new object[] { this.currentDesignThemeMode.ToString(), (byte)this.currentThemeColor });
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private DesignThemeModes ConvertThemeColorModeToThemeMode(ThemeColorMode colorMode)
        {
            if (colorMode == ThemeColorMode.Light)
            {
                return DesignThemeModes.Light;
            }
            else if (colorMode == ThemeColorMode.Dark)
            {
                return DesignThemeModes.Dark;
            }
            //else if (colorMode == ThemeColorMode.System)
            //{
            //    return DesignThemeModes.System;
            //}
            else
            {
                return DesignThemeModes.Light;  //Δεν θα τρέξει ποτέ.
            }
        }

        public void OnThemeColorChanged(ThemeColor? color)
        {
            try
            {
                this.currentThemeColor = color!.Value;
                this.currentThemeColorText = color!.Value.GetEnumDescription();

                MessagingCenter.Send(this, Keywords.ThemeUpdated, new object[] { this.currentDesignThemeMode.ToString(), (byte)this.currentThemeColor });
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion

        #region  Contacts settings
        private async Task OpenContactCategoriesDialog()
        {
            try
            {
                string dialogWidth = "600px", dialogHeight = "500px";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                var parameters = new DialogParameters()
                {
                    Title = Resources.SettingsDialogResource.ContactCategoriesTitle,
                    PreventDismissOnOverlayClick = true,
                    PrimaryAction = "",
                    SecondaryAction = "",
                    Width = dialogWidth,
                    Height = dialogHeight,
                    Modal = true,
                    ShowDismiss = false
                };

                var dialog = await dialogService.ShowDialogAsync<ContactCategoriesDialog>(parameters);
                var result = await dialog.Result;

                if (result.Cancelled == false)
                {
                    // Refresh the categories after changes
                    //await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        #endregion

        #region Calendar settings
        public void OnFirstDayOfWeekChanged(DayOfWeek day)
        {
            try
            {
                this.firstDayOfWeek = day;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void OnCalendarTimeScaleIntervalChanged(TimeScaleInterval value)
        {
            try
            {
                this.Content!.CalendarTimeScaleInterval = value;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void OnCalendarDefaultViewChanged(Data.Model.CalendarView value)
        {
            try
            {
                this.Content!.CalendarDefaultView = value;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void OnCalendarDefaultViewOnMobilesChanged(Data.Model.CalendarView value)
        {
            try
            {
                this.Content!.CalendarDefaultViewOnMobiles = value;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        public void OnCalendarScrollToTimeChanged(Data.Model.CalendarScrollToTime value)
        {
            try
            {
                this.Content!.CalendarScrollToTime = value;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void OnTimeZoneSearch(OptionsSearchEventArgs<TzdbZoneLocation> e)
        {
            e.Items = TzdbDateTimeZoneSource.Default.ZoneLocations.AsQueryable().Where(i => i.ZoneId.StartsWith(e.Text, StringComparison.OrdinalIgnoreCase) ||
                                             i.ZoneId.StartsWith(e.Text, StringComparison.OrdinalIgnoreCase))
                                 .OrderBy(i => i.ZoneId);
        }

        private async Task OpenEventCategoriesDialog()
        {
            try
            {
                string dialogWidth = "600px", dialogHeight = "500px";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                var parameters = new DialogParameters()
                {
                    Title = Resources.SettingsDialogResource.EventCategoriesTitle,
                    PreventDismissOnOverlayClick = true,
                    PrimaryAction = "",
                    SecondaryAction = "",
                    Width = dialogWidth,
                    Height = dialogHeight,
                    Modal = true,
                    ShowDismiss = false
                };

                var dialog = await dialogService.ShowDialogAsync<EventCategoriesDialog>(parameters);
                var result = await dialog.Result;

                if (result.Cancelled == false)
                {
                    // Refresh the categories after changes
                    //await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OpenEventStatesDialog()
        {
            try
            {
                string dialogWidth = "600px", dialogHeight = "500px";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm" || ScreenSizeTracker.CurrentBreakpoint == "md")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                var parameters = new DialogParameters()
                {
                    Title = Resources.SettingsDialogResource.EventStatesTitle,
                    PreventDismissOnOverlayClick = true,
                    PrimaryAction = "",
                    SecondaryAction = "",
                    Width = dialogWidth,
                    Height = dialogHeight,
                    Modal = true,
                    ShowDismiss = false
                };

                var dialog = await dialogService.ShowDialogAsync<EventStatesDialog>(parameters);
                var result = await dialog.Result;

                if (result.Cancelled == false)
                {
                    // Refresh the categories after changes
                    //await eventCategoriesWebApiClient.GetAllEventCategories(AuthenticatedUserData.TenantId);
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion

        #region  Users
        private async Task LoadUsers()
        {
            try
            {
                //this.loading = true;

                string filter = this.searchUsersTxtBox!.Value ?? "";
                Task<PagedData<List<Data.Model.DTOs.UserView>>> usersTask = new WebApi.Client.UsersWebApiClient(httpClient, usersWebApiClientLogger).GetUsers(AuthenticatedUserData.TenantId, filter, (UInt16)(this.usersPagination.CurrentPageIndex + 1), (UInt16)this.usersPagination.ItemsPerPage);
                PagedData<List<Data.Model.DTOs.UserView>> usersPagedData = await usersTask;
                if (usersPagedData.Data != null)
                {
                    this.users = usersPagedData.Data.AsQueryable();
                    await this.usersPagination.SetTotalItemCountAsync(usersPagedData.DataTotalCount);
                    //this.StateHasChanged();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
                //this.loading = false;
                //this.StateHasChanged();
            }
        }

        private async Task NewUserBtnOnClick()
        {
            try
            {
                User user = User.CreateUser(AuthenticatedUserData.TenantId);
                user.UserTimeZoneId = this.Content!.TimeZone;

                DialogResult dlgResult = await this.ShowUser(user);
                if (!dlgResult.Cancelled)
                {
                    await this.LoadUsers();
                    StateHasChangedOptimized();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnEditUser(Guid userId)
        {
            try
            {
                UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                Data.Model.DBOs.Tenants.User? user = await usersWebApiClient.GetUser(userId);

                if (user != null)
                {
                    user.UserTimeZoneId = this.Content!.TimeZone;
                    DialogResult dlgResult = await this.ShowUser(user);
                    if (!dlgResult.Cancelled)
                    {
                        await this.LoadUsers();
                        StateHasChangedOptimized();
                    }
                }
                else
                {
                    var dialog = await dialogService.ShowInfoAsync(Components.Resources.SettingsDialogResource.UserNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                    DialogResult? result = await dialog.Result;
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnDeleteUser(Guid userId)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();
                if (result.Cancelled == false)
                {
                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    await usersWebApiClient.DeleteUser(userId);

                    await this.LoadUsers();
                    StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task<DialogResult> ShowUser(Data.Model.DBOs.Tenants.User user)
        {
            try
            {
                string dialogWidth = "900px", dialogHeight = "550px";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                UserDialogInput userDialogInput = new UserDialogInput() { User = user, AccountMode = false };

                // Display the dialog  
                DialogParameters<UserDialogInput> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnUserDialogResult),
                    Title = UI.Main.Components.Resources.UserDialogResource.Title,
                    PrimaryAction = "",
                    SecondaryAction = "",
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.UserDialog>(userDialogInput, parameters);
                DialogResult result = await dialog.Result;
                return result;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }

            return DialogResult.Cancel();
        }

        private async Task OnUserDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                    await this.LoadUsers();
                }
                if (dialog != null)
                {
                    await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void SearchUsers_ValueChanged()
        {
            try
            {
                if (this.searchUsersTxtBox.Value == "")
                {
                    await this.LoadUsers();
                    StateHasChangedOptimized();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SearchUsers_OnKeyUp(KeyboardEventArgs args)
        {
            try
            {
                if (args.Key == "Enter")
                {
                    await this.LoadUsers();
                    StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task UsersPagination_CurrentPageIndexChanged(int args)
        {
            try
            {
                await this.LoadUsers();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void UsersDataGridOnRowDoubleClick(FluentDataGridRow<UserView> e)
        {
            try
            {
                await OnEditUser(e.Item!.UserId);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void UsersDataGridOnRowClick(FluentDataGridRow<UserView> e)
        {
            try
            {
                await OnEditUser(e.Item!.UserId);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion

        #region  Roles
        private async Task LoadRoles()
        {
            try
            {
                string filter = this.searchRolesTxtBox!.Value ?? "";
                Task<PagedData<List<Data.Model.DBOs.Tenants.Role>>> rolesTask = new WebApi.Client.RolesWebApiClient(httpClient, rolesWebApiClientLogger).GetRoles(AuthenticatedUserData.TenantId, filter, (UInt16)(this.rolesPagination.CurrentPageIndex + 1), (UInt16)this.rolesPagination.ItemsPerPage);
                PagedData<List<Data.Model.DBOs.Tenants.Role>> rolesPagedData = await rolesTask;
                if (rolesPagedData.Data != null)
                {
                    this.roles = rolesPagedData.Data.AsQueryable();
                    await this.rolesPagination.SetTotalItemCountAsync(rolesPagedData.DataTotalCount);
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task NewRoleBtnOnClick()
        {
            try
            {
                Role role = Role.CreateRole(AuthenticatedUserData.TenantId);
                role.UserTimeZoneId = this.Content!.TimeZone;

                DialogResult dlgResult = await this.ShowRole(role);
                if (!dlgResult.Cancelled)
                {
                    await this.LoadRoles();
                    StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnEditRole(Guid roleId)
        {
            try
            {
                RolesWebApiClient rolesWebApiClient = new RolesWebApiClient(httpClient, rolesWebApiClientLogger);
                Data.Model.DBOs.Tenants.Role? role = await rolesWebApiClient.GetRole(roleId);

                if (role != null)
                {
                    role.UserTimeZoneId = this.Content!.TimeZone;
                    DialogResult dlgResult = await this.ShowRole(role);
                    if (!dlgResult.Cancelled)
                    {
                        await this.LoadRoles();
                        StateHasChangedOptimized();
                    }
                }
                else
                {
                    var dialog = await dialogService.ShowInfoAsync(Components.Resources.SettingsDialogResource.RoleNotExists);  //TODO: ίσως να μπει ερώτηση στο χρήστη αν θέλει να γίνει αυτόματα το sync.
                    DialogResult? result = await dialog.Result;
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnDeleteRole(Guid roleId)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();
                if (result.Cancelled == false)
                {
                    RolesWebApiClient rolesWebApiClient = new RolesWebApiClient(httpClient, rolesWebApiClientLogger);
                    await rolesWebApiClient.DeleteRole(roleId);

                    await this.LoadRoles();
                    StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SearchRoles_OnKeyUp(KeyboardEventArgs args)
        {
            try
            {
                if (args.Key == "Enter")
                {
                    await this.LoadRoles();
                    StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SearchRoles_ValueChanged(string value)
        {
            try
            {
                if (this.searchRolesTxtBox!.Value == "")
                {
                    await this.LoadRoles();
                    StateHasChangedOptimized();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void RolesDataGridOnCellClick(FluentDataGridCell<Role> e)
        {
            try
            {
                if (e.CellType == DataGridCellType.ColumnHeader && e.AdditionalAttributes != null && e.AdditionalAttributes["aria-sort"] != null)
                {
                    if ((e.AdditionalAttributes["aria-sort"]?.ToString() ?? "") != (e.Data?.ToString() ?? ""))
                    {
                        e.Data = e.AdditionalAttributes["aria-sort"].ToString();
                        await this.LoadRoles();
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void RolesDataGridOnRowDoubleClick(FluentDataGridRow<Role> e)
        {
            try
            {
                if (e.Item != null)
                {
                    await OnEditRole(e.Item!.RoleId);
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void RolesDataGridOnRowClick(FluentDataGridRow<Role> e)
        {
            try
            {
                await OnEditRole(e.Item!.RoleId);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async Task<DialogResult> ShowRole(Data.Model.DBOs.Tenants.Role role)
        {
            try
            {
                string dialogWidth = "650px", dialogHeight = "500px";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<Data.Model.DBOs.Tenants.Role> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnRoleDialogResult),
                    Title = UI.Main.Components.Resources.RoleDialogResource.Title,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.RoleDialog>(role, parameters);
                DialogResult result = await dialog.Result;
                return result;
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");

            }                
            
            return DialogResult.Cancel();
        }

        private async Task OnRoleDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                    await this.LoadRoles();
                }
                if (dialog != null)
                {
                    await dialog.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task RolesPagination_CurrentPageIndexChanged(int pageIndex)
        {
            try
            {
                await this.LoadRoles();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion
    }
}
