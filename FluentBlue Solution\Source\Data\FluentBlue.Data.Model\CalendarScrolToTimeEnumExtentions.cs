﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public static class CalendarScrollToTimeEnumExtensions
    {
        public static string GetBusinessValue(this CalendarScrollToTime calendarScrollToTime)
        {
            switch (calendarScrollToTime)
            {
                case CalendarScrollToTime.None:
                    {
                         return "";
                    }
                case CalendarScrollToTime.One:
                    {
                        return "01:00";
                    }
                case CalendarScrollToTime.Two:
                    {
                        return "02:00";
                    }
                case CalendarScrollToTime.Three:
                    {
                        return "03:00";
                    }
                case CalendarScrollToTime.Four:
                    {
                        return "04:00";
                    }
                case CalendarScrollToTime.Five:
                    {
                        return "05:00";
                    }
                case CalendarScrollToTime.Six:
                    {
                        return "06:00";
                    }
                case CalendarScrollToTime.Seven:
                    {
                        return "07:00";
                    }
                case CalendarScrollToTime.Eight:
                    {
                        return "08:00";
                    }
                case CalendarScrollToTime.Nine:
                    {
                        return "09:00";
                    }
                case CalendarScrollToTime.Ten:
                    {
                        return "10:00";
                    }
                default:
                    {
                        return "";
                    }
            }
        }

    }
}
