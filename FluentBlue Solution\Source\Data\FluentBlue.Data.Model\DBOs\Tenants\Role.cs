﻿using FluentBlue.Data.Model.DTOs;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DBOs.Tenants
{
    [Table("Role", Schema = "Tenants")]
    public class Role : IObjectState, INotifyPropertyChanged
    {
        public Role()
        {
            RoleId = Guid.CreateVersion7();
            Name = string.Empty;
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            RowVersion = new byte[0];
            ObjectState = ObjectState.Unchanged;
            //this.userTimeZoneId = string.Empty;
            this.Users = new List<User>();
        }

        [Key]
        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = "RoleId")]
        public Guid RoleId { get; set; }

        public Guid TenantId { get; set; }

        //[Required]
        [ForeignKey("TenantId")]
        public Tenant? Tenant { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = nameof(Model.Resources.Tenants.RoleDisplayResource.Name))]
        public string Name { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = nameof(Model.Resources.Tenants.RoleDisplayResource.CanAdministerUsers))]
        public bool CanAdministerUsers { get; set; } = false;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = nameof(Model.Resources.Tenants.RoleDisplayResource.CanAdministerRoles))]
        public bool CanAdministerRoles { get; set; } = false;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = nameof(Model.Resources.Tenants.RoleDisplayResource.CanViewOthersAppointments))]
        public bool CanViewOthersAppointments { get; set; } = false;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = nameof(Model.Resources.Tenants.RoleDisplayResource.CanEditApplicationSettings))]
        public bool CanEditApplicationSettings { get; set; } = false;

        public List<User> Users { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Display(ResourceType = typeof(Data.Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateCreatedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; } = new byte[0];

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            //DateModifiedUtc = DateTime.UtcNow;

            //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
            //if (ObjectState == ObjectState.Unchanged)
            //{
            //    ObjectState = ObjectState.Modified;
            //}

            if (PropertyChanged != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }

        }

        public static Role CreateRole(Guid tenantId)
        {
            Role role = new Role();
            role.TenantId = tenantId;
            role.ObjectState = ObjectState.Added;

            return role;
        }
    }
}
