trigger:
- main

pool:
  vmImage: windows-latest

variables:
  buildConfiguration: 'Release'
  androidPackageDir: '$(Build.ArtifactStagingDirectory)/android'
  iOSPackageDir: '$(Build.ArtifactStagingDirectory)/ios'
  windowsPackageDir: '$(Build.ArtifactStagingDirectory)/windows'

steps:
# Install required SDKs and tools
- task: UseDotNet@2
  displayName: 'Install .NET 5.0.x SDK'
  inputs:
    packageType: 'sdk'
    version: '5.0.x'

- task: UseDotNet@2
  displayName: 'Install .NET 9.0.x SDK'
  inputs:
    packageType: 'sdk'
    version: '9.0.x'

- task: NuGetToolInstaller@1
  displayName: 'Install NuGet'
  inputs:
    versionSpec: '5.11.0'

# Install required workloads
- script: |
    dotnet workload install wasm-tools
    dotnet workload install maui-android
    dotnet workload install ios
    dotnet workload install maui-windows
  displayName: 'Install .NET Workloads'

# Clear NuGet cache and restore packages
- script: nuget locals all -clear
  displayName: 'Clear NuGet Cache'

- task: NuGetCommand@2
  displayName: 'NuGet Restore'
  inputs:
    command: 'restore'
    restoreSolution: '**/*.sln'
    feedsToUse: 'select'

# Build and publish Web UI project
- task: VSBuild@1
  displayName: 'Build FluentBlue.UI.Web'
  inputs:
    solution: 'FluentBlue Solution/Source/UI/FluentBlue.UI.Web/FluentBlue.UI.Web.csproj'
    msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=FileSystem /p:PublishUrl="$(build.artifactStagingDirectory)/web" /p:DeleteExistingFiles=True'
    platform: 'any cpu'
    configuration: $(buildConfiguration)

- powershell: |
   Write-Host "Show all folder content"
   Get-ChildItem -Path $(build.artifactStagingDirectory)/web -Recurse -Force
  errorActionPreference: continue
  displayName: 'PowerShell Script List folder structure'
  continueOnError: true    

- task: ArchiveFiles@2
  displayName: 'Zip FluentBlue.UI.Web'
  inputs:
    rootFolderOrFile: '$(Build.ArtifactStagingDirectory)\web'
    includeRootFolder: false
    archiveFile: '$(Build.ArtifactStagingDirectory)\FluentBlue.UI.Web.zip'
    replaceExistingArchive: true

- powershell: |
   Write-Host "Show all folder content"
   Get-ChildItem -Path $(build.artifactStagingDirectory)/web -Recurse -Force
  errorActionPreference: continue
  displayName: 'PowerShell Script List folder structure'
  continueOnError: true    

- task: PublishPipelineArtifact@1
  displayName: 'Publish Web UI Artifact'
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)/FluentBlue.UI.Web.zip'
    artifactName: 'FluentBlue.UI.Web'

# Build and publish WebAPI project
- task: VSBuild@1
  displayName: 'Build FluentBlue.WebApi.Service'
  inputs:
    solution: 'FluentBlue Solution/Source/WebApi/FluentBlue.WebApi.Service/FluentBlue.WebApi.Service.csproj'
    msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=FileSystem /p:PublishUrl="$(build.artifactStagingDirectory)/webapi" /p:DeleteExistingFiles=True'
    platform: 'any cpu'
    configuration: $(buildConfiguration)

- task: ArchiveFiles@2
  displayName: 'Zip FluentBlue.WebApi.Service'
  inputs:
    rootFolderOrFile: '$(Build.ArtifactStagingDirectory)\webapi'
    includeRootFolder: false
    archiveFile: '$(Build.ArtifactStagingDirectory)\FluentBlue.WebApi.Service.zip'
    replaceExistingArchive: true

- task: PublishPipelineArtifact@1
  displayName: 'Publish WebAPI Artifact'
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)/webapi'
    artifactName: 'FluentBlue.WebApi.Service'


# # Build and publish Android app
# - task: DotNetCoreCLI@2
#   displayName: 'Restore Android Project with Runtime'
#   inputs:
#     command: 'restore'
#     projects: 'FluentBlue Solution/Source/UI/FluentBlue.UI.Devices/FluentBlue.UI.Devices.csproj'
#     arguments: '--runtime android-arm64 --runtime win-x64'

# - task: DotNetCoreCLI@2
#   displayName: 'Build Android App'
#   inputs:
#     command: 'build'
#     projects: 'FluentBlue Solution/Source/UI/FluentBlue.UI.Devices/FluentBlue.UI.Devices.csproj'
#     arguments: '--configuration $(BuildConfiguration) --no-restore /p:RuntimeIdentifier=android-arm64 /p:AndroidBuildApplicationPackage=true /p:OutputPath="$(androidPackageDir)"'

# # Replaced above
# #- task: VSBuild@1
# #  displayName: 'Build Android App'
# #  inputs:
# #    solution: 'FluentBlue Solution/Source/UI/FluentBlue.UI.Devices/FluentBlue.UI.Devices.csproj'
# #    msbuildArgs: '/p:Configuration=$(BuildConfiguration) /p:Platform="Any CPU" /p:AndroidBuildApplicationPackage=true /p:OutputPath="$(androidPackageDir)"'
# #    platform: 'any cpu'
# #    configuration: $(buildConfiguration)

# - task: ArchiveFiles@2
#   displayName: 'Zip Android Package'
#   inputs:
#     rootFolderOrFile: '$(androidPackageDir)'
#     archiveFile: '$(Build.ArtifactStagingDirectory)/FluentBlue.Android.zip'
#     replaceExistingArchive: true

# - task: PublishPipelineArtifact@1
#   displayName: 'Publish Android Artifact'
#   inputs:
#     targetPath: '$(Build.ArtifactStagingDirectory)/FluentBlue.Android.zip'
#     artifactName: 'FluentBlue.Android'

# # Build and publish iOS app
# - task: VSBuild@1
#   displayName: 'Build iOS App'
#   inputs:
#     solution: 'FluentBlue Solution/Source/UI/FluentBlue.UI.Devices/FluentBlue.UI.Devices.csproj'
#     msbuildArgs: '/p:Configuration=$(BuildConfiguration) /p:Platform="iPhone" /p:OutputPath="$(iOSPackageDir)"'
#     platform: 'any cpu'
#     configuration: $(buildConfiguration)

# - task: ArchiveFiles@2
#   displayName: 'Zip iOS Package'
#   inputs:
#     rootFolderOrFile: '$(iOSPackageDir)'
#     archiveFile: '$(Build.ArtifactStagingDirectory)/FluentBlue.iOS.zip'
#     replaceExistingArchive: true

# - task: PublishPipelineArtifact@1
#   displayName: 'Publish iOS Artifact'
#   inputs:
#     targetPath: '$(Build.ArtifactStagingDirectory)/FluentBlue.iOS.zip'
#     artifactName: 'FluentBlue.iOS'

# # Build and publish Windows app
# - task: VSBuild@1
#   displayName: 'Build Windows App'
#   inputs:
#     solution: 'FluentBlue Solution/Source/UI/FluentBlue.UI.Devices/FluentBlue.UI.Devices.csproj'
#     msbuildArgs: '/p:Configuration=$(BuildConfiguration) /p:Platform="x64" /p:OutputPath="$(windowsPackageDir)"'
#     platform: 'any cpu'
#     configuration: $(buildConfiguration)

# - task: ArchiveFiles@2
#   displayName: 'Zip Windows Package'
#   inputs:
#     rootFolderOrFile: '$(windowsPackageDir)'
#     archiveFile: '$(Build.ArtifactStagingDirectory)/FluentBlue.Windows.zip'
#     replaceExistingArchive: true

# - task: PublishPipelineArtifact@1
#   displayName: 'Publish Windows Artifact'
#   inputs:
#     targetPath: '$(Build.ArtifactStagingDirectory)/FluentBlue.Windows.zip'
#     artifactName: 'FluentBlue.Windows'

# Deploy database schema
# - task: SqlAzureDacpacDeployment@1
#   displayName: 'Deploy Database Schema'
#   inputs:
#     azureSubscription: 'Subscription1(12dd6ea4-12f2-4024-8cf5-550d4f87a5d9)'
#     AuthenticationType: 'connectionString'
#     ConnectionString: 'Server=tcp:fluentbluesqlsrv.database.windows.net,1433;Initial Catalog=FluentBlue;Persist Security Info=False;User ID=fluentBlueSqlAdmin;Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;'
#     deployType: 'DacpacTask'
#     DeploymentAction: 'Publish'
#     DacpacFile: '$(Build.ArtifactStagingDirectory)/database/FluentBlue.Data.Database.dacpac'
#     DatabaseName: 'FluentBlue'
#     IpDetectionMethod: 'AutoDetect'
#     AdditionalArguments: '/p:DropObjectsNotInSource=True /p:BlockOnPossibleDataLoss=False'

- task: VSBuild@1
  displayName: 'Build SQL Database Project (dacpac)'
  inputs:
    solution: '**/FluentBlue.Data.Database.sqlproj'  # Adjust path if needed
    platform: 'Any CPU'
    configuration: 'Release'
    msbuildArgs: '/p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath="$(Build.ArtifactStagingDirectory)/database"'


- task: PublishPipelineArtifact@1
  displayName: Publish Artifact for Database project (dacpac)
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)\database\FluentBlue.Data.Database.dacpac'   
    publishLocation: 'pipeline' 
    artifactName: 'FluentBlue.Data.Database'

# - task: SqlAzureDacpacDeployment@1
#   displayName: 'Deploy Database Schema'
#   inputs:
#     azureSubscription: 'Subscription1(12dd6ea4-12f2-4024-8cf5-550d4f87a5d9)'
#     AuthenticationType: 'connectionString'
#     ConnectionString: 'Server=tcp:fluentbluesqlsrv.database.windows.net,1433;Initial Catalog=FluentBlue;Persist Security Info=False;User ID=fluentBlueSqlAdmin;Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;'
#     deployType: 'DacpacTask'
#     DeploymentAction: 'Publish'
#     DacpacFile: '$(Build.ArtifactStagingDirectory)\database\FluentBlue.Data.Database.dacpac'
#     DatabaseName: 'FluentBlue'
#     IpDetectionMethod: 'AutoDetect'
#     AdditionalArguments: '/p:DropObjectsNotInSource=True /p:BlockOnPossibleDataLoss=False'

# Deploy WebAPI to Azure
- task: AzureRmWebAppDeployment@4
  displayName: 'Deploy WebAPI to Azure'
  inputs:
    ConnectionType: 'AzureRM'
    azureSubscription: 'Subscription1(12dd6ea4-12f2-4024-8cf5-550d4f87a5d9)'
    appType: 'webApp'
    WebAppName: 'FluentBlueWebApi-AS'
    packageForLinux: '$(Build.ArtifactStagingDirectory)/FluentBlue.WebApi.Service.zip'

# Deploy Web UI to Azure
- task: AzureRmWebAppDeployment@4
  displayName: 'Deploy Web UI to Azure'
  inputs:
    ConnectionType: 'AzureRM'
    azureSubscription: 'Subscription1(12dd6ea4-12f2-4024-8cf5-550d4f87a5d9)'
    appType: 'webApp'
    WebAppName: 'FluentBlueWeb-WA'
    packageForLinux: '$(Build.ArtifactStagingDirectory)/FluentBlue.UI.Web.zip'

# Print download links
- script: |
    echo "Installation packages can be downloaded from:"
    echo "Android: $(System.TeamFoundationCollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/artifacts?artifactName=FluentBlue.Android&api-version=6.0"
    echo "iOS: $(System.TeamFoundationCollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/artifacts?artifactName=FluentBlue.iOS&api-version=6.0"
    echo "Windows: $(System.TeamFoundationCollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/artifacts?artifactName=FluentBlue.Windows&api-version=6.0"
  displayName: 'Print Download Links'