using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.WebApi.Service.Controllers;
using FluentBlue.WebApi.Service.Hubs;
using FluentBlue.WebApi.Service.Services;
using FluentBlue.WebApi.Shared.Request;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using Serilog.Exceptions;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json.Serialization;

namespace FluentBlue.WebApi.Service
{

    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Get the assembly version
            string assemblyVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration) // Read base config from appsettings.json
                .Enrich.WithExceptionDetails() // Add detailed exception information
                .Enrich.WithProperty("Application", "FluentBlue.WebApi.Service")
                //.Enrich.WithProperty("Version", assemblyVersion)
                .Enrich.WithMachineName() // Add server name
                .Enrich.WithEnvironmentName() // Add environment (Development/Production)
                .WriteTo.Console() // Console sink for local debugging
                .WriteTo.Sentry(o =>
                {
                    o.Dsn = "https://<EMAIL>/4509101428572240";
                    o.MinimumEventLevel = LogEventLevel.Error;
                    o.AttachStacktrace = true;
                    o.Debug = builder.Environment.IsDevelopment();
                    o.Environment = builder.Environment.IsDevelopment() ? "Development" : "Production";
                    o.Release = assemblyVersion;
#if Debug
                    o.TracesSampleRate = 1.0f; // Adjust the sample rate as needed
#else
                    o.TracesSampleRate = 0.1f; // Adjust the sample rate as needed
#endif
                })
                .CreateLogger();

            // Replace default logging with Serilog
            builder.Host.UseSerilog();

            //builder.Services.AddLogging();
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("MyOrigins",
                    policy =>
                    {
                        policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
                    });
            });


            //Logging
            //builder.Host.UseSerilog((context, configuration) => configuration.ReadFrom.Configuration(context.Configuration));

            #region  Depedency Injection
            //AutoMapper
            var autoMapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Contacts.ContactCategoryMapping, FluentBlue.Data.Model.DTOs.ContactCategoryView>()
                    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ContactCategory != null ? src.ContactCategory.Name : ""))
                    .ForMember(dest => dest.Color, opt => opt.MapFrom(src => src.ContactCategory != null ? src.ContactCategory.Color : ""));
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Contacts.Contact, FluentBlue.Data.Model.DTOs.ContactView>()
                    .ForMember(dest => dest.ContactCategoryViews, opt => opt.MapFrom(src => src.ContactCategoryMappings));
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Tenants.Role, FluentBlue.Data.Model.DTOs.RoleLI>();
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Tenants.User, FluentBlue.Data.Model.DTOs.UserLI>();
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Contacts.Contact, FluentBlue.Data.Model.DTOs.ContactLI>();
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Tenants.User, FluentBlue.Data.Model.DTOs.UserView>()
                    .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role!.Name));
                cfg.CreateMap<RequestDataParameters, ReadPagedDataParameters>();
                cfg.CreateMap<RequestPagedContactsParameters, ReadPagedContactsParameters>();
                cfg.CreateMap<FluentBlue.WebApi.Shared.Request.RequestEventsParameters, FluentBlue.Application.Business.Request.RequestEventsParameters>();
            });
            AutoMapper.Mapper mapper = new Mapper(autoMapperConfig);
            builder.Services.AddSingleton<IMapper>(mapper);

            //Other services
            builder.Services.AddScoped<IWebApiCallerInfo, WebApiCallerInfo>();
            builder.Services.AddScoped<ITenantsBusiness>(sp => new FluentBlue.Application.Business.TenantsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<TenantsBusiness>>()));
            builder.Services.AddScoped<IContactsBusiness>(sp => new FluentBlue.Application.Business.ContactsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<ContactsBusiness>>()));
            builder.Services.AddScoped<IRolesBusiness>(sp => new FluentBlue.Application.Business.RolesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<UsersBusiness>>()));
            builder.Services.AddScoped<IUsersBusiness>(sp => new FluentBlue.Application.Business.UsersBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<UsersBusiness>>()));
            builder.Services.AddScoped<IEventsBusiness>(sp => new FluentBlue.Application.Business.EventsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<EventsBusiness>>()));
            builder.Services.AddScoped<ISettingsBusiness>(sp => new FluentBlue.Application.Business.SettingsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<SettingsBusiness>>()));
            builder.Services.AddScoped<IEventCategoriesBusiness>(sp => new FluentBlue.Application.Business.EventCategoriesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<EventCategoriesBusiness>>()));
            builder.Services.AddScoped<IEventStatesBusiness>(sp => new FluentBlue.Application.Business.EventStatesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<EventStatesBusiness>>()));
            builder.Services.AddScoped<IContactCategoriesBusiness>(sp => new FluentBlue.Application.Business.ContactCategoriesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<ContactCategoriesBusiness>>()));
            //add to services FluentBlueDbContext as scoped service
            //builder.Services.AddDbContext<FluentBlue.Data.Model.FluentBlueDbContext>(options => options.UseSqlServer(builder.Configuration["FluentBlueConnectionString"]));
            builder.Services.AddScoped<FluentBlue.Data.Model.FluentBlueDbContext>(sp => new FluentBlue.Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]));

            #endregion

            //Json serialization
            builder.Services.AddControllers().AddJsonOptions(x => x.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles);  //Η εντολή αυτή προστέθηκε για να φτιάξει το de-serialization και να μην εμφανίζει το σφάλμα " A possible object cycle was detected."

            builder.Services.AddSignalR(); // Add SignalR services

            //Swagger
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "FluentBlue.WebApi", Version = "v1" });

                //To Enable authorization using Swagger (JWT)
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer 12345abcdef\"",
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[] {}
                    }
                });
            });

            //JWT Authentication
            builder.Services.AddAuthentication(option =>
            {
                option.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                option.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;

            }).AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["jwt:key"]!))
                };
            });

            //WebAPI versioning
            builder.Services.AddApiVersioning(config =>
            {
                config.ReportApiVersions = true;
                config.AssumeDefaultVersionWhenUnspecified = true;
                config.DefaultApiVersion = new ApiVersion(1, 0);
            });

            builder.Services.AddHostedService<EventReminderService>();

            builder.Services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
            });

            var app = builder.Build();

            //Configuration
            builder.Configuration.SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: false, true).AddJsonFile($"appsettings.{app.Environment.EnvironmentName}.json", optional: true, true);

            //Cors
            app.UseCors(cors => cors
                 .AllowAnyMethod()
                 .AllowAnyHeader()
                 .SetIsOriginAllowed(origin => true)
                 .AllowCredentials()
            );

            // Configure the HTTP request pipeline
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            // Configure exception handling - MUST be before other middleware to ensure exceptions are properly handled
            app.UseExceptionHandler(errorApp =>
            {
                errorApp.Run(async context =>
                {
                    var exceptionHandlerPathFeature = context.Features.Get<Microsoft.AspNetCore.Diagnostics.IExceptionHandlerPathFeature>();
                    var exception = exceptionHandlerPathFeature?.Error;

                    if (exception != null)
                    {
                        Log.Error(exception, "Unhandled exception");
                    }

                    // Return appropriate error response
                    context.Response.StatusCode = 500;
                    await context.Response.WriteAsJsonAsync(new { error = "An error occurred processing your request" });
                });
            });

            app.UseSerilogRequestLogging();
            app.UseAuthorization();
            app.UseMiddleware<WebApiCallerMiddleware>();  //Προσθέτουμε το middleware που διαβάζει το Tenant
            app.UseResponseCompression();

            app.MapControllers();

            app.MapHub<NotificationHub>("/notificationHub"); // Map the hub to a URL

            app.Logger.LogInformation("Environment name:" + app.Environment.EnvironmentName);
            //app.Logger.LogInformation(app.Configuration.GetValue<string>("FluentBlueConnectionString")!.ToString());

            try
            {
                Log.Information("Starting FluentBlue.WebApi.Service");
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "FluentBlue.WebApi.Service terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}
