﻿@inherits LayoutComponentBase

@using Blazored.LocalStorage
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop




@inject HttpClient httpClient
@inject ILogger<ThemeLayout> logger
@inject ILogger<FluentBlue.WebApi.Client.SettingsWebApiClient> userSettingsWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject IDialogService dialogService
@inject IJSRuntime JS
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject ILocalStorageService LocalStorage

<FluentDesignTheme @ref="fluentDesignTheme" @bind-Mode="@currentDesignThemeMode" CustomColor="@currentThemeColorText" StorageName="Theme" />

@Body