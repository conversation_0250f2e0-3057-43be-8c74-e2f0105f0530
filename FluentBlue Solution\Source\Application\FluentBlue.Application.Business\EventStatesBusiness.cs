﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.Application.Business
{
    public class EventStatesBusiness : IEventStatesBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public EventStatesBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<EventStatesBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.EventState>> GetEventStates(Guid tenantId)
        {
            try
            {
                //Validation

                //Query

                IQueryable<FluentBlue.Data.Model.DBOs.Calendar.EventState> query = this.dbContext.EventStates.AsQueryable();

                query = query.AsNoTracking().Where(x => x.TenantId == tenantId).OrderBy(x => x.SortIndex);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Calendar.EventState> eventStates = await query.AsNoTracking().ToListAsync();

                //Response
                return eventStates;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Calendar.EventState?> GetEventState(Guid eventStateId)
        {
            try
            {
                //Query
                IQueryable<EventState> query = dbContext.EventStates.AsQueryable();
                query = query.Where(c => c.EventStateId.ToString() == eventStateId.ToString());

                return await query.AsNoTracking().FirstOrDefaultAsync<EventState>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "EventStateId=" + eventStateId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateEventState(EventState eventStateObj)
        {
            try
            {
                #region Validation
                if (eventStateObj == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                List<ValidationResult> validationResults = new List<ValidationResult>();
                ValidationContext validationContext = new ValidationContext(eventStateObj);
                if (Validator.TryValidateObject(eventStateObj, validationContext, validationResults, true) == false)
                {
                    string validationErrors = string.Empty;
                    foreach (CompositeValidationResult compValidationResult in validationResults)
                    {
                        foreach (ValidationResult validationResult in compValidationResult.Results)
                        {
                            validationErrors += validationResult.ErrorMessage + ". ";
                        }
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Checks in database if Title alread exists
                FluentBlue.Data.Model.DBOs.Calendar.EventState? eventState = await this.dbContext.EventStates.Where(x => x.Name == eventStateObj.Name && x.EventStateId != eventStateObj.EventStateId).FirstOrDefaultAsync();
                if (eventState != null)
                {
                    throw new ApplicationException(Resources.EventStatesResource.TitleAlreadyExists);
                }

                // In case we are deleting the EventState
                if (eventStateObj.ObjectState == ObjectState.Deleted)
                {
                    // Checks in database if this EventState is used in any Event
                    FluentBlue.Data.Model.DBOs.Calendar.Event? evt = await this.dbContext.Events.Where(x => x.EventStateId == eventStateObj.EventStateId).FirstOrDefaultAsync();
                    if (evt != null)
                    {
                        throw new ApplicationException(Resources.EventStatesResource.CannotDeleteAlreadyUsedState);
                    }
                }
                #endregion

                //Query
                this.dbContext.Attach(eventStateObj);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { eventStateObj.EventStateId });
                throw;
            }
        }

        public async Task DeleteEventState(Guid eventStateId)
        {
            try
            {
                EventState? eventCagegoryObj = await this.dbContext.EventStates.Where(x => x.EventStateId == eventStateId).FirstAsync();
                if (eventCagegoryObj != null)
                {
                    // Checks in database if this EventState is used in any Event
                    FluentBlue.Data.Model.DBOs.Calendar.Event? evt = await this.dbContext.Events.Where(x => x.EventStateId == eventStateId).FirstOrDefaultAsync();
                    if (evt != null)
                    {
                        throw new ApplicationException(Resources.EventStatesResource.CannotDeleteAlreadyUsedState);
                    }

                    eventCagegoryObj.ObjectState = ObjectState.Deleted;
                    this.dbContext.Attach(eventCagegoryObj);
                    this.dbContext.SaveChanges();
                }
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { "EventStateId=" + eventStateId.ToString() });
                throw;
            }
        }
    }
}
