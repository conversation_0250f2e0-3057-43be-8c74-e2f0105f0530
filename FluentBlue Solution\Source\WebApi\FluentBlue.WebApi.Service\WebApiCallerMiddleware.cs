﻿using Microsoft.Extensions.Primitives;
using System.Security.Claims;
using System.Text.Json;

namespace FluentBlue.WebApi.Service
{
    internal class WebApiCallerMiddleware
    {
        private readonly RequestDelegate _next;
        public WebApiCallerMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        // Get Tenant Id from incoming requests 
        public async Task InvokeAsync(HttpContext context, IWebApiCallerInfo currentTenantService)
        {
            context.Request.Headers.TryGetValue("Authorization", out StringValues strValues); // Tenant Id from incoming request header
            context.Request.Headers.TryGetValue("ApplicationId", out StringValues applicationId); // The ApplicationId from incoming request header
            
            if (strValues.Count > 0)
            {
                string token = strValues[0]!.Replace("bearer", "").Trim();
                FluentBlue.Shared.Authorization.JwtHelper.ParseClaimsFromJwt(token, out Dictionary<string, Claim> claims);
                string tenantId = claims["TenantId"].Value;
                string userId = claims["UserId"].Value;
                await currentTenantService.ValidateCallerInfo(Guid.Parse(tenantId), Guid.Parse(userId));
            }

            if (applicationId.Count > 0)
            {
                if (applicationId[0] != null && applicationId[0] != string.Empty)
                {
                    currentTenantService.ApplicationId = Guid.Parse(applicationId[0]!.Trim());
                }
            }

            await _next(context);
        }
    }
}
