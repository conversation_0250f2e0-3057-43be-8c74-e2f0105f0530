﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using NodaTime.TimeZones;
using System;
using System.Globalization;

namespace FluentBlue.Data.Model.DBOs.Validators
{


    public class UserSettingValidator : AbstractValidator<UserSetting>
    {
        public UserSettingValidator()
        {
            RuleFor(x => x.UserSettingId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.UserSettingId));
            RuleFor(x => x.UserId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.UserId));
            RuleFor(x => x.TimeZone).NotEmpty().MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.FieldRequired);
            RuleFor(x => x.TimeZone).MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            RuleFor(x => x.TimeZone).Must(x => TzdbDateTimeZoneSource.Default.ZoneLocations!.Select(x => x.ZoneId).Contains(x)).WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameIsInvalid, nameof(UserSetting.TimeZone));
            RuleFor(x => x.Language).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.Language));
            //RuleFor(x => x.Culture).Must(x => CultureInfo.GetCultures(CultureTypes.SpecificCultures).Select(x => x.Name).Contains(x)).WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameIsInvalid, nameof(UserSetting.Culture));
            RuleFor(x => x.DateFormat).MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            RuleFor(x => x.TimeFormat).MaximumLength(50).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            RuleFor(x => x.ThemeColor).NotNull().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.ThemeColor));
            RuleFor(x => x.FirstDayOfWeek).NotNull().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.FirstDayOfWeek));
            RuleFor(x => x.ThemeColorMode).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.ThemeColorMode));
            RuleFor(x => x.CalendarWorkDays).NotNull().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.CalendarWorkDays));
            RuleFor(x => x.CalendarWorkStart).NotEqual(TimeSpan.Zero).WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.CalendarWorkStart));
            RuleFor(x => x.CalendarWorkEnd).NotEqual(TimeSpan.Zero).WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.CalendarWorkEnd));

            RuleFor(x => x.CalendarWorkStartDateTime).NotNull().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.CalendarWorkStartDateTime));
            RuleFor(x => x.CalendarWorkEndDateTime).NotNull().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(UserSetting.CalendarWorkEndDateTime));
        }

        protected override bool PreValidate(ValidationContext<UserSetting> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }
    }
}
