﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{
    public class EventStateValidator : AbstractValidator<EventState>
    {
        public EventStateValidator()
        {
            RuleFor(x => x.EventStateId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.EventStateId));
            RuleFor(x => x.TenantId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.TenantId));
            RuleFor(x => x.Name).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.Name));
            RuleFor(x => x.Name).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength50);
            //RuleFor(x => x.BackColor).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.BackColor));
            RuleFor(x => x.BackColor).NotEqual("#000000").WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameIsInvalid, nameof(EventState.BackColor));
            RuleFor(x => x.BackColor).MaximumLength(100).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength10);
          

            RuleFor(x => x.DateCreatedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.DateModifiedLocal));
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.DateModifiedLocal));
            RuleFor(x => x.DateCreatedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.DateModifiedUtc));
            RuleFor(x => x.DateModifiedUtc).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(EventState.DateModifiedUtc));
        }

        protected override bool PreValidate(ValidationContext<EventState> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }

    }
}
