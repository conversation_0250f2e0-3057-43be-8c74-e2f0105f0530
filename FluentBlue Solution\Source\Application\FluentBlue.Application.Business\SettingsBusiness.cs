﻿using AutoMapper;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business
{
    public class SettingsBusiness : ISettingsBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        private IMapper mapper;
        private ILogger logger;

        public SettingsBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<SettingsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<UserSetting?> GetUserSettings(Guid userId)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Settings.UserSetting> query = this.dbContext.UserSettings.AsQueryable();
                query = query.Where(x => x.UserId == userId);
                Data.Model.DBOs.Settings.UserSetting? userSettings = await query.FirstOrDefaultAsync();

                //Result
                return userSettings;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateOrUpdateUserSettings(UserSetting userSetting)
        {
            try
            {
                //Validation
                if (userSetting == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                List<ValidationResult> validationResults = new List<ValidationResult>();
                ValidationContext validationContext = new ValidationContext(userSetting);
                if (Validator.TryValidateObject(userSetting, validationContext, validationResults, true) == false)
                {
                    string validationErrors = string.Empty;
                    foreach (CompositeValidationResult compValidationResult in validationResults)
                    {
                        foreach (ValidationResult validationResult in compValidationResult.Results)
                        {
                            validationErrors += validationResult.ErrorMessage + ". ";
                        }
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Query
                this.dbContext.Attach(userSetting);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { userSetting.UserId });
                throw;
            }
        }

    }
}
