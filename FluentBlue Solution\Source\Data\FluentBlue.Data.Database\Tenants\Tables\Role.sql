﻿CREATE TABLE [Tenants].[Role] (
    [RoleId]                     UNIQUEIDENTIFIER CONSTRAINT [DF_Role_RoleId1] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]                   UNIQUEIDENTIFIER NOT NULL,
    [Name]                       NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Role_Name] DEFAULT (N'') NOT NULL,
    [CanAdministerUsers]         BIT              CONSTRAINT [DF_Role_CanAdministerUsers] DEFAULT ((0)) NOT NULL,
    [CanAdministerRoles]         BIT              CONSTRAINT [DF_Role_CanAdministerUsers1] DEFAULT ((0)) NOT NULL,
    [CanViewOthersAppointments]  BIT              CONSTRAINT [DF_Role_CanViewOthersAppointments] DEFAULT ((1)) NOT NULL,
    [CanEditApplicationSettings] BIT              CONSTRAINT [DF_Role_CanViewOthersAppointments1] DEFAULT ((1)) NOT NULL,
    [DateCreatedUtc]             DATETIME         CONSTRAINT [DF_Role_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc]            DATETIME2 (7)    CONSTRAINT [DF_Role_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]                 ROWVERSION       NULL,
    CONSTRAINT [PK_Role] PRIMARY KEY CLUSTERED ([RoleId] ASC),
    CONSTRAINT [FK_Role_Tenant] FOREIGN KEY ([TenantId]) REFERENCES [Tenants].[Tenant] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);












GO



GO
CREATE NONCLUSTERED INDEX [UniqueRole]
    ON [Tenants].[Role]([TenantId] ASC, [Name] ASC);

