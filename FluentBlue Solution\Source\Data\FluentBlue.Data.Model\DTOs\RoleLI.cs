﻿using FluentBlue.Data.Model.DBOs.Calendar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DTOs
{
    public class RoleLI
    {
        public RoleLI()
        {
            
        }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = "RoleId")]
        public Guid? RoleId { get; set; }

        //public Guid TenantId { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.RoleDisplayResource), Name = "Name")]
        public string Name { get; set; } = string.Empty;

        public static RoleLI Empty
        {
            get
            {
                return new RoleLI() { Name = "", RoleId = Guid.Empty };
            }
        }
    }
}
