﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ContactDialogResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ContactDialogResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.ContactDialogResource", typeof(ContactDialogResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Addresses.
        /// </summary>
        public static string Addresses {
            get {
                return ResourceManager.GetString("Addresses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call .
        /// </summary>
        public static string Call {
            get {
                return ResourceManager.GetString("Call", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string CancelBtn_Text {
            get {
                return ResourceManager.GetString("CancelBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category.
        /// </summary>
        public static string ContactCategory {
            get {
                return ResourceManager.GetString("ContactCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string DeleteBtn_Text {
            get {
                return ResourceManager.GetString("DeleteBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emails.
        /// </summary>
        public static string Emails {
            get {
                return ResourceManager.GetString("Emails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events.
        /// </summary>
        public static string EventsTab {
            get {
                return ResourceManager.GetString("EventsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General.
        /// </summary>
        public static string GeneralTab {
            get {
                return ResourceManager.GetString("GeneralTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum number of categories selected.
        ///.
        /// </summary>
        public static string MaximumCategoriesSelected {
            get {
                return ResourceManager.GetString("MaximumCategoriesSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Name.
        /// </summary>
        public static string MiddleName {
            get {
                return ResourceManager.GetString("MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Navigate.
        /// </summary>
        public static string NavigateToAddress {
            get {
                return ResourceManager.GetString("NavigateToAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No addresses.
        /// </summary>
        public static string NoAddresses {
            get {
                return ResourceManager.GetString("NoAddresses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No categories found.
        /// </summary>
        public static string NoCategoriesFound {
            get {
                return ResourceManager.GetString("NoCategoriesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No emails.
        /// </summary>
        public static string NoEmails {
            get {
                return ResourceManager.GetString("NoEmails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No phones.
        /// </summary>
        public static string NoPhones {
            get {
                return ResourceManager.GetString("NoPhones", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Occupation.
        /// </summary>
        public static string Occupation {
            get {
                return ResourceManager.GetString("Occupation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phones.
        /// </summary>
        public static string Phones {
            get {
                return ResourceManager.GetString("Phones", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string SaveBtn_Text {
            get {
                return ResourceManager.GetString("SaveBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send.
        /// </summary>
        public static string SendEmail {
            get {
                return ResourceManager.GetString("SendEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSN.
        /// </summary>
        public static string SSN {
            get {
                return ResourceManager.GetString("SSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TIN.
        /// </summary>
        public static string TIN {
            get {
                return ResourceManager.GetString("TIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
    }
}
