﻿CREATE TABLE [Contacts].[ContactCategory] (
    [ContactCategoryId] UNIQUEIDENTIFIER CONSTRAINT [DF_Table_1_EventCategoryId] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]          UNIQUEIDENTIFIER NOT NULL,
    [Name]              NVARCHAR (50)    CONSTRAINT [DF_Table_1_Title] DEFAULT ('') NOT NULL,
    [Color]             NVARCHAR (10)    CONSTRAINT [DF_ContactCategory_BackColor] DEFAULT ('') NOT NULL,
    [SortIndex]         INT              NULL,
    [Disabled]          BIT              CONSTRAINT [DF_ContactCategory_Disabled] DEFAULT ((0)) NOT NULL,
    [DateCreatedUtc]    DATETIME         CONSTRAINT [DF_ContactCategory_DateCreatedUtc] DEFAULT (getutcdate()) NOT NULL,
    [DateModifiedUtc]   DATETIME         CONSTRAINT [DF_ContactCategory_DateModifiedUtc] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]        ROWVERSION       NULL,
    CONSTRAINT [PK_ContactCategory] PRIMARY KEY CLUSTERED ([ContactCategoryId] ASC)
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueName]
    ON [Contacts].[ContactCategory]([TenantId] ASC, [Name] ASC);


GO
CREATE NONCLUSTERED INDEX [TenantIdIndex]
    ON [Contacts].[ContactCategory]([TenantId] ASC);

