//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources {
    using System;
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class PhoneTypeResource_el {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PhoneTypeResource_el() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.PhoneTypeResource.el", typeof(PhoneTypeResource_el).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κινητό.
        /// </summary>
        public static string CellPhoneType {
            get {
                return ResourceManager.GetString("CellPhoneType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σπίτι.
        /// </summary>
        public static string HomePhoneType {
            get {
                return ResourceManager.GetString("HomePhoneType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Άλλο.
        /// </summary>
        public static string OtherPhoneType {
            get {
                return ResourceManager.GetString("OtherPhoneType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εργασία.
        /// </summary>
        public static string WorkPhoneType {
            get {
                return ResourceManager.GetString("WorkPhoneType", resourceCulture);
            }
        }
    }
} 