﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{


    public class ContactAddressValidator : AbstractValidator<ContactAddress>
    {
        public ContactAddressValidator()
        {
            RuleFor(x => x.ContactId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactAddress.ContactId));
            RuleFor(x => x.ContactAddressId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactAddress.ContactAddressId));
            RuleFor(x => x.Address).MaximumLength(250).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength250);
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactAddress.DateModifiedLocal));
        }

        protected override bool PreValidate(ValidationContext<ContactAddress> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }
    }
}
