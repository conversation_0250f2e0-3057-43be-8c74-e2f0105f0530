﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	  <DebugType>portable</DebugType>
	  <DebugSymbols>true</DebugSymbols>
	  <GenerateMapFile>true</GenerateMapFile>
	  <IsAotCompatible>False</IsAotCompatible>
	  <IsTrimmable>False</IsTrimmable>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<IsAotCompatible>True</IsAotCompatible>
		<IsTrimmable>True</IsTrimmable>
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
		<Optimize>true</Optimize>
		<DefineConstants>TRACE;RELEASE</DefineConstants>
		<PublishTrimmed>true</PublishTrimmed>
		<TrimMode>full</TrimMode>
		<OptimizationLevel>3</OptimizationLevel>
		<EnableSingleFileAnalysis>true</EnableSingleFileAnalysis>
		<EnableTrimAnalyzer>true</EnableTrimAnalyzer>
		<SuppressTrimAnalysisWarnings>false</SuppressTrimAnalysisWarnings>
		<EnableAotAnalyzer>true</EnableAotAnalyzer>
		<EnableCacheAnalysis>true</EnableCacheAnalysis>
		
		<JsonSerializerIsReflectionEnabledByDefault>false</JsonSerializerIsReflectionEnabledByDefault>
	</PropertyGroup>


	<ItemGroup>
		<SupportedPlatform Include="browser" />
		<TrimmerRootAssembly Include="Microsoft.FluentUI.AspNetCore.Components" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AKSoftware.Blazor.Utilities" Version="1.1.0" />
		<PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
		<PackageReference Include="ClosedXML" Version="0.105.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.DataAnnotations.Validation" Version="3.2.0-rc1.20223.4" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.5" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.5.0" />
		<PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.5" />
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.9" />
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.9" />
		<PackageReference Include="NodaTime" Version="3.2.2" />
		<PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="29.2.4" />
		<PackageReference Include="Syncfusion.Blazor.Schedule" Version="29.2.4" />
		<PackageReference Include="Syncfusion.Blazor.Themes" Version="29.2.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\WebApi\FluentBlue.WebApi.Client\FluentBlue.WebApi.Client.csproj" />
		<ProjectReference Include="..\..\WebApi\FluentBlue.WebApi.Shared\FluentBlue.WebApi.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Components\Resources\ContactCategoriesDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>ContactCategoriesDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\ContactDialogResource.Designer.cs">
		  <DependentUpon>ContactDialogResource.resx</DependentUpon>
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		</Compile>
		<Compile Update="Components\Resources\EventCategoriesDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>EventCategoriesDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\EventDialogResource.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>EventDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\EventStatesDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>EventStatesDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\RecurrenceDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>RecurrenceDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\RoleDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>RoleDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\SetPasswordDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>SetPasswordDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\RecurrentEventHandlingDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>RecurrentEventHandlingDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Components\Resources\UserDialogResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>UserDialogResource.resx</DependentUpon>
		</Compile>
		<Compile Update="GlobalResource.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>GlobalResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Layout\Resources\MainlLayoutResource.el.Designer.cs">
		  <DependentUpon>MainlLayoutResource.el.resx</DependentUpon>
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		</Compile>
		<Compile Update="Layout\Resources\MainlLayoutResource.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>MainlLayoutResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Layout\Resources\NavMenuResource.el.Designer.cs">
		  <DependentUpon>NavMenuResource.el.resx</DependentUpon>
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		</Compile>
		<Compile Update="Layout\Resources\NavMenuResource.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>NavMenuResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Pages\Resources\CalendarResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>CalendarResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Pages\Resources\ContactsResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>ContactsResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Pages\Resources\HomeResource.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>HomeResource.resx</DependentUpon>
		</Compile>
		<Compile Update="Resources\SfResources.el.Designer.cs">
		  <DependentUpon>SfResources.el.resx</DependentUpon>
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Resources\SfResources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>SfResources.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
	  <Content Update="Components\EventStatesDialog.razor">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	  </Content>
	  <Content Update="wwwroot\Images\fluent-repeat-all-24-regular-white.png">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="wwwroot\Images\fluent-repeat-all-24-regular.png">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Components\Resources\ContactCategoriesDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\ContactCategoriesDialogResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>ContactCategoriesDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\ContactDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\ContactDialogResource.resx">
		  <LastGenOutput>ContactDialogResource.Designer.cs</LastGenOutput>
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\EventCategoriesDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\EventCategoriesDialogResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>EventCategoriesDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\EventDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\EventDialogResource.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>EventDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\EventStatesDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\EventStatesDialogResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>EventStatesDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\RecurrenceDialogResource.el.resx">
		  <Generator>ResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\RecurrenceDialogResource.resx">
		  <Generator>ResXFileCodeGenerator</Generator>
		  <LastGenOutput>RecurrenceDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\RoleDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\RoleDialogResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>RoleDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\SetPasswordDialogResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\SetPasswordDialogResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>SetPasswordDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\SettingsDialogResource.el.resx">
		  <Generator></Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\SettingsDialogResource.resx">
			<Generator></Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\RecurrentEventHandlingDialogResource.resx">
		  <Generator>ResXFileCodeGenerator</Generator>
		  <LastGenOutput>RecurrentEventHandlingDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Components\Resources\UserDialogResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>UserDialogResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="GlobalResource.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>GlobalResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Layout\Resources\MainlLayoutResource.el.resx">
		  <LastGenOutput>MainlLayoutResource.el.Designer.cs</LastGenOutput>
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Layout\Resources\MainlLayoutResource.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>MainlLayoutResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Layout\Resources\NavMenuResource.el.resx">
		  <LastGenOutput>NavMenuResource.el.Designer.cs</LastGenOutput>
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Layout\Resources\NavMenuResource.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>NavMenuResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Pages\Resources\CalendarResource.el.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Pages\Resources\CalendarResource.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>CalendarResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Pages\Resources\ContactsResource.resx">
		  <Generator>ResXFileCodeGenerator</Generator>
		  <LastGenOutput>ContactsResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Pages\Resources\HomeResource.resx">
		  <Generator>ResXFileCodeGenerator</Generator>
		  <LastGenOutput>HomeResource.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\SfResources.el.resx">
		  <LastGenOutput>SfResources.el.Designer.cs</LastGenOutput>
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\SfResources.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>SfResources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

</Project>
