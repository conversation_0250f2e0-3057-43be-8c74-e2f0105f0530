//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SettingsDialogResource_el {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SettingsDialogResource_el() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.SettingsDialogResource.el", typeof(SettingsDialogResource_el).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string UserEmail {
            get {
                return ResourceManager.GetString("UserEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο χρήστης δεν υπάρχει.
        /// </summary>
        public static string UserNotExists {
            get {
                return ResourceManager.GetString("UserNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Νέος Ρόλος.
        /// </summary>
        public static string NewRoleBtn_Text {
            get {
                return ResourceManager.GetString("NewRoleBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο ρόλος δεν υπάρχει.
        /// </summary>
        public static string RoleNotExists {
            get {
                return ResourceManager.GetString("RoleNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ρόλος.
        /// </summary>
        public static string RoleName {
            get {
                return ResourceManager.GetString("RoleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επεξεργασία Κατηγοριών Συμβάντων.
        /// </summary>
        public static string EventCategoriesBtn_Text {
            get {
                return ResourceManager.GetString("EventCategoriesBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κατηγορίες Συμβάντων.
        /// </summary>
        public static string EventCategoriesTitle {
            get {
                return ResourceManager.GetString("EventCategoriesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Καταστάσεις Συμβάντων.
        /// </summary>
        public static string EventStatesBtn_Text {
            get {
                return ResourceManager.GetString("EventStatesBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Καταστάσεις Συμβάντων.
        /// </summary>
        public static string EventStatesTitle {
            get {
                return ResourceManager.GetString("EventStatesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ρυθμίσεις.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
    }
} 