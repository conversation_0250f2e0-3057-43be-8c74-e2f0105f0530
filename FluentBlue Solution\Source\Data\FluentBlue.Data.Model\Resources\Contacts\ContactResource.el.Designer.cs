﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources.Contacts {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ContactResource_el1 {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ContactResource_el1() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.Contacts.ContactResource.el1", typeof(ContactResource_el1).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID Επαφής.
        /// </summary>
        public static string ContactId {
            get {
                return ResourceManager.GetString("ContactId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Όνομα.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πάρα πολλοί χαρακτήρες (όριο 50 χαρακτήρων).
        /// </summary>
        public static string FirstNameMaxLengthError {
            get {
                return ResourceManager.GetString("FirstNameMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ονοματεπώνυμο.
        /// </summary>
        public static string FullName {
            get {
                return ResourceManager.GetString("FullName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εικόνα.
        /// </summary>
        public static string Image {
            get {
                return ResourceManager.GetString("Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επώνυμο.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πάρα πολλοί χαρακτήρες (όριο 50 χαρακτήρων).
        /// </summary>
        public static string LastNameMaxLengthError {
            get {
                return ResourceManager.GetString("LastNameMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μεσαίο Όνομα.
        /// </summary>
        public static string MiddleName {
            get {
                return ResourceManager.GetString("MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σημειώσεις.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επάγγελμα.
        /// </summary>
        public static string Occupation {
            get {
                return ResourceManager.GetString("Occupation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τηλέφωνα.
        /// </summary>
        public static string PhoneNumbers {
            get {
                return ResourceManager.GetString("PhoneNumbers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αριθμός Κοινωνικής Ασφάλισης.
        /// </summary>
        public static string SSN {
            get {
                return ResourceManager.GetString("SSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Περίληψη.
        /// </summary>
        public static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ΑΦΜ.
        /// </summary>
        public static string TIN {
            get {
                return ResourceManager.GetString("TIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τύπος.
        /// </summary>
        public static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ΦΠΑ.
        /// </summary>
        public static string Vat {
            get {
                return ResourceManager.GetString("Vat", resourceCulture);
            }
        }
    }
}
