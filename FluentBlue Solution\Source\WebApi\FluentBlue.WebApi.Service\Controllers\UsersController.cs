﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Security.Claims;
using System.Text;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class UsersController : ControllerBase
    {
        private ILogger<UsersController> logger;
        private IUsersBusiness usersBusiness;
        private IRolesBusiness rolesBusiness;
        private IConfiguration configuration;
        private IWebApiCallerInfo webApiCallerInfo;
        private ISettingsBusiness settingsBusiness;
        private ILogger<SettingsController> settingsLogger;
        private IMapper mapper;

        public UsersController(IUsersBusiness usersBusiness, IRolesBusiness rolesBusiness, ILogger<UsersController> logger, IConfiguration configuration, IWebApiCallerInfo webApiCallerInfo, ISettingsBusiness settingsBusiness, ILogger<SettingsController> settingsLogger, IMapper mapper)
        {
            try
            {
                this.usersBusiness = usersBusiness;
                this.rolesBusiness = rolesBusiness;
                this.settingsBusiness = settingsBusiness;
                this.logger = logger;  //TODO: να δω αν θα χρησιμοποιείσω το ILogger γενικά στην εφαρμογή.
                this.configuration = configuration;
                this.webApiCallerInfo = webApiCallerInfo;
                this.settingsLogger = settingsLogger;
                this.mapper = mapper;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        /// <summary>
        /// Retrieves contacts in paged mode.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Users/Get")]
        public async Task<ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>>> GetUsers([FromBody] RequestDataParameters parameters)
        {
            try
            {
                //Map data
                ReadPagedDataParameters readPagedDataParameters = this.mapper.Map<ReadPagedDataParameters>(parameters);

                //Validation
                if (readPagedDataParameters.TenantId != this.webApiCallerInfo.TenantId)
                {
                    return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = "TenantId missmacth." };
                }

                //Query
                PagedData<List<FluentBlue.Data.Model.DTOs.UserView>> data = await this.usersBusiness.GetUsers(readPagedDataParameters);

                //Response
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, parameters);
                return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        /// <summary>
        /// Returns all the Users of Tenant. This list should be used for displaying in lists, dropdowns.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Users/GetList")]
        public async Task<ApiResponse<List<FluentBlue.Data.Model.DTOs.UserLI>>> GetUsersList()
        {
            try
            {
                //Validation

                //Query
                List<FluentBlue.Data.Model.DTOs.UserLI> data = await this.usersBusiness.GetUsersLI(this.webApiCallerInfo.TenantId!.Value);

                //Response
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.UserLI>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.UserLI>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, this.webApiCallerInfo.TenantId!.Value);
                return new ApiResponse<List<FluentBlue.Data.Model.DTOs.UserLI>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("User/Get")]
        public async Task<ApiResponse<User?>> GetUser([FromQuery] string userId)
        {
            try
            {
                //Query
                User? user = await this.usersBusiness.GetUser(Guid.Parse(userId));

                //Response
                return new ApiResponse<User?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = user };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<User?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "UserId=" + userId);
                return new ApiResponse<User?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpPost]
        [Route("User/CreateOrUpdate")]
        public async Task<ApiResponse<User>> CreateOrUpdateUser([FromBody] User user)
        {
            try
            {
                #region Validation
                if (user == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Ελέγχει αν υπάρχει ήδη στη βάση δεδομένων
                User? existingUser = await this.usersBusiness.CheckUserExists(this.webApiCallerInfo.TenantId!.Value, user.UserId, user.Email);
                if (existingUser != null)
                {
                    return new ApiResponse<User>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = FluentBlue.WebApi.Service.Resources.UsersResource.UserAlreadyExists };
                }
                #endregion

                //Query
                await this.usersBusiness.CreateOrUpdateUser(user);

                //Response
                User? conctact = await this.usersBusiness.GetUser(user.UserId);
                return new ApiResponse<User>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = user };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<User>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, user);
                return new ApiResponse<User>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("User/Delete")]
        public ApiResponse DeleteUser([FromQuery] Guid userId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                this.usersBusiness.DeleteUser(userId);

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "UserId=" + userId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("User/Settings/Get")]
        public async Task<ApiResponse<UserSetting?>> GetUserSettings([FromQuery] string userId)
        {
            try
            {
                //Query
                UserSetting? userSetting = await this.settingsBusiness.GetUserSettings(Guid.Parse(userId));
                //Thread.Sleep(14000);
                //Response
                return new ApiResponse<UserSetting?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = userSetting };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<UserSetting?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "UserId=" + userId);
                return new ApiResponse<UserSetting?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("User/CheckExists")]
        public async Task<ApiResponse<User?>> CheckUserExists([FromQuery] string userId, string email)
        {
            try
            {
                //Query
                User? result = await this.usersBusiness.CheckUserExists(this.webApiCallerInfo.TenantId!.Value, Guid.Parse(userId), email);

                //TODO: να δω αν θα προστεθεί έλεγχος ασφαλείας ότι το User ανοίκει στο Tenant που το ζητάει.

                //Response
                return new ApiResponse<User?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = result };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<User?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "UserId=" + userId + "eamil=" + email);
                return new ApiResponse<User?>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("User/Role/Get")]
        public async Task<ApiResponse<Role?>> GetRoleOfUser([FromQuery] string userId)
        {
            try
            {
                //Query
                Role? result = await this.rolesBusiness.GetRoleOfUser(this.webApiCallerInfo.TenantId!.Value, Guid.Parse(userId));

                //TODO: να δω αν θα προστεθεί έλεγχος ασφαλείας ότι το User ανοίκει στο Tenant που το ζητάει.

                //Response
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = result };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Role?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "UserId=" + userId);
                return new ApiResponse<Role?>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }   
        }


        //[HttpGet("Login")]
        //public async Task<ApiResponse<LoginResponse>> Login(string username, string password)
        //{
        //    //Query
        //    Data.Model.DBOs.Tenants.User? user = await this.usersBusiness.GetUser(username, password);

        //    //Αν ο User υπάρχει
        //    if (user != null)
        //    {
        //        UserToken token = BuildToken(user.UserId.ToString(), username, user.FullName, user.Role.Name.ToString(), user.TenantId.ToString());

        //        //TODO: Λείπει κώδικας αν η συνδρομή του Tenant έχει λήξει

        //        //Response
        //        LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.Ok, Token = token };
        //        return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
        //    }
        //    else  //Αν ο User δεν υπάρχει
        //    {
        //        //Response
        //        LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.InvalidUsernamePassword, Token = new UserToken() };
        //        return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
        //    }
        //}

        //[HttpGet("RenewToken")]
        //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        //public ActionResult<UserToken> Renew()
        //{
        //    UserToken userToken = BuildToken("9AD0538F-EAEA-4C4B-8FE7-B69CEDDCABD3", HttpContext.User.Identity.Name, "John", "Admin", "FD335871-9253-4364-9BEB-5D1238F1C4E5");

        //    return userToken;
        //}

        //private UserToken BuildToken(string userId, string username, string fullName, string roleName, string tenantId)
        //{
        //    var claims = new List<Claim>()
        //    {
        //        new Claim("UserId", userId),
        //        new Claim("Username", username),
        //        new Claim(ClaimTypes.Name, fullName),                
        //        new Claim(ClaimTypes.Role, roleName),
        //        new Claim("TenantId", tenantId)
        //    };

        //    //var identityUser = await _userManager.FindByEmailAsync(userinfo.Email);
        //    //var claimsDB = await _userManager.GetClaimsAsync(identityUser);
        //    //claims.AddRange(claimsDB);

        //    var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["jwt:key"]!));
        //    var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        //    var expiration = DateTime.UtcNow.AddYears(1);

        //    JwtSecurityToken token = new JwtSecurityToken(
        //       issuer: null,
        //       audience: null,
        //       claims: claims,
        //       expires: expiration,
        //       signingCredentials: creds);

        //    return new UserToken()
        //    {
        //        Token = new JwtSecurityTokenHandler().WriteToken(token),
        //        Expiration = expiration
        //    };
        //}

    }
}
