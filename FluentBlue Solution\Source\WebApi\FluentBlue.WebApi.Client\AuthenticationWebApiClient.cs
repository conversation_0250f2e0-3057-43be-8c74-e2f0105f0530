﻿using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared.Authorization;
using FluentBlue.WebApi.Shared.Response;
using Newtonsoft.Json;

namespace FluentBlue.WebApi.Client
{
    public class AuthenticationWebApiClient
    {
        private HttpClient httpClient;
        //private readonly IHttpService httpService;

        public AuthenticationWebApiClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
        }

        public async Task<LoginResponse> Login(string username, string password)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + @"Authentication/Login";

                requestUri += "?Username=" + username + "&Password=" + password;

                string responseString = await this.httpClient.GetStringAsync(requestUri);
                ApiResponse<LoginResponse>? response = JsonConvert.DeserializeObject<ApiResponse<LoginResponse>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                //this.logger.LogError(ex, "Error in GetContactsLI({@tenantId})", tenantId);
                throw;
            }
        }

        public async Task<UserToken> RenewToken()
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + "RenewToken";

                string responseString = await this.httpClient.GetStringAsync(requestUri);
                ApiResponse<UserToken>? response = JsonConvert.DeserializeObject<ApiResponse<UserToken>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                //this.logger.LogError(ex, "Error in GetContactsLI({@tenantId})", tenantId);
                throw;
            }
        }
    }
}
