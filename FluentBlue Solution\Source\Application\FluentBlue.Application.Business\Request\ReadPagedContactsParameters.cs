﻿using FluentBlue.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business.Request
{
    public class ReadPagedContactsParameters
    {
        public Guid TenantId { get; set; }  
        public string Filter { get; set; } = string.Empty;
        /// <summary>
        /// If not provided, all contact categories will be returned. Guid with "00000000-0000-0000-0000-000000000000" means no category.
        /// </summary>
        public List<Guid?>? ContactCategoryIds { get; set; } = new List<Guid?>();
        public int PageIndex { get; set; } = 0;
        public int PageSize { get; set; } = 10;
        public Dictionary<string, SortOrder> SortColumns { get; set; } = new Dictionary<string, SortOrder>();
    }
}
