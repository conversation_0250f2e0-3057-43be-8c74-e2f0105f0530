﻿using FluentBlue.Data.Model.DBOs.Contacts;

namespace FluentBlue.Application.Business
{
    public interface IContactCategoriesBusiness
    {
        Task<List<FluentBlue.Data.Model.DBOs.Contacts.ContactCategory>> GetContactCategories(Guid tenantId);
        Task<Data.Model.DBOs.Contacts.ContactCategory?> GetContactCategory(Guid eventCategoryId);
        Task CreateOrUpdateContactCategory(ContactCategory eventObj);
        Task DeleteContactCategory(Guid eventCategoryId);
    }
}