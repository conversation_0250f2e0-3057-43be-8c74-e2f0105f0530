<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FluentBlue.UI.Web</title>
    <base href="/" />
    <link href="_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.ico" />
    <link rel="stylesheet" href="_content/FluentBlue.UI.Main/css/app.css" />
    <link rel="stylesheet" href="_content/FluentBlue.UI.Main/css/tailwind.css" />

    <!--Syncfusion-->
    <link id="syncfusionTheme" href="_content/Syncfusion.Blazor.Themes/fluent2.css" rel="stylesheet" />
    <!--<link href="_content/Syncfusion.Blazor.Themes/fluent-dark.css" rel="stylesheet" />-->
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>

    <link href="FluentBlue.UI.Web.styles.css" rel="stylesheet" />
    <link href="manifest.webmanifest" rel="manifest" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />

    <script src="_content/FluentBlue.UI.Main/js/common.js" type="text/javascript"></script>
    <script src="_content/FluentBlue.UI.Main/js/Sortable.min.js" type="text/javascript"></script>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="." class="reload">Reload</a>
        <span class="dismiss">🗙</span>
    </div>

    <script src="_framework/blazor.webassembly.js"></script>
    <script>navigator.serviceWorker.register('service-worker.js');</script>

    <!-- Set the default theme -->
    <script src="_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js" type="text/javascript"></script>
    <loading-theme storage-name="Theme"></loading-theme>

    <!--Script for Syncfusion theme-->
    <script>
        function setTheme(theme) {
            document.getElementsByTagName('body')[0].style.display = 'none';
            let synclink = document.getElementById('syncfusionTheme');
            //synclink.href = '_content/Syncfusion.Blazor.Themes/' + theme + '.css';
            synclink.href = '_content/FluentBlue.UI.Main/css/' + theme + '.css';
            setTimeout(function () { document.getElementsByTagName('body')[0].style.display = 'block'; }, 300);
        }
    </script>
</body>

</html>
