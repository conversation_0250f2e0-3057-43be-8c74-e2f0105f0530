﻿CREATE TABLE [Calendar].[EventReminder] (
    [EventReminderId] UNIQUEIDENTIFIER CONSTRAINT [DF_Table_1_EventId] DEFAULT (newsequentialid()) NOT NULL,
    [EventId]         UNIQUEIDENTIFIER NOT NULL,
    [<PERSON>minder<PERSON><PERSON>et]  TINYINT          CONSTRAINT [DF_EventReminder_MinutesBefore] DEFAULT ((10)) NULL,
    [ReminderTimeUtc] DATETIME         NULL,
    [PushSent]        BIT              CONSTRAINT [DF_EventReminder_Dismissed] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_EventReminder] PRIMARY KEY CLUSTERED ([EventReminderId] ASC),
    CONSTRAINT [FK_EventReminder_Event] FOREIGN KEY ([EventId]) REFERENCES [Calendar].[Event] ([EventId]) ON DELETE CASCADE ON UPDATE CASCADE
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Identifies if the Push Notification is sent.', @level0type = N'SCHEMA', @level0name = N'Calendar', @level1type = N'TABLE', @level1name = N'EventReminder', @level2type = N'COLUMN', @level2name = N'PushSent';

