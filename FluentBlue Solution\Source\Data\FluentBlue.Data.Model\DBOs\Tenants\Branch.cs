﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DBOs.Tenants
{
    [Table("Branch", Schema = "Tenants")]
    public class Branch
    {
        [Key]
        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.BranchDisplayResource), Name = "BranchId")]
        public Guid BranchId { get; set; }

        public Guid CompanyId { get; set; }

        [Required]
        [ForeignKey("CompanyId")]
        public Company? Company { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.BranchDisplayResource), Name = "Name")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Name { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.BranchDisplayResource), Name = "AddressLine1")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string AddressLine1 { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.BranchDisplayResource), Name = "City")]
        [MaxLength(100)]
        [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string City { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.BranchDisplayResource), Name = "StateProvinceId")]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public int StateProvinceId { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.BranchDisplayResource), Name = "PostalCode")]
        [MaxLength(10)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string PostalCode { get; set; } = string.Empty;

        //[Display(ResourceType = typeof(Data.Model.DBOs.Tenants.Resources.BranchDisplayResource), Name = "SpatialLocation")]
        //public DbGeography SpatialLocation { get; set; }

        //[Display(ResourceType = typeof(Resources.TenantDisplayResource), Name = "Summary")]
        //public override string Summary
        //{
        //    get
        //    {
        //        return BrandName;
        //    }
        //}

        [Display(ResourceType = typeof(Data.Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; } = new byte[0];


        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;
    }
}
