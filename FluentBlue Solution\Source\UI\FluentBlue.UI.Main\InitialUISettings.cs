﻿using FluentBlue.Data.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main
{
    /// <summary>
    /// This class is used to store the initial UI settings of the application before the User logs in, and also when local storage does not contain any UserSetting from previous User.
    /// The data of this class is used to initialize the UserSetting object after the User logs in.
    /// </summary>
    public class InitialUISettings
    {
        public Language CurrentLanguage { get; set; }
        public string TimeZone { get; set; }
        public string DateFormat { get; set; }
        public string TimeFormat { get; set; }
    }
}
