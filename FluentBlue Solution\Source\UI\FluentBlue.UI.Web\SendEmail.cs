﻿using FluentBlue.Shared.Utilities;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{
    class SendEmail : ISendEmail
    {
        private readonly IJSRuntime _jsRuntime;

        public SendEmail(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public async Task OpenEmailForm(string to, string subject, string body)
        {
            var encodedSubject = Uri.EscapeDataString(subject);
            var encodedBody = Uri.EscapeDataString(body);
            var mailtoUrl = $"mailto:{to}?subject={encodedSubject}&body={encodedBody}";
            await _jsRuntime.InvokeVoidAsync("eval", $"window.location.href='{mailtoUrl}'");
        }
    }
}
