﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.Data.Model.DBOs.Validators
{


    public class ContactPhoneValidator : AbstractValidator<ContactPhone>
    {
        public ContactPhoneValidator()
        {
            RuleFor(x => x.ContactId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactPhone.ContactId));
            RuleFor(x => x.ContactPhoneId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactPhone.ContactPhoneId));
            RuleFor(x => x.PhoneNumber).MaximumLength(25).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength25);
            RuleFor(x => x.PhoneNumber).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactPhone.PhoneNumber));
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactPhone.DateModifiedLocal));
        }

        protected override bool PreValidate(ValidationContext<ContactPhone> context, FluentValidation.Results.ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }
    }
}
