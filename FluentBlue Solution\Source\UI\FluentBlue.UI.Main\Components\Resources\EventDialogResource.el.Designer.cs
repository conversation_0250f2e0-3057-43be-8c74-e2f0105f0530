//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EventDialogResource_el {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EventDialogResource_el() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.EventDialogResource.el", typeof(EventDialogResource_el).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ολοήμερο.
        /// </summary>
        public static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κλείσιμο.
        /// </summary>
        public static string CancelBtn_Text {
            get {
                return ResourceManager.GetString("CancelBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κατηγορία.
        /// </summary>
        public static string Category {
            get {
                return ResourceManager.GetString("Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επαφή.
        /// </summary>
        public static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Διαγραφή.
        /// </summary>
        public static string DeleteBtn_Text {
            get {
                return ResourceManager.GetString("DeleteBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Περιγραφή.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Απόρριψη.
        /// </summary>
        public static string Dismiss {
            get {
                return ResourceManager.GetString("Dismiss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Λήξη.
        /// </summary>
        public static string End {
            get {
                return ResourceManager.GetString("End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τοποθεσία.
        /// </summary>
        public static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επιλέχθηκε ο μέγιστος αριθμός χρηστών.
        /// </summary>
        public static string MaximumUsersSelected {
            get {
                return ResourceManager.GetString("MaximumUsersSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Νέα.
        /// </summary>
        public static string NewContactBtn_Text {
            get {
                return ResourceManager.GetString("NewContactBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν βρέθηκαν χρήστες.
        /// </summary>
        public static string NoUsersFound {
            get {
                return ResourceManager.GetString("NoUsersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αποθήκευση.
        /// </summary>
        public static string SaveBtn_Text {
            get {
                return ResourceManager.GetString("SaveBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Έναρξη.
        /// </summary>
        public static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κατάσταση.
        /// </summary>
        public static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Θέμα.
        /// </summary>
        public static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Συμβάν.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Χρήστης.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
    }
} 