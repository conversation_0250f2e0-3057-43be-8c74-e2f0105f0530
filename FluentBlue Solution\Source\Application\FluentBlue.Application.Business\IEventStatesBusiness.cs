﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;

namespace FluentBlue.Application.Business
{
    public interface IEventStatesBusiness
    {
        Task<List<FluentBlue.Data.Model.DBOs.Calendar.EventState>> GetEventStates(Guid tenantId);
        Task<Data.Model.DBOs.Calendar.EventState?> GetEventState(Guid eventStateId);
        Task CreateOrUpdateEventState(EventState eventObj);
        Task DeleteEventState(Guid eventStateId);
    }
}