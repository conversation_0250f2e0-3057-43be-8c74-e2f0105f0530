﻿CREATE TABLE [Tenants].[Company] (
    [CompanyId]       UNIQUEIDENTIFIER CONSTRAINT [DF_Company_CompanyId1] DEFAULT (newsequentialid()) NOT NULL,
    [TenantId]        UNIQUEIDENTIFIER NOT NULL,
    [Name]            NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Company_Name] DEFAULT (N'') NOT NULL,
    [BrandName]       NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Company_BrandName] DEFAULT (N'') NOT NULL,
    [Website]         NVARCHAR (100)   COLLATE SQL_Latin1_General_CP1_CI_AS CONSTRAINT [DF_Company_Website] DEFAULT (N'') NOT NULL,
    [DateModifiedUtc] DATETIME2 (7)    CONSTRAINT [DF_Company_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]      ROWVERSION       NULL,
    CONSTRAINT [PK_Company] PRIMARY KEY CLUSTERED ([CompanyId] ASC),
    CONSTRAINT [FK_Company_Tenant] FOREIGN KEY ([TenantId]) REFERENCES [Tenants].[Tenant] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);








GO
