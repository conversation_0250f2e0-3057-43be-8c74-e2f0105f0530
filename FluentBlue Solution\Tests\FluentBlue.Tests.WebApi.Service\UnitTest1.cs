//using FluentBlue.Data.Model.DBOs.Contacts;
//using FluentBlue.WebApi.Service.Controllers;
//using FluentBlue.WebApi.Service.Models;
//using Microsoft.AspNetCore.Mvc;
//using Moq;
//using Xunit;

//namespace FluentBlue.Tests.WebApi.Service
//{


//    public class ContactsControllerTests
//    {
//        private readonly Mock<IContactsService> _mockContactsService;
//        private readonly ContactsController _controller;

//        public ContactsControllerTests()
//        {
//            _mockContactsService = new Mock<IContactsService>();
//            _controller = new ContactsController(_mockContactsService.Object);
//        }

//        [Fact]
//        public async Task GetContacts_ReturnsOkResultWithContacts()
//        {
//            // Arrange
//            var expectedContacts = new[]
//            {
//                new Contact { Id = 1, Name = "<PERSON>" },
//                new Contact { Id = 2, Name = "<PERSON>" }
//            };

//            _mockContactsService.Setup(s => s.GetContacts()).ReturnsAsync(expectedContacts);

//            // Act
//            var result = await _controller.GetContacts();

//            // Assert
//            var okResult = Assert.IsType<OkObjectResult>(result);
//            var actualContacts = Assert.IsType<Contact[]>(okResult.Value);
//            Assert.Equal(expectedContacts, actualContacts);
//        }

//        [Fact]
//        public async Task GetContact_ReturnsOkResultWithContact()
//        {
//            // Arrange
//            var expectedContact = new Contact { ContactId = 1, Name = "John Doe" };
//            var contactId = 1;

//            _mockContactsService.Setup(s => s.GetContact(contactId)).ReturnsAsync(expectedContact);

//            // Act
//            var result = await _controller.GetContact(contactId);

//            // Assert
//            var okResult = Assert.IsType<OkObjectResult>(result);
//            var actualContact = Assert.IsType<Contact>(okResult.Value);
//            Assert.Equal(expectedContact, actualContact);
//        }

//        [Fact]
//        public async Task CreateContact_ReturnsCreatedResultWithContact()
//        {
//            // Arrange
//            var expectedContact = new Contact { Id = 1, Name = "John Doe" };
//            var contact = new CreateContactDto { Name = "John Doe" };

//            _mockContactsService.Setup(s => s.CreateContact(contact)).ReturnsAsync(expectedContact);

//            // Act
//            var result = await _controller.CreateContact(contact);

//            // Assert
//            var createdResult = Assert.IsType<CreatedAtActionResult>(result);
//            var actualContact = Assert.IsType<Contact>(createdResult.Value);
//            Assert.Equal(expectedContact, actualContact);
//            Assert.Equal("GetContact", createdResult.ActionName);
//            Assert.Equal(new { id = expectedContact.Id }, createdResult.RouteValues);
//        }
//    }
//}
