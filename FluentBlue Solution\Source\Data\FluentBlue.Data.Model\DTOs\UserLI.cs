﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DTOs
{
    public class UserLI
    {
        public UserLI()
        {
        }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.UserId))]
        public Guid? UserId { get; set; }

        public Guid RoleId { get; set; }

        public Guid TenantId { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.LastName))]
        public string LastName { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Tenants.UserDisplayResource), Name = nameof(Model.Resources.Tenants.UserDisplayResource.FirstName))]
        public string FirstName { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.FullName))]
        public string FullName { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Email))]
        public string Email { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Username))]
        public string Username { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.AvailableInEventUsers))]
        public bool AvailableInEventUsers { get; set; } = true;

        [NotMapped]
        public string Initials
        {
            get
            {
                try
                {
                    return (string.IsNullOrEmpty(FirstName) ? "" : FirstName.Substring(0, 1) ) + (string.IsNullOrEmpty(LastName) ? "" : LastName?.Substring(0, 1));
                }
                catch
                {
                    return "";
                }
            }
        }

        [NotMapped]
        public bool Expanded { get; set; }
     

        public static UserLI Empty
        {
            get
            {
                return new UserLI() { FullName = "", Email = "", Username = "", UserId = Guid.Empty, RoleId = Guid.Empty, TenantId = Guid.Empty };
            }
        }
    }
}
