﻿CREATE TABLE [Settings].[UserSetting] (
    [UserSettingId]                UNIQUEIDENTIFIER CONSTRAINT [DF_UserSetting_UserSettingId1] DEFAULT (newsequentialid()) NOT NULL,
    [UserId]                       UNIQUEIDENTIFIER CONSTRAINT [DF_GeneralSettings_UserId] DEFAULT (newid()) NOT NULL,
    [TimeZone]                     NVARCHAR (50)    CONSTRAINT [DF_GeneralSettings_TimeZone] DEFAULT ('') NOT NULL,
    [Language]                     TINYINT          CONSTRAINT [DF_GeneralSettings_Language] DEFAULT ((1)) NOT NULL,
    [Culture]                      NVARCHAR (10)    CONSTRAINT [DF_UserSetting_CultureInfo] DEFAULT ('') NOT NULL,
    [DateFormat]                   NVARCHAR (50)    CONSTRAINT [DF_UserSetting_DateFormat] DEFAULT ('') NOT NULL,
    [TimeFormat]                   NVARCHAR (50)    CONSTRAINT [DF_UserSetting_DateFormat1] DEFAULT ('') NOT NULL,
    [ThemeColorMode]               TINYINT          CONSTRAINT [DF_UserGeneralSetting_ColorMode] DEFAULT ('') NOT NULL,
    [ThemeColor]                   TINYINT          CONSTRAINT [DF_UserSetting_Color] DEFAULT ((0)) NOT NULL,
    [FullNameDisplay]              TINYINT          CONSTRAINT [DF_UserSetting_ContactsFullNameType] DEFAULT ((1)) NOT NULL,
    [ShowTinInContactLists]        BIT              CONSTRAINT [DF_UserSetting_ShowTinInContactsList] DEFAULT ((0)) NOT NULL,
    [ShowSsnInContactLists]        BIT              CONSTRAINT [DF_UserSetting_ShowSsnInContactsList] DEFAULT ((0)) NOT NULL,
    [ShowPhonesInContactLists]     BIT              CONSTRAINT [DF_UserSetting_ShowPhonesInContactsList] DEFAULT ((0)) NOT NULL,
    [FirstDayOfWeek]               INT              CONSTRAINT [DF_UserSetting_FirstDayOfWeek] DEFAULT ((1)) NOT NULL,
    [CalendarWorkDays]             NVARCHAR (10)    CONSTRAINT [DF_UserSetting_CalendarWorkDays] DEFAULT ('1,2,3,4,5') NOT NULL,
    [CalendarWorkStart]            TIME (7)         CONSTRAINT [DF_UserSetting_CalendarWorkStart] DEFAULT ('09:00:00') NOT NULL,
    [CalendarWorkEnd]              TIME (7)         CONSTRAINT [DF_UserSetting_CalendarWorkStart1] DEFAULT ('17:00:00') NOT NULL,
    [CalendarTimeScaleInterval]    TINYINT          CONSTRAINT [DF_UserSetting_CalendarTimeScaleInterval] DEFAULT ((60)) NOT NULL,
    [CalendarDefaultView]          TINYINT          CONSTRAINT [DF_UserSetting_CalendarDefaultView] DEFAULT ((2)) NOT NULL,
    [CalendarDefaultViewOnMobiles] TINYINT          CONSTRAINT [DF_UserSetting_CalendarDefaultViewOnMobiles] DEFAULT ((5)) NOT NULL,
    [CalendarScrollToTime]         TINYINT          CONSTRAINT [DF_UserSetting_CalendarScrollToTime] DEFAULT ((1)) NOT NULL,
    [DateModifiedUtc]              DATETIME2 (7)    CONSTRAINT [DF_UserGeneralSetting_DateModified] DEFAULT (getutcdate()) NOT NULL,
    [RowVersion]                   ROWVERSION       NULL,
    CONSTRAINT [PK_UserSetting] PRIMARY KEY CLUSTERED ([UserSettingId] ASC),
    CONSTRAINT [FK_UserSetting_User] FOREIGN KEY ([UserId]) REFERENCES [Tenants].[User] ([UserId]) ON DELETE CASCADE ON UPDATE CASCADE
);










GO



GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueUserId]
    ON [Settings].[UserSetting]([UserId] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Determines if field TIN will be visible in dropdown of Contacts.', @level0type = N'SCHEMA', @level0name = N'Settings', @level1type = N'TABLE', @level1name = N'UserSetting', @level2type = N'COLUMN', @level2name = N'ShowTinInContactLists';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Determines if field SSN will be displayed in dropdowns of Contacts.', @level0type = N'SCHEMA', @level0name = N'Settings', @level1type = N'TABLE', @level1name = N'UserSetting', @level2type = N'COLUMN', @level2name = N'ShowSsnInContactLists';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Determines is phones, mobiles will be displayed in dropdown of Contacts.', @level0type = N'SCHEMA', @level0name = N'Settings', @level1type = N'TABLE', @level1name = N'UserSetting', @level2type = N'COLUMN', @level2name = N'ShowPhonesInContactLists';

