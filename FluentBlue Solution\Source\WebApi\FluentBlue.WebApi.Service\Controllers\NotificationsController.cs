﻿using FluentBlue.WebApi.Service.Hubs;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;

namespace FluentBlue.WebApi.Service.Controllers
{

    //[ApiController]
    //[ApiVersion("1.0")]
    //[Route("v{version:apiVersion}")]  //[Route("v{version:apiVersion}/[controller]")]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    //public class NotificationsController : ControllerBase
    //{
    //    private readonly IHubContext<NotificationHub> hubContext;
    //    private IWebApiCallerInfo webApiCallerInfo;

    //    public NotificationsController(IHubContext<NotificationHub> hubContext, IWebApiCallerInfo webApiCallerInfo)
    //    {
    //        this.hubContext = hubContext;
    //        this.webApiCallerInfo = webApiCallerInfo;
    //    }

    //    [HttpPost("send")]
    //    public async Task<IActionResult> SendNotification([FromBody] NotificationPayload payload)
    //    {
    //        if (string.IsNullOrEmpty(this.webApiCallerInfo.TenantId.ToString()))
    //            return BadRequest("TenantId is required");

    //        string payloadJson = System.Text.Json.JsonSerializer.Serialize(payload);  //convert payload to JSON string
    //        await hubContext.Clients.Group(this.webApiCallerInfo.TenantId.ToString()).SendAsync("FluentBlueNotification", payloadJson);
    //        return Ok();
    //    }
    //}

    //public class NotificationPayload
    //{
    //    public string NotificationType { get; set; } = string.Empty;
    //    public string Title { get; set; } = string.Empty;
    //    public string Subtitle { get; set; } = string.Empty;
    //    public string Message { get; set; } = string.Empty;
    //    public string SenderUserId { get; set; } = string.Empty;
    //}
}

