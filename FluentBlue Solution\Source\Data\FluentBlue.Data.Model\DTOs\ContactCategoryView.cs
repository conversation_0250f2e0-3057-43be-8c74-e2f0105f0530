﻿using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.VisualBasic;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DTOs
{
    public class ContactCategoryView
    {
        [Key]
        public Guid ContactCategoryMappingId { get; set; }
        [Required]
        public Guid? ContactId { get; set; }    
        [Required]
        public Guid? ContactCategoryId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }
}
