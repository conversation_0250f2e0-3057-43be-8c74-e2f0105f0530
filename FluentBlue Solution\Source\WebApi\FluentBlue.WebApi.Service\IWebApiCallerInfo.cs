﻿namespace FluentBlue.WebApi.Service
{
    public interface IWebApiCallerInfo
    {
        Guid? TenantId { get; }  //Χρησιμεύει ώστε να ξέρουμε σε ποιον Tenant ανοίκει ο User που έστειλε το request στο WebApi.
        Guid? UserId { get; }  //Χρησιμεύει ώστε να ξέρουμε ποιος User έστειλε το request στο WebApi.
        Guid? ApplicationId { get; set; }  //Χρησιμεύει ώστε να ξέρουμε ποιο application έστειλε το request στο WebApi.
        public Task<bool> ValidateCallerInfo(Guid tenantId, Guid userId);
    }
}