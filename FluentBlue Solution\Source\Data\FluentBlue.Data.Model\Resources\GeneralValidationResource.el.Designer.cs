//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources {
    using System;
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class GeneralValidationResource_el {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal GeneralValidationResource_el() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.GeneralValidationResource.el", typeof(GeneralValidationResource_el).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το πεδίο είναι υποχρεωτικό.
        /// </summary>
        public static string FieldRequired {
            get {
                return ResourceManager.GetString("FieldRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το πεδίο {PropertyName} δεν είναι έγκυρο.
        /// </summary>
        public static string FieldWithNameIsInvalid {
            get {
                return ResourceManager.GetString("FieldWithNameIsInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το πεδίο {PropertyName} είναι υποχρεωτικό.
        /// </summary>
        public static string FieldWithNameRequired {
            get {
                return ResourceManager.GetString("FieldWithNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τα δεδομένα δεν είναι έγκυρα.
        /// </summary>
        public static string InvalidData {
            get {
                return ResourceManager.GetString("InvalidData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εισάγετε ενα έγκυρο email.
        /// </summary>
        public static string InvalidEmailAddress {
            get {
                return ResourceManager.GetString("InvalidEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 10 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength10 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 100 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength100 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 15 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength15 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 200 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength200 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength200", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 25 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength25 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength25", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 250 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength250 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength250", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 30 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength30 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το μέγιστο επιτρεπόμενο μήκος είναι 50 χαρακτήρες.
        /// </summary>
        public static string MaximumAllowedLength50 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength50", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η τιμή υπάρχει ήδη.
        /// </summary>
        public static string ValueAlreadyExists {
            get {
                return ResourceManager.GetString("ValueAlreadyExists", resourceCulture);
            }
        }
    }
} 