﻿using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.Extensions.Options;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DBOs.Calendar
{
    [Table("EventReminder", Schema = "Calendar")]
    public class EventReminder : IObjectState, INotifyPropertyChanged
    {
        private Guid eventReminderId;
        private Guid? eventId;
        private ReminderTime reminderOffset = ReminderTime.ZeroMinutes;
        private DateTime? reminderTimeUtc = null;
        private bool pushSent = false;
        private bool notifyPropertyChangedEnabled = false;

        public EventReminder()
        {
            eventReminderId = Guid.CreateVersion7();
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventReminderResource), Name = nameof(Model.Resources.Calendar.EventReminderResource.EventReminderId))]
        public Guid EventReminderId
        {
            get
            {
                return eventReminderId;
            }
            set
            {
                eventReminderId = value;
                NotifyPropertyChanged();
            }
        }

        [Required]
        public Guid? EventId
        {
            get
            {
                return eventId;
            }
            set
            {
                eventId = value;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventReminderResource), Name = nameof(Model.Resources.Calendar.EventReminderResource.MinutesBefore))]
        public ReminderTime ReminderOffset
        {
            get
            {
                return reminderOffset;
            }
            set
            {
                if (reminderOffset != value)
                {
                    reminderOffset = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public DateTime? ReminderTimeUtc
        {
            get
            {
                return this.reminderTimeUtc;
            }
            set
            {
                if (reminderTimeUtc != value)
                {
                    reminderTimeUtc = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public bool PushSent
        {
            get
            {
                return this.pushSent;
            }
            set
            {
                if (this.pushSent != value)
                {
                    this.pushSent = value;
                    NotifyPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Navigation property for the associated Event entity
        /// </summary>
        [ForeignKey(nameof(EventId))]
        public virtual Event? Event { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        [NotMapped]
        public bool NotifyPropertyChangedEnabled
        {
            get
            {
                return this.notifyPropertyChangedEnabled;
            }
            set
            {
                this.notifyPropertyChangedEnabled = value;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (notifyPropertyChangedEnabled == true)
            {
                //DateModifiedUtc = DateTime.UtcNow;
                if (ObjectState == ObjectState.Unchanged)
                {
                    ObjectState = ObjectState.Modified;
                }

                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
            }
        }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

    }
}
