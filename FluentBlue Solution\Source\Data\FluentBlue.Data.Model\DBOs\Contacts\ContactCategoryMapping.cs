﻿using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.VisualBasic;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.CompilerServices;
using FluentBlue.Data.Model.DBOs.Calendar;

namespace FluentBlue.Data.Model.DBOs.Contacts
{

    [Table("ContactCategoryMapping", Schema = "Contacts")]
    public class ContactCategoryMapping : IObjectState
    {
        private Guid contactCategoryMappingId;
        private Guid? contactId;
        private Guid? contactCategoryId;

        public ContactCategoryMapping()
        {
            contactCategoryMappingId = Guid.CreateVersion7();
            ObjectState = ObjectState.Unchanged;
        }

        [Key]
        public Guid ContactCategoryMappingId
        {
            get
            {
                return contactCategoryMappingId;
            }
            set
            {
                contactCategoryMappingId = value;
            }
        } 

        [Required]
        public Guid? ContactId
        {
            get
            {
                return contactId;
            }
            set
            {
                contactId = value;
            }
        }

        [Required]
        public Guid? ContactCategoryId
        {
            get
            {
                return contactCategoryId ?? Guid.Empty;
            }
            set
            {
                contactCategoryId = (value == Guid.Empty ? null : value);
            }
        }

        [ForeignKey("ContactCategoryId")]
        public ContactCategory? ContactCategory { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }


    }
}
