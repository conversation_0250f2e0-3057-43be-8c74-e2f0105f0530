﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Utilities
{
    public class DateTimeConvert
    {
        public static DateTime ToLocalTime(DateTime utcDate, string timeZoneId)
        {
            //var localTimeZoneId = "China Standard Time";
            var localTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            var localTime = TimeZoneInfo.ConvertTimeFromUtc(utcDate, localTimeZone);
            return localTime;
        }
    }
}
