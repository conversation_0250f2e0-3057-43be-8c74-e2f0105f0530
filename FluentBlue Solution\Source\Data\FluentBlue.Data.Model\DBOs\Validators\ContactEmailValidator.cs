﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.Extensions;
using FluentValidation;
using FluentValidation.Results;
using System;

namespace FluentBlue.Data.Model.DBOs.Validators
{


    public class ContactEmailValidator : AbstractValidator<ContactEmail>
    {
        public ContactEmailValidator()
        {
            RuleFor(x => x.ContactId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactEmail.ContactId));
            RuleFor(x => x.ContactEmailId).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactEmail.ContactEmailId));
            RuleFor(x => x.EmailAddress).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactEmail.EmailAddress));
            RuleFor(x => x.EmailAddress).MaximumLength(30).WithMessage(Data.Model.Resources.GeneralValidationResource.MaximumAllowedLength30);
            RuleFor(x => x.EmailAddress).EmailAddress().WithMessage(Data.Model.Resources.GeneralValidationResource.InvalidEmailAddress);
            RuleFor(x => x.DateModifiedLocal).NotEmpty().WithLocalizedMessage(Data.Model.Resources.GeneralValidationResource.FieldWithNameRequired, nameof(ContactEmail.DateModifiedLocal));
        }

        protected override bool PreValidate(ValidationContext<ContactEmail> context, ValidationResult result)
        {
            if (context.InstanceToValidate == null)
            {
                result.Errors.Add(new ValidationFailure("", Resources.GeneralValidationResource.InvalidData));
                return false;
            }
            return true;
        }
    }
}
