﻿//using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public class NotZeroTimeSpanAttribute33 : ValidationAttribute
    {

        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return new CompositeValidationResult(String.Format("Validation for {0} failed!", validationContext.DisplayName));  //TODO: Εδώ πρεπει να φτιαχτεί ώστε τα validation μηνύματα που επιστρέφει να είνα κατανοητά.
            }

            var results = new List<ValidationResult>();
            var context = new ValidationContext(value, null, null);

            //Αν είναι τύπου TimeSpan
            if (value.GetType() == typeof(TimeSpan))
            {
                if ((TimeSpan)value == TimeSpan.Zero)
                {
                    return new ValidationResult(String.Format("Validation for {0} failed!", validationContext.DisplayName));  //TODO: Εδώ πρεπει να φτιαχτεί ώστε τα validation μηνύματα που επιστρέφει να είνα κατανοητά.
                }
            }
            //else  //Αλλιώς για τα υπόλοιπα objects.
            //{
            //    Validator.TryValidateObject(value, context, results, true);
            //}

            return ValidationResult.Success!;
        }


    }


}
