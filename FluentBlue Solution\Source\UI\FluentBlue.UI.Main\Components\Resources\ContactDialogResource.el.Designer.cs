//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ContactDialogResource_el {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ContactDialogResource_el() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.ContactDialogResource.el", typeof(ContactDialogResource_el).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Διευθύνσεις.
        /// </summary>
        public static string Addresses {
            get {
                return ResourceManager.GetString("Addresses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κλείσιμο.
        /// </summary>
        public static string CancelBtn_Text {
            get {
                return ResourceManager.GetString("CancelBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πόλη.
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Διαγραφή.
        /// </summary>
        public static string DeleteBtn_Text {
            get {
                return ResourceManager.GetString("DeleteBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emails.
        /// </summary>
        public static string Emails {
            get {
                return ResourceManager.GetString("Emails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Συμβάντα.
        /// </summary>
        public static string EventsTab {
            get {
                return ResourceManager.GetString("EventsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Όνομα.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Γενικά.
        /// </summary>
        public static string GeneralTab {
            get {
                return ResourceManager.GetString("GeneralTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επώνυμο.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πατρώνυμο.
        /// </summary>
        public static string MiddleName {
            get {
                return ResourceManager.GetString("MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν υπάρχουν διευθύνσεις.
        /// </summary>
        public static string NoAddresses {
            get {
                return ResourceManager.GetString("NoAddresses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν υπάρχουν emails.
        /// </summary>
        public static string NoEmails {
            get {
                return ResourceManager.GetString("NoEmails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν υπάρχουν τηλέφωνα.
        /// </summary>
        public static string NoPhones {
            get {
                return ResourceManager.GetString("NoPhones", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σημειώσεις.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επάγγελμα.
        /// </summary>
        public static string Occupation {
            get {
                return ResourceManager.GetString("Occupation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τηλέφωνα.
        /// </summary>
        public static string Phones {
            get {
                return ResourceManager.GetString("Phones", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ταχ. Κωδικός.
        /// </summary>
        public static string PostalCode {
            get {
                return ResourceManager.GetString("PostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αποθήκευση.
        /// </summary>
        public static string SaveBtn_Text {
            get {
                return ResourceManager.GetString("SaveBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Οδός.
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επαφή.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
    }
} 