﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.Application.Business
{
    public class TenantsBusiness : ITenantsBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public TenantsBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<TenantsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<Data.Model.DBOs.Tenants.Tenant?> GetTenant(Guid tenantId)
        {
            try
            {
                //Query
                IQueryable<Tenant> query = dbContext.Tenants.Where(c => c.TenantId.ToString() == tenantId.ToString());

                return await query.FirstOrDefaultAsync<Tenant>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

    }
}
