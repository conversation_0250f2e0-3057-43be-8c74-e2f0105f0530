using System;

namespace FluentBlue.Shared
{
    /// <summary>
    /// Provides information about the current application instance
    /// </summary>
    public static class ApplicationInstanceInfo
    {
        /// <summary>
        /// A unique identifier for this application instance.
        /// Generated once when the application starts and remains the same until the application closes.
        /// </summary>
        public static Guid ApplicationInstanceId { get; } = Guid.NewGuid();
    }
} 