﻿using FluentBlue.Data.Model.DBOs.Contacts;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using FluentBlue.Data.Model.DBOs.Tenants;

namespace FluentBlue.Data.Model.DTOs
{
    public class ContactView
    {
        [Key]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = "ContactId")]
        public Guid ContactId { get; set; }


        [Required]
        public Guid TenantId { get; set; }


        [ForeignKey("TenantId")]
        public Tenant Tenant { get; set; } = null;

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = "Type")]
        public ContactType? Type { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FirstName))]
        public string FirstName { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.LastName))]
        public string LastName { get; set; } = string.Empty;


        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.MiddleName))]
        public string MiddleName { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Occupation))]
        public string Occupation { get; set; } = string.Empty;

        /// <summary>
        /// Tax Identification Number
        /// </summary>
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.TIN))]
        public string TIN { get; set; } = string.Empty;

        /// <summary>
        /// Social Security Number
        /// </summary>
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.SSN))]
        public string SSN { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Vat))]
        [Column(TypeName = "decimal(6,2)")]
        public decimal Vat { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.ContactCategories))]
        public List<ContactCategoryView> ContactCategoryViews { get; set; } = new List<ContactCategoryView>();

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Image))]
        public string Image { get; set; } = string.Empty;

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FullName))]
        public string FullName
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FullName))]
        public string FirstLastName
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.FullName))]
        public string LastFirstName
        {
            get
            {
                return LastName + " " + FirstName;
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactResource), Name = nameof(Model.Resources.Contacts.ContactResource.Summary))]
        public string Summary
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }

        public string Initials { get; set; } = string.Empty;
    }
}
