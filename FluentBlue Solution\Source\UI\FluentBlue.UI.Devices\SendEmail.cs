﻿using FluentBlue.Shared.Utilities;

namespace FluentBlue.UI.Devices
{
    class SendEmail : ISendEmail
    {
        public async Task OpenEmailForm(string to, string subject, string body)
        {
            if (DeviceInfo.Platform == DevicePlatform.Android)
            {
                await SendAndroidEmail(to, subject, body);
            }
            else if (DeviceInfo.Platform == DevicePlatform.iOS)
            {
                await SendIOSEmail(to, subject, body);
            }
            else if (DeviceInfo.Idiom == DeviceIdiom.Desktop)
            {
                await SendDesktopEmail(to, subject, body);
            }
        }

        private async Task SendAndroidEmail(string to, string subject, string body)
        {
            try
            {
                var uri = new Uri($"mailto:{to}?subject={Uri.EscapeDataString(subject)}&body={Uri.EscapeDataString(body)}");
                await Launcher.OpenAsync(uri);
            }
            catch (Exception)
            {
                // Fallback if mailto doesn't work
                var email = new EmailMessage
                {
                    To = new List<string> { to },
                    Subject = subject,
                    Body = body
                };
                await Email.ComposeAsync(email);
            }
        }

        private async Task SendIOSEmail(string to, string subject, string body)
        {
            var email = new EmailMessage
            {
                To = new List<string> { to },
                Subject = subject,
                Body = body
            };
            await Email.ComposeAsync(email);
        }

        private async Task SendDesktopEmail(string to, string subject, string body)
        {
            var uri = new Uri($"mailto:{to}?subject={Uri.EscapeDataString(subject)}&body={Uri.EscapeDataString(body)}");
            await Launcher.OpenAsync(uri);
        }
    }
}
