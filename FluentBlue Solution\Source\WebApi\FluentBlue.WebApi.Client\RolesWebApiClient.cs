﻿using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace FluentBlue.WebApi.Client
{
    public class RolesWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<RolesWebApiClient> logger;

        public RolesWebApiClient(HttpClient httpClient, ILogger<RolesWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DBOs.Tenants.Role>>> GetRoles(Guid tenantId, string filter, UInt16 pageIndex, UInt16 pageSize)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "Roles/Get";

                RequestDataParameters requestDataParams = new RequestDataParameters();
                requestDataParams.TenantId = tenantId;
                requestDataParams.Filter = filter.Trim();
                requestDataParams.PageIndex = pageIndex;
                requestDataParams.PageSize = pageSize;
                requestDataParams.SortColumns = new Dictionary<string, SortOrder>();
                requestDataParams.SortColumns.Add("Name", SortOrder.Ascending);
                string getEntitiesParamsJson = JsonConvert.SerializeObject(requestDataParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

                string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
                requestUri = requestUri + "?" + urlParameters;

                //Εκτελούμε το request.
                //this.httpClient.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIyY2FhYmI1NC1mZDM4LTRhY2MtOTMyYS1kNWM3Yjk0ZmRkMmYiLCJVc2VybmFtZSI6IjMzIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6IlAgTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlN1cGVyQWRtaW4iLCJUZW5hbnRJZCI6IjNmYTg1ZjY0LTU3MTctNDU2Mi1iM2ZjLTJjOTYzZjY2YWZhNiIsImV4cCI6MTc0NzA0Mjk2Mn0.ZqUnCMKdLJ37E2PBZJJVTaFXpVBa5skxbHhBY18wTjs");
                //HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);  //Διαβάζουμε το HttpResponse

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<PagedData<List<Data.Model.DBOs.Tenants.Role>>> response = JsonConvert.DeserializeObject<ApiResponse<PagedData<List<Data.Model.DBOs.Tenants.Role>>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new PagedData<List<Data.Model.DBOs.Tenants.Role>> { Data = null, DataTotalCount = 0 };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetRoles({@tenantId},{fiter},{@pageIndex}, {@pageSize})", tenantId, filter, pageIndex, pageSize);
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.RoleLI>> GetRolesList(Guid tenantId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Roles/GetList";

                //Εκτελούμε το request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Model.DTOs.RoleLI>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DTOs.RoleLI>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new List<Data.Model.DTOs.RoleLI>();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetRolesLI({@tenantId})", tenantId);
                throw;
            }
        }

        public async Task<Role?> GetRole(Guid roleId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "Role/Get?RoleId=" + roleId.ToString();  // + "&tenantId=" + tenantId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Role?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Tenants.Role?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetRole({@roleId})", roleId);
                throw;
            }
        }

        public async Task<Role?> CreateOrUpdateRole(Role role)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Role/CreateOrUpdate";

                //string RoleJson = Newtonsoft.Json.JsonConvert.SerializeObject(Role, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Role object σε json.
                string RoleJson = System.Text.Json.JsonSerializer.Serialize(role);  //Μετατρέπουμε το Role object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(RoleJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<Role>? response = JsonConvert.DeserializeObject<ApiResponse<Role>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateRole({@tenant})", role);
                throw;
            }
        }

        public async Task DeleteRole(Guid roleId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/Role/Delete?RoleId=" + roleId.ToString();

                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (Exception)
            {
                throw;
            }

        }

        public async Task<Role?> CheckRoleExists(Guid roleId, string roleName)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "Role/CheckExists?RoleId=" + roleId.ToString() + "&Name=" + roleName;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Role?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Tenants.Role?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CheckRoleExists({@roleId}, {@name})", roleId, roleName);
                throw;
            }
        }
    }
}
