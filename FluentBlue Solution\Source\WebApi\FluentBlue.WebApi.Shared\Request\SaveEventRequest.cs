﻿using FluentBlue.Data.Model.DBOs.Calendar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Shared.Request
{
    public class SaveEventRequest
    {
        public required Event Event { get; set; }
        public RecurrentEventHandlingType? RecurrentEventUpdateType { get; set; }
        public List<DateTime>? RecurrenceDates { get; set; } = new List<DateTime>();
    }
}
