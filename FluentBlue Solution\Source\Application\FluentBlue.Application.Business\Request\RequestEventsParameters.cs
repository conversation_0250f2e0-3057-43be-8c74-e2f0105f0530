﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business.Request
{
    public class RequestEventsParameters
    {
        public Guid TenantId { get; set; }  //TODO: πρέπει να φύγει το TenantId να πάει στο jwt
        public string Filter { get; set; } = string.Empty;
        public Guid[]? UserIds { get; set; } = null;
        public long StartDateTicksUtc { get; set; }
        public long EndDateTicksUtc { get; set; }
    }
}
