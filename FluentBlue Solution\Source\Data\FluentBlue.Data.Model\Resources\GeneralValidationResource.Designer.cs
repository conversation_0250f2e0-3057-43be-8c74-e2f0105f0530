﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.Data.Model.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class GeneralValidationResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal GeneralValidationResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.Data.Model.Resources.GeneralValidationResource", typeof(GeneralValidationResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field is required..
        /// </summary>
        public static string FieldRequired {
            get {
                return ResourceManager.GetString("FieldRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field {PropertyName} is invalid..
        /// </summary>
        public static string FieldWithNameIsInvalid {
            get {
                return ResourceManager.GetString("FieldWithNameIsInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field {PropertyName} is required..
        /// </summary>
        public static string FieldWithNameRequired {
            get {
                return ResourceManager.GetString("FieldWithNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data are invalid..
        /// </summary>
        public static string InvalidData {
            get {
                return ResourceManager.GetString("InvalidData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter a valid email address..
        /// </summary>
        public static string InvalidEmailAddress {
            get {
                return ResourceManager.GetString("InvalidEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 10 characters.
        /// </summary>
        public static string MaximumAllowedLength10 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 100 characters.
        /// </summary>
        public static string MaximumAllowedLength100 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 15 characters.
        /// </summary>
        public static string MaximumAllowedLength15 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 200 characters.
        /// </summary>
        public static string MaximumAllowedLength200 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength200", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 25 characters.
        /// </summary>
        public static string MaximumAllowedLength25 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength25", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 250 characters.
        /// </summary>
        public static string MaximumAllowedLength250 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength250", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 30 characters.
        /// </summary>
        public static string MaximumAllowedLength30 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed length is 50 characters.
        /// </summary>
        public static string MaximumAllowedLength50 {
            get {
                return ResourceManager.GetString("MaximumAllowedLength50", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value already exists.
        /// </summary>
        public static string ValueAlreadyExists {
            get {
                return ResourceManager.GetString("ValueAlreadyExists", resourceCulture);
            }
        }
    }
}
