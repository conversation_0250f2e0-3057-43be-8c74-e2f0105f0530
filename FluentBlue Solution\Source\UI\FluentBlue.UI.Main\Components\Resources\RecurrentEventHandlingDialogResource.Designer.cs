﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class RecurrentEventHandlingDialogResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal RecurrentEventHandlingDialogResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.RecurrentEventHandlingDialogResource", typeof(RecurrentEventHandlingDialogResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All events of recurrence.
        /// </summary>
        internal static string AllEvents {
            get {
                return ResourceManager.GetString("AllEvents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This event and the following.
        /// </summary>
        internal static string CurrentAndFollowingEvents {
            get {
                return ResourceManager.GetString("CurrentAndFollowingEvents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only this event.
        /// </summary>
        internal static string CurrentEvent {
            get {
                return ResourceManager.GetString("CurrentEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Event.
        /// </summary>
        internal static string DeleteRucurrentEventTitle {
            get {
                return ResourceManager.GetString("DeleteRucurrentEventTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select how you want to delete the recurrent event..
        /// </summary>
        internal static string SelectRecurrentEventDeleteType {
            get {
                return ResourceManager.GetString("SelectRecurrentEventDeleteType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select how you want to update the recurrent event..
        /// </summary>
        internal static string SelectRecurrentEventUpdateType {
            get {
                return ResourceManager.GetString("SelectRecurrentEventUpdateType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Event.
        /// </summary>
        internal static string UpdateRucurrentEventTitle {
            get {
                return ResourceManager.GetString("UpdateRucurrentEventTitle", resourceCulture);
            }
        }
    }
}
