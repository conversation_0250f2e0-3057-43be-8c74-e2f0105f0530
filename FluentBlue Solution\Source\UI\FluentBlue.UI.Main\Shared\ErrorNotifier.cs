﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.UI.Main.Components;
using FluentBlue.WebApi.Shared.Request;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Main.Shared
{
    public class ErrorNotifier
    {
        private readonly IDialogService dialogService;
        //private static readonly SemaphoreSlim semaphore = new SemaphoreSlim(1, 1);
        private static readonly Mutex _mutex = new();
        private static bool dialogOpened = false;

        public ErrorNotifier(IDialogService dialogService)
        {
            this.dialogService = dialogService;
        }

        public async Task ShowErrorAsync(string message, string title)
        {
            _mutex.WaitOne();
            try
            {
                if (dialogOpened == false)
                {
                    dialogOpened = true;
                    await dialogService.ShowErrorAsync(message, title);
                       
                    dialogOpened = false; // Reset the flag after the dialog is closed
                }


                //IDialogReference? dialog;

                //Data.Model.DBOs.Contacts.Contact simplePerson = new()
                //{
                //    FirstName = "Steve"
                //};

                ////εμφανίζει το dialog
                //DialogParameters<Data.Model.DBOs.Calendar.Event> parameters = new()
                //{
                //    ShowTitle = true,
                //    OnDialogResult = dialogService.CreateDialogCallback(this, OnDialogResult),
                //    Title = UI.Main.Pages.Resources.CalendarResource.Event,
                //    PrimaryAction = "",  //GlobalResource.Save,
                //    SecondaryAction = "", //=GlobalResource.Cancel,
                //    //Width = dialogWidth,
                //    //Height = dialogHeight,
                //    TrapFocus = false,
                //    Modal = true,
                //    //PreventScroll = true,
                //    PreventDismissOnOverlayClick = true,
                //    ShowDismiss = false,
                //    Alignment = HorizontalAlignment.Center
                //};
                //EventDialogInput eventDialogInput = new EventDialogInput() { Event = null, RecurrentEventHandlingType = WebApi.Shared.Request.RecurrentEventHandlingType.Current };
                //dialog = dialogService.ShowDialog<UI.Main.Components.EventDialog>eventDialogInput, parameters);
                //DialogResult? result = await dialog.Result;
            }
            finally
            {
                _mutex.ReleaseMutex(); // Always release lock
            }
        }

        private async Task OnDialogResult(DialogResult result)
        {
        }

        public void ShowInfo(string message, string title)
        {
            this.dialogService.ShowInfo(message, title);
        }
    }
}
