﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Shared.Response
{
    public class SummaryDataDto
    {
        public int ContactsCount { get; set; } = 0;
        public int FutureEventsCount { get; set; } = 0;
        public int TodayEventsCount { get; set; } = 0;
        public int TomorrowEventsCount { get; set; } = 0;
        public int UsersCount { get; set; } = 0;
    }
}
