/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ["./**/*.{razor,html,cshtml}"],
    important: true,
    theme: {
        extend: {},
        screens: {
            'xs': { 'min': '0px', 'max': '600px' },
            // => @media (max-width: 600px) { ... }

            'sm': { 'min': '601px', 'max': '960px' },
            // => @media (max-width: 960px) { ... }

            'md': { 'min': '961px', 'max': '1280px' },
            // => @media (max-width: 1280px) { ... }

            'lg': { 'min': '1281px', 'max': '1920px' },
            // => @media (max-width: 1920px) { ... }

            'xl': { 'min': '1921px', 'max': '2560px' },
            // => @media (max-width: 2560px) { ... }

            'xxl': { 'min': '2561px' },
            // => @media (min-width: 2561px) { ... }
        }
    },
    plugins: [],
}

