﻿CREATE TABLE [Calendar].[EventUser] (
    [EventUserId] UNIQUEIDENTIFIER CONSTRAINT [DF_Owner_OwnerId] DEFAULT (newsequentialid()) NOT NULL,
    [UserId]      UNIQUEIDENTIFIER NOT NULL,
    [EventId]     UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_Owner] PRIMARY KEY CLUSTERED ([EventUserId] ASC),
    CONSTRAINT [FK_EventUser_Event] FOREIGN KEY ([EventId]) REFERENCES [Calendar].[Event] ([EventId]) ON DELETE CASCADE ON UPDATE CASCADE
);






GO
CREATE NONCLUSTERED INDEX [UserIdIndex]
    ON [Calendar].[EventUser]([UserId] ASC);


GO
CREATE NONCLUSTERED INDEX [EventIdIndex]
    ON [Calendar].[EventUser]([EventId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueEventUser]
    ON [Calendar].[EventUser]([UserId] ASC, [EventId] ASC);

