﻿using FluentBlue.Shared.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{
    class PerformPhoneCall : IPerformPhoneCall
    {
        public async Task PlacePhoneCall(string phoneNumber)
        {
            try
            {
                if (DeviceInfo.Platform == DevicePlatform.Android)
                {
                    if (await Permissions.CheckStatusAsync<Permissions.Phone>() != PermissionStatus.Granted)
                    {
                        await Permissions.RequestAsync<Permissions.Phone>();
                    }

                    PhoneDialer.Default.Open(phoneNumber);
                }
                else if (DeviceInfo.Platform == DevicePlatform.iOS)
                {
                    var url = new Uri($"tel:{phoneNumber}");
                    await Browser.Default.OpenAsync(url, BrowserLaunchMode.SystemPreferred);
                }
            }
            catch (Exception exp)
            {
                throw;
            }
        }
    }
}
