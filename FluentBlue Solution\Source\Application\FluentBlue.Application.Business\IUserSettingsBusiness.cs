﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;

namespace FluentBlue.Application.Business
{
    public interface ISettingsBusiness
    {
        Task<UserSetting?> GetUserSettings(Guid userId);
        Task CreateOrUpdateUserSettings(UserSetting userSetting);
    }
}