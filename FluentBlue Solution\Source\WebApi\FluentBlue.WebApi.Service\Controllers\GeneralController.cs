﻿using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Diagnostics;


namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]  //[Route("v{version:apiVersion}/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class GeneralController : ControllerBase
    {
        private IContactsBusiness contactsBusiness;
        private IEventsBusiness eventsBusiness;
        private IUsersBusiness usersBusiness;
        private ILogger<ContactsController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public GeneralController(IContactsBusiness contactsBusiness, IEventsBusiness eventsBusiness, IUsersBusiness usersBusiness, ILogger<ContactsController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            try
            {
                this.contactsBusiness = contactsBusiness;
                this.eventsBusiness = eventsBusiness;
                this.usersBusiness = usersBusiness;
                this.logger = logger;
                this.mapper = mapper;
                this.webApiCallerInfo = webApiCallerInfo;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        [HttpGet]
        [Route("General/GetSummaryData")]
        public async Task<ApiResponse<SummaryDataDto>> GetSummaryData(DateTime todayLocal, string timeZoneId)
        {
            try
            {
                SummaryDataDto dashboardDataDto = new SummaryDataDto();
                //Map data

                //Validation
                //if (readPagedContactsParameters.TenantId != this.webApiCallerInfo.TenantId)
                //{
                //    return new ApiResponse<PagedData<List<FluentBlue.Data.Model.DTOs.ContactView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = "TenantId missmacth." };
                //}

                //Query
                dashboardDataDto.ContactsCount = await this.contactsBusiness.GetContactsCount(this.webApiCallerInfo.TenantId!.Value);
                dashboardDataDto.FutureEventsCount = await this.eventsBusiness.GetFutureEventsCount(this.webApiCallerInfo.TenantId!.Value);
                dashboardDataDto.TodayEventsCount = await this.eventsBusiness.GetTotalEventsForDate(this.webApiCallerInfo.TenantId!.Value, todayLocal, timeZoneId);
                dashboardDataDto.TomorrowEventsCount = await this.eventsBusiness.GetTotalEventsForDate(this.webApiCallerInfo.TenantId!.Value, todayLocal.AddDays(1), timeZoneId);
                dashboardDataDto.UsersCount = await this.usersBusiness.GetUsersCount(this.webApiCallerInfo.TenantId!.Value);

                //Response
                return new ApiResponse<SummaryDataDto>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = dashboardDataDto };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<SummaryDataDto>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                return new ApiResponse<SummaryDataDto>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}
