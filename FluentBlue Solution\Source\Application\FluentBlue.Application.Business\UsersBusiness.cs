﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using static System.Runtime.InteropServices.JavaScript.JSType;
using FluentBlue.Data.Model.DBOs.Settings;

namespace FluentBlue.Application.Business
{
    public class UsersBusiness : IUsersBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        private IMapper mapper;
        private ILogger logger;

        public UsersBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger logger)
        {
            this.dbContext = dbContext;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<PagedData<List<FluentBlue.Data.Model.DTOs.UserView>>> GetUsers(ReadPagedDataParameters parameters)
        {
            try
            {
                //Validation
                int resultsCount;
                parameters.Filter = parameters.Filter?.Trim() ?? "";
                if (parameters.PageIndex <= 0)
                {
                    parameters.PageIndex = 1;
                }
                if (parameters.PageSize <= 0)
                {
                    parameters.PageSize = 10;
                }

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.Include(x => x.Role).AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == parameters.TenantId);
                if (parameters.Filter != null && parameters.Filter != "")
                {
                    query = query.Where(x => x.Username.Contains(parameters.Filter) || x.FirstName.Contains(parameters.Filter) || x.LastName.Contains(parameters.Filter) || x.Email.Contains(parameters.Filter) || x.Role!.Name.Contains(parameters.Filter));
                }
                if (parameters.SortColumns != null && parameters.SortColumns.Count > 0)
                {
                    foreach (var sortColumn in parameters.SortColumns)
                    {
                        query = sortColumn.Value == SortOrder.Ascending
                            ? query.OrderBy(e => EF.Property<object>(e, sortColumn.Key))
                            : query.OrderByDescending(e => EF.Property<object>(e, sortColumn.Key));
                    }
                }

                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Tenants.User> users = await query.Skip((parameters.PageIndex - 1) * parameters.PageSize).Take(parameters.PageSize).ToListAsync();
                List<FluentBlue.Data.Model.DTOs.UserView> usersDTOs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Tenants.User>, List<FluentBlue.Data.Model.DTOs.UserView>>(users);

                PagedData<List<FluentBlue.Data.Model.DTOs.UserView>> pagedData = new PagedData<List<Data.Model.DTOs.UserView>>();
                pagedData.Data = usersDTOs;
                pagedData.DataTotalCount = resultsCount;
                return pagedData;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<List<FluentBlue.Data.Model.DTOs.UserLI>> GetUsersLI(Guid tenantId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTrackingWithIdentityResolution().AsQueryable();
                query = query.Where(x => x.TenantId == tenantId).OrderBy(x => x.LastName);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Tenants.User> users = await query.ToListAsync();
                List<FluentBlue.Data.Model.DTOs.UserLI> usersLIs = mapper.Map<List<FluentBlue.Data.Model.DBOs.Tenants.User>, List<FluentBlue.Data.Model.DTOs.UserLI>>(users);

                return usersLIs;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Tenants.User?> GetUser(string username, string password)
        {
            try
            {
                if (username == "333" && password == "333")
                {
                    User fakeUser = new Data.Model.DBOs.Tenants.User();
                    fakeUser.Username = "33";
                    fakeUser.LastName = "Manp";
                    fakeUser.FirstName = "Peter";
                    fakeUser.Email = "<EMAIL>";
                    fakeUser.UserId = Guid.CreateVersion7();
                    fakeUser.TenantId = Guid.CreateVersion7();
                    fakeUser.RoleId = Guid.CreateVersion7();
                    Role role = new Role();
                    role.TenantId = fakeUser.TenantId;
                    role.Name = "Admin";
                    fakeUser.Role = role;
                    return fakeUser;
                }

                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTrackingWithIdentityResolution().AsQueryable();
                query = query.Include(x => x.Role).Where(x => x.Username == username && x.Password == password);
                Data.Model.DBOs.Tenants.User? user = await query.FirstOrDefaultAsync();

                //Result
                return user;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Tenants.User?> GetUser(Guid userId)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTrackingWithIdentityResolution().Include(x => x.Role).AsNoTrackingWithIdentityResolution().Where(x => x.UserId == userId);
                Data.Model.DBOs.Tenants.User? user = await query.FirstOrDefaultAsync();

                if (user?.Role != null)
                {
                    user.Role.Users = new List<User>();  //Καθαρίζουμε το property Users του Role γιατί φέρνει δεδομένα χωρίς να τα έχουμε ζητήσει και δημιουργεί πρόβλημα.
                }

                //////////////////////
                //FluentBlue.Data.Model.DBOs.Tenants.User? user2 = await this.dbContext.Users.AsNoTrackingWithIdentityResolution().Where(x => x.UserId == userId).FirstOrDefaultAsync();
                //if (user2 != null)
                //{
                //    this.dbContext.Entry(user2).Reference(x => x.Role).Load();
                //}

                //Result
                return user;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateOrUpdateUser(User user)
        {
            try
            {
                //Validation
                if (user == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                UserValidator validator = new UserValidator();
                FluentValidation.Results.ValidationResult result = validator.Validate(user);
                string validationErrors = string.Empty;
                if (!result.IsValid)
                {
                    foreach (var failure in result.Errors)
                    {
                        validationErrors += failure.ErrorMessage + ". ";
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Query
                this.dbContext.Attach(user);

                //Creates UserSetting for new User
                if (user.ObjectState == ObjectState.Added)
                {
                    UserSetting userSetting = new UserSetting();
                    userSetting.UserId = user.UserId;
                    userSetting.ObjectState = ObjectState.Added;
                    this.dbContext.Attach(userSetting);
                }
                
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { user.UserId });
                throw;
            }
        }

        public async Task DeleteUser(Guid userId)
        {
            User? user = await this.dbContext.Users.Where(x => x.UserId == userId).AsNoTracking().FirstAsync();
            if (user != null)
            {
                user.ObjectState = ObjectState.Deleted;
                this.dbContext.Attach(user);
                this.dbContext.SaveChanges();
            }
        }

        public async Task<bool> CheckUsernamePasswordExists(string username, string password)
        {
            try
            {
                //Query
                IQueryable<FluentBlue.Data.Model.DBOs.Tenants.User> query = this.dbContext.Users.AsNoTracking().AsQueryable();
                query = query.Where(x => x.Username == username && x.Password == password);
                int count = await query.CountAsync();

                return count > 0;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<User?> CheckUserExists(Guid tenantId, Guid userId, string email)
        {
            User? user = await this.dbContext.Users.Where(x => x.UserId != userId && x.Email == email && x.TenantId == tenantId).FirstOrDefaultAsync();
            return user;
        }

        public async Task<int> GetUsersCount(Guid tenantId)
        {
            try
            {
                //Query
                IQueryable<User> query = this.dbContext.Users.AsNoTracking().AsQueryable();
                query = query.Where(c => c.TenantId == tenantId);

                //Return total count
                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }
    }
}
