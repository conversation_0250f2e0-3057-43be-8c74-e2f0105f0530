<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Calendar" xml:space="preserve">
    <value>Ημερολόγιο</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Επαφές</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>Γενικά</value>
  </data>
  <data name="LanguageTimeTitle" xml:space="preserve">
    <value>Γλώσσα &amp; Ώρα</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Χρώμα</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>Θέμα</value>
  </data>
  <data name="CalendarWorkDays" xml:space="preserve">
    <value>Εργάσιμες ημέρες</value>
  </data>
  <data name="CalendarWorkHours" xml:space="preserve">
    <value>Ώρες εργασίας</value>
  </data>
  <data name="EndHour" xml:space="preserve">
    <value>Ώρα λήξης</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Παρασκευή</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Δευτέρα</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Σάββατο</value>
  </data>
  <data name="StartHour" xml:space="preserve">
    <value>Ώρα έναρξης</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>Κυριακή</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Πέμπτη</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Τρίτη</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Τετάρτη</value>
  </data>
  <data name="FirstDayOfWeek" xml:space="preserve">
    <value>Πρώτη ημέρα της εβδομάδας</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Γλώσσα</value>
  </data>
  <data name="CalendarTimeScaleInterval" xml:space="preserve">
    <value>Χρονικό διάστημα</value>
  </data>
  <data name="CalendarDefaultView" xml:space="preserve">
    <value>Προεπιλεγμένη προβολή</value>
  </data>
  <data name="CalendarDefaultViewOnMobiles" xml:space="preserve">
    <value>Προεπιλεγμένη προβολή σε κινητά</value>
  </data>
  <data name="BehaviourTitle" xml:space="preserve">
    <value>Συμπεριφορά</value>
  </data>
  <data name="CalendarScrollToTime" xml:space="preserve">
    <value>Κύλιση σε συγκεκριμένη ώρα</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>Ζώνη ώρας</value>
  </data>
  <data name="CultureInfo" xml:space="preserve">
    <value>Τοπικές Ρυθμίσεις</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Ρόλοι</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Χρήστες</value>
  </data>
  <data name="UsersRoles" xml:space="preserve">
    <value>Χρήστες &amp; Ρόλοι</value>
  </data>
  <data name="NewUserBtn.Text" xml:space="preserve">
    <value>Νέος Χρήστης</value>
  </data>
  <data name="UserFullName" xml:space="preserve">
    <value>Όνομα</value>
  </data>
  <data name="UserEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UserNotExists" xml:space="preserve">
    <value>Ο χρήστης δεν υπάρχει.</value>
  </data>
  <data name="NewRoleBtn.Text" xml:space="preserve">
    <value>Νέος Ρόλος</value>
  </data>
  <data name="RoleNotExists" xml:space="preserve">
    <value>Ο ρόλος δεν υπάρχει.</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Ρόλος</value>
  </data>
  <data name="EventCategoriesBtn.Text" xml:space="preserve">
    <value>Επεξεργασία Κατηγοριών Συμβάντων</value>
  </data>
  <data name="EventCategoriesTitle" xml:space="preserve">
    <value>Κατηγορίες Συμβάντων</value>
  </data>
  <data name="EventStatesBtn.Text" xml:space="preserve">
    <value>Επεξεργασία Καταστάσεων Συμβάντων</value>
  </data>
  <data name="EventStatesTitle" xml:space="preserve">
    <value>Καταστάσεις Συμβάντων</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Ρυθμίσεις</value>
  </data>
  <data name="ShowSsnInContactLists" xml:space="preserve">
    <value>Εμφάνιση του Α.Φ.Μ. στις λίστες των επαφών.</value>
  </data>
  <data name="ShowTinInContactLists" xml:space="preserve">
    <value>Εμφάνιση του Α.Μ.Κ.Α. στις λίστες των επαφών.</value>
  </data>
  <data name="ContactCategoriesBtn.Text" xml:space="preserve">
    <value>Επεξεργασία Κατηγοριών Επαφών</value>
  </data>
  <data name="ContactCategoriesTitle" xml:space="preserve">
    <value>Κατηγορίες Επαφών</value>
  </data>
  <data name="AppearanceTitle" xml:space="preserve">
    <value>Εμφάνιση</value>
  </data>
  <data name="DateFormat" xml:space="preserve">
    <value>Μορφή ημερομηνίας</value>
  </data>
  <data name="TimeFormat" xml:space="preserve">
    <value>Μορφή ώρας</value>
  </data>
  <data name="LanguageChangeRequiresRestartConfirmation" xml:space="preserve">
    <value>Η αλλαγή γλώσσας απαιτεί την επανεκίννηση της εφαρμογής. Είστε σίγουρος ότι θέλετε να αλλάξετε τη γλώσσα;</value>
  </data>
</root>