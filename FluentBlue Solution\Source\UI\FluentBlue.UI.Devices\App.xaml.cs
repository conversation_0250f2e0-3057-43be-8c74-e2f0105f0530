﻿using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Main;
using System.Globalization;
using Microsoft.AspNetCore.Components;
using FluentBlue.UI.Main.Shared;
using Microsoft.FluentUI.AspNetCore.Components;
using Blazored.LocalStorage;

namespace FluentBlue.UI.Devices
{
    public partial class App : Application
    {
        public App()
        {
            InitializeComponent();
        }

        protected override Window CreateWindow(IActivationState? activationState) // Updated nullability to match the base method
        {
            return new Window(new MainPage());
        }
    }
}
