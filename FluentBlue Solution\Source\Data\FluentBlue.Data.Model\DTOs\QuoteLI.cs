﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DTOs
{
    public class QuoteLI
    {
        public Guid QuoteId { get; set; }
        public string QuoteCode { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public DateTime DateTime { get; set; }
        public string CustomerDisplayName { get; set; } = string.Empty;
    }
}
