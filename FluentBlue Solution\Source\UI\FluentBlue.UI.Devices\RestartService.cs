﻿using FluentBlue.UI.Main;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.UI.Devices
{

    public class RestartAppService : IRestartAppService
    {
        public async Task Restart()
        {
            //MainThread.BeginInvokeOnMainThread(async () =>
            //{
            //    //App.Current.MainPage = new MainPage();
            //    Application.Current.MainPage = new MainPage(); 
            //});

            var filename = Process.GetCurrentProcess().MainModule.FileName;
            // Start a new instance of the application
            Process.Start(filename);
            // Close the current process
            Application.Current.Quit();

            //(Application.Current as App).MainPage.Dispatcher.Dispatch(() =>
            //{
            //    (Application.Current as App).MainPage = new MainPage();
            //});
        }
    }
}
