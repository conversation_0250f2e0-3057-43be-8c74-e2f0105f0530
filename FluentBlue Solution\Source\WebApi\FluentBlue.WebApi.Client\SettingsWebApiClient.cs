﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.WebApi.Client
{
    public class SettingsWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<SettingsWebApiClient> logger;

        public SettingsWebApiClient(HttpClient httpClient, ILogger<SettingsWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        //public async Task<UserSetting?> GetUserSettings(Guid userId)
        //{
        //    try
        //    {
        //        string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "Settings" + "/" + userId.ToString();  

        //        string responseString = await this.httpClient.GetStringAsync(requestUri);

        //        ApiResponse<UserSetting?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Model.DBOs.Settings.UserSetting?>>(responseString);
        //        if (response!.ResultCode == ApiResponseResultCode.Ok)
        //        {
        //            return response.ResponseContent;
        //        }
        //        else if (response.ResultCode == ApiResponseResultCode.Exception)
        //        {
        //            if (response.ExceptionMessageForUser != null)
        //            {
        //                throw new ApplicationException(response.ExceptionMessageForUser ?? "");
        //            }
        //            else
        //            {
        //                throw new Exception(response.ExceptionMessage ?? "");
        //            }
        //        }

        //        return null;
        //    }
        //    catch (Exception ex)
        //    {
        //        this.logger.LogError(ex, "Error in GetUserSettings({@userId})", userId);
        //        throw;
        //    }
        //}

        public async Task<UserSetting?> CreateOrUpdateUserSettings(UserSetting userSetting)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/Setting/CreateOrUpdate";

                //string ContactJson = Newtonsoft.Json.JsonConvert.SerializeObject(Contact, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Contact object σε json.
                string ContactJson = System.Text.Json.JsonSerializer.Serialize(userSetting);  //Μετατρέπουμε το Contact object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(ContactJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<UserSetting>? response = JsonConvert.DeserializeObject<ApiResponse<UserSetting>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateUserSetting({@userSettings})", userSetting);
                throw;
            }
        }

    }
}
