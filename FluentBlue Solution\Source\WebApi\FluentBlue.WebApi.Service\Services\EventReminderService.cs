﻿using Azure.Core;
using FluentBlue.Application.Business;
using FluentBlue.Data.Model;
using FluentBlue.Shared.SignalR;
using FluentBlue.WebApi.Service.Controllers;
using FluentBlue.WebApi.Service.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System;

namespace FluentBlue.WebApi.Service.Services
{
    public class EventReminderService : BackgroundService
    {
        private IServiceProvider serviceProvider;
        private IHubContext<NotificationHub> notificationHub;
        private IEventsBusiness eventsBusiness;
        private ILogger<EventReminderService> logger;
        private IWebApiCallerInfo webApiCallerInfo;

        public EventReminderService(IServiceProvider serviceProvider, IHubContext<NotificationHub> notificationHub,  ILogger<EventReminderService> logger)
        {
            this.serviceProvider = serviceProvider;
            //this.webApiCallerInfo = webApiCallerInfo;
            this.notificationHub = notificationHub;
            //this.eventsBusiness = eventsBusiness;
            this.logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await CheckRemindersAsync();
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Check every minute
            }
        }

        private async Task CheckRemindersAsync()
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                FluentBlueDbContext context = scope.ServiceProvider.GetRequiredService<Data.Model.FluentBlueDbContext>();
                IWebApiCallerInfo webApiCallerInfo = scope.ServiceProvider.GetRequiredService<IWebApiCallerInfo>();

                var utcNow = DateTime.UtcNow;
                var reminders = await context.EventReminders.Include(x=>x.Event).Where(x => x.PushSent == false && x.ReminderTimeUtc <= utcNow && x.ReminderTimeUtc >= utcNow.AddMinutes(-15))
                    .ToListAsync();

                foreach (var reminder in reminders)
                {
                    #region  Notifies User about Event.
                    try
                    {
                        //Send SignalR notification to clients
                        NotificationData notificationData = new NotificationData();
                        notificationData.NotificationType = "EventReminder";
                        notificationData.SenderId = webApiCallerInfo.ApplicationId?.ToString() ?? "";
                        string notificationDataJson = System.Text.Json.JsonSerializer.Serialize(notificationData);  //convert payload to JSON string
                        await notificationHub.Clients.All.SendAsync("FluentBlueNotification", notificationDataJson);
                    }
                    catch (Exception ex)
                    {
                        this.logger.LogError(ex, ex.Message, reminder.ToString());
                    }
                    #endregion


                    // reminder.IsDismissed = true;
                }

                await context.SaveChangesAsync();
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                logger.LogError(ex, ex.Message);
                //new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    logger.LogError(ex, ex.Message);
                    //new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    //new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                //new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
    }
}
