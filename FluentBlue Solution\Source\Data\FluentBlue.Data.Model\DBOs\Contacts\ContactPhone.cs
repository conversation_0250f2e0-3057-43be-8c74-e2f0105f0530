﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.CompilerServices;

namespace FluentBlue.Data.Model.DBOs.Contacts
{
    [Table("ContactPhone", Schema = "Contacts")]
    public class ContactPhone : IObjectState, INotifyPropertyChanged
    {
        private Guid contactPhoneId;
        private Guid contactId;
        private PhoneType? type;
        private string phoneNumber;
        private bool notifyPropertyChangedEnabled = false;

        public ContactPhone()
        {
            contactPhoneId = Guid.CreateVersion7();
            phoneNumber = string.Empty;
            type = PhoneType.Cell;
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            ObjectState = ObjectState.Unchanged;
            RowVersion = new byte[0];
        }

        internal static ContactPhone NewContactPhone()
        {
            ContactPhone contactPhone = new ContactPhone();
            contactPhone.ObjectState = ObjectState.Added;

            return contactPhone;
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactPhoneResource), Name = "ContactPhoneId")]
        public Guid ContactPhoneId
        {
            get
            {
                return contactPhoneId;
            }
            set
            {
                if (value != contactPhoneId)
                {
                    contactPhoneId = value;
                }
            }
        }

        public Guid ContactId
        {
            get
            {
                return contactId;
            }
            set
            {
                if (value != contactId)
                {
                    contactId = value;
                }
            }
        }

        [ForeignKey("ContactId")]
        public Contact? Contact { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactPhoneResource), Name = "Type")]
        public PhoneType? Type
        {
            get
            {
                return type;
            }
            set
            {
                if (value != type)
                {
                    type = value;
                    NotifyPropertyChanged(nameof(Type));
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Contacts.ContactPhoneResource), Name = "PhoneNumber")]
        public string PhoneNumber
        {
            get
            {
                return phoneNumber;
            }
            set
            {
                if (value != phoneNumber)
                {
                    phoneNumber = value ?? "";
                    NotifyPropertyChanged(nameof(PhoneNumber));
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateCreated")]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateCreatedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateModified")]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        //[NotMapped]
        //public string? TypeStr
        //{
        //    get
        //    {
        //        return type.ToString();
        //    }
        //    set
        //    {
        //        List<string> types = Enum.GetValues(typeof(Data.Model.PhoneType)).Cast<Data.Model.PhoneType?>().Select(x => x.Value.ToString()).ToList();
        //        if (types.Contains(value ?? ""))
        //        {
        //            Type = (Data.Model.PhoneType)Enum.Parse(typeof(Data.Model.PhoneType), value ?? "");
        //        }
        //        else
        //        {
        //            throw new Exception("Wrong enum string in ContactPhone.TypeStr");
        //        }
        //    }
        //}

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        [NotMapped]
        internal bool NotifyPropertyChangedEnabled
        {
            get
            {
                return this.notifyPropertyChangedEnabled;
            }
            set
            {
                this.notifyPropertyChangedEnabled = value;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged = null;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (notifyPropertyChangedEnabled == true)
            {
                //DateModifiedUtc = DateTime.UtcNow;
                if (ObjectState == ObjectState.Unchanged)
                {
                    ObjectState = ObjectState.Modified;  //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
                }

                if (PropertyChanged != null)
                {
                    PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
                }
            }
        }
    }
}
