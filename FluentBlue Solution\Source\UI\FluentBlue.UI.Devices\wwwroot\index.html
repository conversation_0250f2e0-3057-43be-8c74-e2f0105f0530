<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>FluentBlue.UI.Devices</title>
    <base href="/" />
    <link href="_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css" rel="stylesheet" />
    <link rel="stylesheet" href="_content/FluentBlue.UI.Main/css/app.css" />
    <link rel="stylesheet" href="_content/FluentBlue.UI.Main/css/tailwind.css" />


    <!--Syncfusion-->
    <link id="syncfusionTheme" href="_content/Syncfusion.Blazor.Themes/fluent2.css" rel="stylesheet" />
    <!--<link href="_content/Syncfusion.Blazor.Themes/fluent-dark.css" rel="stylesheet" />-->
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>

    <link rel="stylesheet" href="FluentBlue.UI.Devices.styles.css" />  <!-- TODO: Θέλουν φτιάξιμο τα 2 index.html, κάποια αρχεία css δεν υπάρχουν πλέον.-->
    <link rel="icon" type="image/png" href="favicon.png" />

    <script src="_content/FluentBlue.UI.Main/js/common.js" type="text/javascript"></script>
    <script src="_content/FluentBlue.UI.Main/js/Sortable.min.js" type="text/javascript"></script>
</head>

<body style="min-height:100vh;">
    <div class="status-bar-safe-area"></div>

    <div id="app" style="width:100%; height:100%; display:block">
        <div style="display: flex; justify-content: center;  align-items: center;  height: 800px;">
            <img src="_content/FluentBlue.UI.Main/icon-512.png" style="margin:auto;" class="xs:w-40 sm:w-40 md:w-80 lg:w-80 xl:w-80" />
        </div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <!--<script app-name="FluentBlue.UI.Devices" src="./_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js"></script>-->
    <script src="_framework/blazor.webview.js" autostart="false"></script>

    <!-- Set the default theme -->
    <script src="_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js" type="text/javascript"></script>
    <loading-theme storage-name="Theme"></loading-theme>

    <!--Script for Syncfusion theme-->
    <script>
        function setTheme(theme) {
            document.getElementsByTagName('body')[0].style.display = 'none';
            let synclink = document.getElementById('syncfusionTheme');
            //synclink.href = '_content/Syncfusion.Blazor.Themes/' + theme + '.css';
            synclink.href = '_content/FluentBlue.UI.Main/css/' + theme + '.css';
            setTimeout(function () { document.getElementsByTagName('body')[0].style.display = 'block'; }, 300);
        }
    </script>
</body>

</html>