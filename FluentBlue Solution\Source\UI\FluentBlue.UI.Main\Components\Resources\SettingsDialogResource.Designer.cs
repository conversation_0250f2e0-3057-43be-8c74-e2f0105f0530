﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace FluentBlue.UI.Main.Components.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SettingsDialogResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SettingsDialogResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("FluentBlue.UI.Main.Components.Resources.SettingsDialogResource", typeof(SettingsDialogResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appearance.
        /// </summary>
        public static string AppearanceTitle {
            get {
                return ResourceManager.GetString("AppearanceTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Behaviour.
        /// </summary>
        public static string BehaviourTitle {
            get {
                return ResourceManager.GetString("BehaviourTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar.
        /// </summary>
        public static string Calendar {
            get {
                return ResourceManager.GetString("Calendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default view.
        /// </summary>
        public static string CalendarDefaultView {
            get {
                return ResourceManager.GetString("CalendarDefaultView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default view on mobiles.
        /// </summary>
        public static string CalendarDefaultViewOnMobiles {
            get {
                return ResourceManager.GetString("CalendarDefaultViewOnMobiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scroll to time.
        /// </summary>
        public static string CalendarScrollToTime {
            get {
                return ResourceManager.GetString("CalendarScrollToTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time interval.
        /// </summary>
        public static string CalendarTimeScaleInterval {
            get {
                return ResourceManager.GetString("CalendarTimeScaleInterval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work days.
        /// </summary>
        public static string CalendarWorkDays {
            get {
                return ResourceManager.GetString("CalendarWorkDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work hours.
        /// </summary>
        public static string CalendarWorkHours {
            get {
                return ResourceManager.GetString("CalendarWorkHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color.
        /// </summary>
        public static string Color {
            get {
                return ResourceManager.GetString("Color", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Contact Categories.
        /// </summary>
        public static string ContactCategoriesBtn_Text {
            get {
                return ResourceManager.GetString("ContactCategoriesBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Categories.
        /// </summary>
        public static string ContactCategoriesTitle {
            get {
                return ResourceManager.GetString("ContactCategoriesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        public static string Contacts {
            get {
                return ResourceManager.GetString("Contacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regional Settings.
        /// </summary>
        public static string CultureInfo {
            get {
                return ResourceManager.GetString("CultureInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date format.
        /// </summary>
        public static string DateFormat {
            get {
                return ResourceManager.GetString("DateFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End hour.
        /// </summary>
        public static string EndHour {
            get {
                return ResourceManager.GetString("EndHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Event Categories.
        /// </summary>
        public static string EventCategoriesBtn_Text {
            get {
                return ResourceManager.GetString("EventCategoriesBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event Categories.
        /// </summary>
        public static string EventCategoriesTitle {
            get {
                return ResourceManager.GetString("EventCategoriesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Event States.
        /// </summary>
        public static string EventStatesBtn_Text {
            get {
                return ResourceManager.GetString("EventStatesBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event States.
        /// </summary>
        public static string EventStatesTitle {
            get {
                return ResourceManager.GetString("EventStatesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First day of week.
        /// </summary>
        public static string FirstDayOfWeek {
            get {
                return ResourceManager.GetString("FirstDayOfWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Friday.
        /// </summary>
        public static string Friday {
            get {
                return ResourceManager.GetString("Friday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General.
        /// </summary>
        public static string General {
            get {
                return ResourceManager.GetString("General", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change of language requires restarting the app. Are you sure you want to change the language?.
        /// </summary>
        public static string LanguageChangeRequiresRestartConfirmation {
            get {
                return ResourceManager.GetString("LanguageChangeRequiresRestartConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language &amp; Time.
        /// </summary>
        public static string LanguageTimeTitle {
            get {
                return ResourceManager.GetString("LanguageTimeTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monday.
        /// </summary>
        public static string Monday {
            get {
                return ResourceManager.GetString("Monday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Role.
        /// </summary>
        public static string NewRoleBtn_Text {
            get {
                return ResourceManager.GetString("NewRoleBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New User.
        /// </summary>
        public static string NewUserBtn_Text {
            get {
                return ResourceManager.GetString("NewUserBtn.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ρόλος.
        /// </summary>
        public static string RoleName {
            get {
                return ResourceManager.GetString("RoleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role does not exist..
        /// </summary>
        public static string RoleNotExists {
            get {
                return ResourceManager.GetString("RoleNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roles.
        /// </summary>
        public static string Roles {
            get {
                return ResourceManager.GetString("Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday.
        /// </summary>
        public static string Saturday {
            get {
                return ResourceManager.GetString("Saturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include SSN in lists of contacts.
        /// </summary>
        public static string ShowSsnInContactLists {
            get {
                return ResourceManager.GetString("ShowSsnInContactLists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include TIN in lists of contacts.
        /// </summary>
        public static string ShowTinInContactLists {
            get {
                return ResourceManager.GetString("ShowTinInContactLists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start hour.
        /// </summary>
        public static string StartHour {
            get {
                return ResourceManager.GetString("StartHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunday.
        /// </summary>
        public static string Sunday {
            get {
                return ResourceManager.GetString("Sunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theme.
        /// </summary>
        public static string Theme {
            get {
                return ResourceManager.GetString("Theme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thursday.
        /// </summary>
        public static string Thursday {
            get {
                return ResourceManager.GetString("Thursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time format.
        /// </summary>
        public static string TimeFormat {
            get {
                return ResourceManager.GetString("TimeFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time zone.
        /// </summary>
        public static string TimeZone {
            get {
                return ResourceManager.GetString("TimeZone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tuesday.
        /// </summary>
        public static string Tuesday {
            get {
                return ResourceManager.GetString("Tuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string UserEmail {
            get {
                return ResourceManager.GetString("UserEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string UserFullName {
            get {
                return ResourceManager.GetString("UserFullName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User does not exist..
        /// </summary>
        public static string UserNotExists {
            get {
                return ResourceManager.GetString("UserNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users &amp; Roles.
        /// </summary>
        public static string UsersRoles {
            get {
                return ResourceManager.GetString("UsersRoles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wednesday.
        /// </summary>
        public static string Wednesday {
            get {
                return ResourceManager.GetString("Wednesday", resourceCulture);
            }
        }
    }
}
