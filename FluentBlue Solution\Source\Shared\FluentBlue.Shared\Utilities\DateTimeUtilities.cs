﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Shared.Utilities
{
    public class DateTimeUtilities
    {
        public static bool HasOverlappingDates(List<(DateTime Start, DateTime End)> dateRanges)
        {
            // Convert to list and sort by start date
            var sortedRanges = dateRanges.OrderBy(x => x.Start).ToList();

            // If there are less than 2 ranges, there can't be any overlap
            if (sortedRanges.Count < 2)
                return false;

            // Compare each range with the next one
            for (int i = 0; i < sortedRanges.Count - 1; i++)
            {
                if (sortedRanges[i].End > sortedRanges[i + 1].Start)
                {
                    return true;
                }
            }

            return false;
        }
    }
}
