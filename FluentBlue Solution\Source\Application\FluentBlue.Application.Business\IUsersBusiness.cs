﻿using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Response;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared;

namespace FluentBlue.Application.Business
{
    public interface IUsersBusiness
    {
        Task<bool> CheckUsernamePasswordExists(string username, string password);
        Task<User?> GetUser(string username, string password);
        Task<User?> GetUser(Guid userId);
        Task<PagedData<List<UserView>>> GetUsers(ReadPagedDataParameters parameters);
        Task<List<UserLI>> GetUsersLI(Guid tenantId);
        Task CreateOrUpdateUser(User user);
        Task DeleteUser(Guid userId);
        Task<User?> CheckUserExists(Guid tenantId, Guid userId, string email);
        Task<int> GetUsersCount(Guid tenantId);
    }
}