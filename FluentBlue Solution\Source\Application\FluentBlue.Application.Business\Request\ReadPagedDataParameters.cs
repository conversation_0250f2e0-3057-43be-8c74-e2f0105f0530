﻿using FluentBlue.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Application.Business.Request
{
    public class ReadPagedDataParameters
    {
        public Guid TenantId { get; set; }  
        public string Filter { get; set; } = string.Empty;
        public int PageIndex { get; set; } = 0;
        public int PageSize { get; set; } = 10;
        public Dictionary<string, SortOrder> SortColumns { get; set; } = new Dictionary<string, SortOrder>();
    }
}
