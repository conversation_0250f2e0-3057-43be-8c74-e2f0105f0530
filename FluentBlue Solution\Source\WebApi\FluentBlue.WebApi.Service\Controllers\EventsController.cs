﻿using AutoMapper;
using Azure.Core;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Shared;
using FluentBlue.Shared.SignalR;
using FluentBlue.WebApi.Service.Hubs;
using FluentBlue.WebApi.Service.Resources;
using FluentBlue.WebApi.Shared.Request;
using FluentBlue.WebApi.Shared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace FluentBlue.WebApi.Service.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class EventsController : ControllerBase
    {
        private IEventsBusiness eventsBusiness;
        private ILogger<EventsController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;
        // Add to constructor parameters:
        private readonly IHubContext<NotificationHub> notificationHub;

        public EventsController(IEventsBusiness eventsBusiness, ILogger<EventsController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo, IHubContext<NotificationHub> notificationHub)
        {
            try
            {
                this.eventsBusiness = eventsBusiness;
                this.logger = logger;
                this.mapper = mapper;
                this.webApiCallerInfo = webApiCallerInfo;
                this.notificationHub = notificationHub;
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }
        }

        /// <summary>
        /// Retrieves events in paged mode.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Events/Get")]
        public async Task<ApiResponse<List<Data.Model.DBOs.Calendar.Event>>> GetEvents([FromBody] Shared.Request.RequestEventsParameters parameters)
        {
            try
            {
                //Validation
                parameters.Filter = parameters.Filter ?? "";

                //Map data
                Application.Business.Request.RequestEventsParameters requestEventsParameters = this.mapper.Map<Application.Business.Request.RequestEventsParameters>(parameters);

                //Query
                List<FluentBlue.Data.Model.DBOs.Calendar.Event> data = await this.eventsBusiness.GetEvents(requestEventsParameters);

                //List<Data.Models.Event> eventsDto = TinyMapper.Map<List<Data.Models.Event>, List<Data.Models.Event>>(events);

                //Response
                return new ApiResponse<List<Data.Model.DBOs.Calendar.Event>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<Data.Model.DBOs.Calendar.Event>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception exp)
            {
                //ExceptionHandler.RecordException(exp);
                return new ApiResponse<List<Data.Model.DBOs.Calendar.Event>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = exp.Message };
            }
        }

        [HttpGet]
        [Route("Event/Get")]
        public async Task<ApiResponse<Event>> GetEvent([FromQuery] string eventId)
        {
            try
            {
                //Query
                Event? eventObj = await this.eventsBusiness.GetEvent(Guid.Parse(eventId));

                //Response
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = eventObj };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                //ExceptionHandler.RecordException(exp);
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpPost]
        [Route("Event/Create")]
        public async Task<ApiResponse<Event>> CreateEvent([FromBody] SaveEventRequest request)
        {
            try
            {
                //Validation
                if (request.Event == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (request.Event.ObjectState != Data.Model.ObjectState.Added)
                {
                    return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = GlobalResource.WrongImplementation };
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Query
                await this.eventsBusiness.CreateOrUpdateEvent(request.Event, null, request.RecurrenceDates);

                #region  Notifies all client apps.
                try
                {
                    NotificationData notificationData = new NotificationData();
                    notificationData.NotificationType = "EventChanged";
                    notificationData.SenderId = webApiCallerInfo.ApplicationId?.ToString() ?? "";
                    string notificationDataJson = System.Text.Json.JsonSerializer.Serialize(notificationData);  //convert payload to JSON string
                    await notificationHub.Clients.Group(webApiCallerInfo.TenantId.ToString()).SendAsync("FluentBlueNotification", notificationDataJson);
                }
                catch (Exception ex)
                {
                    this.logger.LogError(ex, ex.Message, request.Event);
                }
                #endregion

                //Response
                Event? conctact = await this.eventsBusiness.GetEvent(request.Event.EventId);
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = request.Event };
            }
            catch (ApplicationException ex)
            {
                //this.logger.LogError(ex, ex.Message, eventObj);
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, request.Event);
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpPost]
        [Route("Event/Update")]
        public async Task<ApiResponse<Event>> UpdateEvent([FromBody] SaveEventRequest request)
        {
            try
            {
                //Validation
                if (request == null)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                #region Query
                await this.eventsBusiness.CreateOrUpdateEvent(request.Event, request.RecurrentEventUpdateType, request.RecurrenceDates);



                //Event eventObj = request.Event;

                ////If we create a new Event
                //if (eventObj.ObjectState == Data.Model.ObjectState.Added)
                //{
                //    //If there is no recurrence.
                //    if (eventObj.CustomRecurrence == false)
                //    {
                //        await this.eventsBusiness.CreateOrUpdateEvent(eventObj);
                //    }
                //    else  //If there is recurrence.
                //    {
                //        List<Event> recurrenceEvents = this.eventsBusiness.GenerateRecurrentEvents(eventObj, request.RecurrenceDates, eventObj.CustomRecurrenceRule, Guid.CreateVersion7());
                //        await this.eventsBusiness.CreateOrUpdateEventsBatch(recurrenceEvents);
                //    }
                //}
                ////If we update the Event.
                //else if (eventObj.ObjectState == Data.Model.ObjectState.Modified)
                //{

                //}

                #endregion

                #region  Notifies all client apps.
                try
                {
                    NotificationData notificationData = new NotificationData();
                    notificationData.NotificationType = "EventChanged";
                    notificationData.SenderId = webApiCallerInfo.ApplicationId?.ToString() ?? "";
                    string notificationDataJson = System.Text.Json.JsonSerializer.Serialize(notificationData);  //convert payload to JSON string
                    await notificationHub.Clients.Group(webApiCallerInfo.TenantId.ToString()).SendAsync("FluentBlueNotification", notificationDataJson);
                }
                catch (Exception ex)
                {
                    this.logger.LogError(ex, ex.Message, request.Event);
                }
                #endregion

                //Response
                Event? conctact = await this.eventsBusiness.GetEvent(request.Event.EventId);
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = request.Event };
            }
            catch (DbUpdateConcurrencyException ex)
            {
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.DbConcurrencyException };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, request.Event);
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        //[HttpPost]
        //[Route("Event/CreateOrUpdate")]
        //public async Task<ApiResponse<Event>> CreateOrUpdateEvent([FromBody] Event eventObj)
        //{
        //    try
        //    {
        //        //Validation
        //        if (eventObj == null)
        //        {
        //            throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
        //        }

        //        if (ModelState.IsValid == false)
        //        {
        //            throw new Exception(ModelState.Values.ToString());
        //        }

        //        //Query
        //        await this.eventsBusiness.CreateOrUpdateEvent(eventObj);

        //        //Response
        //        Event? conctact = await this.eventsBusiness.GetEvent(eventObj.EventId);
        //        return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = eventObj };
        //    }
        //    catch (ApplicationException ex)
        //    {
        //        //this.logger.LogError(ex, ex.Message, eventObj);
        //        return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
        //    }
        //    catch (Exception ex)
        //    {
        //        this.logger.LogError(ex, ex.Message, eventObj);
        //        return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
        //    }
        //}

        [HttpPost]
        [Route("Events/CreateOrUpdateBatch")]
        public async Task<ApiResponse> CreateOrUpdateEventsBatch([FromBody] List<Event> events)
        {
            try
            {
                // Validation
                if (events == null || events.Count == 0 || events.Count > 50) //Batch events should not exceed a huge number, in case of an attack.
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (!ModelState.IsValid)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                // Query
                await this.eventsBusiness.SaveEventsBatch(events);

                #region  Notifies all client apps.
                try
                {
                    NotificationData notificationData = new NotificationData();
                    notificationData.NotificationType = "EventChanged";
                    notificationData.SenderId = webApiCallerInfo.ApplicationId?.ToString() ?? "";
                    string notificationDataJson = System.Text.Json.JsonSerializer.Serialize(notificationData);  //convert payload to JSON string
                    await notificationHub.Clients.Group(webApiCallerInfo.TenantId.ToString()).SendAsync("FluentBlueNotification", notificationDataJson);
                }
                catch (Exception ex)
                {
                    this.logger.LogError(ex, ex.Message, events);
                }
                #endregion

                // Response
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, events);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpPost]
        [Route("Event/Delete")]
        public async Task<ApiResponse> DeleteEvent([FromBody] DeleteEventRequest deleteEventRequest)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                this.eventsBusiness.DeleteEvent(deleteEventRequest.EventId, deleteEventRequest.RecurrentEventUpdateType);

                #region  Notifies all client apps.
                try
                {
                    NotificationData notificationData = new NotificationData();
                    notificationData.NotificationType = "EventChanged";
                    notificationData.SenderId = webApiCallerInfo.ApplicationId?.ToString() ?? "";
                    string notificationDataJson = System.Text.Json.JsonSerializer.Serialize(notificationData);  //convert payload to JSON string
                    await notificationHub.Clients.Group(webApiCallerInfo.TenantId.ToString()).SendAsync("FluentBlueNotification", notificationDataJson);
                }
                catch (Exception ex)
                {
                    this.logger.LogError(ex, ex.Message, deleteEventRequest);
                }
                #endregion

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (DbUpdateConcurrencyException ex)
            {
                return new ApiResponse<Event>() { ResultCode = ApiResponseResultCode.DbConcurrencyException };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "EventId=" + deleteEventRequest.EventId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        /// <summary>
        /// Retrieves events for export with all related data (Contact, EventCategory, EventState).
        /// </summary>
        /// <param name="request">Request containing the list of event IDs to export</param>
        /// <returns>List of events with related data for export</returns>
        [HttpPost]
        [Route("Events/GetForExport")]
        public async Task<ApiResponse<List<Event>>> GetEventsForExport([FromBody] ExportEventsRequest request)
        {
            try
            {
                //Validation
                if (request == null || request.EventIds == null || request.EventIds.Count == 0)
                {
                    throw new Exception(FluentBlue.WebApi.Service.Resources.GlobalResource.InvalidDataMessage);
                }

                if (request.EventIds.Count > 1000) // Limit to prevent performance issues
                {
                    throw new Exception("Too many events requested for export. Maximum allowed is 1000.");
                }

                //Query
                List<Event> events = await this.eventsBusiness.GetEventsForExport(request.EventIds);

                //Set the TimeZoneId
                if(!string.IsNullOrEmpty(request.TimeZoneId))
                {
                    foreach (var evnt in events)
                    {
                        evnt.UserTimeZoneId = request.TimeZoneId;
                    }
                }

                //Response
                return new ApiResponse<List<Event>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = events };
            }
            catch (ApplicationException ex)
            {
                return new ApiResponse<List<Event>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { "EventIds=" + string.Join(",", request?.EventIds ?? new List<Guid>()) });
                return new ApiResponse<List<Event>>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

    }
}