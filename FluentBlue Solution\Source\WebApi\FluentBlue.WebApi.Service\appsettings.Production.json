{
  "FluentBlueConnectionString": "Server=fluentbluesqlsrv.database.windows.net,1433;Initial Catalog=FluentBlue;Persist Security Info=False;User ID=fluentBlueSqlAdmin;Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}

  //"FluentBlueConnectionString": "Data Source=winzone50.grserver.gr,1555;User ID=FluentBlueAdmin;Password=**********;Encrypt=False",