﻿using FluentBlue.Data.Model.DBOs.Contacts;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model.DBOs.Tenants
{
    [Table("User", Schema = "Tenants")]
    public class User : IObjectState, INotifyPropertyChanged
    {
        private string firstName;
        private string middleName;
        private string lastName;
        //private string userTimeZoneId;

        public User()
        {
            UserId = Guid.CreateVersion7();
            firstName = string.Empty;
            middleName = string.Empty;
            lastName = string.Empty;
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            RowVersion = new byte[0];
            ObjectState = ObjectState.Unchanged;
            //this.userTimeZoneId = string.Empty;
        }

        [Key]
        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.UserId))]
        public Guid UserId { get; set; }

        [Required]
        public Guid? RoleId { get; set; }

        [ForeignKey("RoleId")]
        public Role? Role { get; set; }

        public Guid TenantId { get; set; }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.LastName))]
        //[MaxLength(100)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string LastName
        {
            get
            {
                return lastName;
            }
            set
            {
                lastName = value ?? string.Empty;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Tenants.UserDisplayResource), Name = nameof(Model.Resources.Tenants.UserDisplayResource.FirstName))]
        public string FirstName
        {
            get
            {
                return firstName;
            }
            set
            {
                firstName = value ?? string.Empty;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Tenants.UserDisplayResource), Name = nameof(Model.Resources.Tenants.UserDisplayResource.MiddleName))]
        //[MaxLength(100)]
        //[DefaultValue("")]
        public string MiddleName
        {
            get
            {
                return middleName;
            }
            set
            {
                middleName = value ?? string.Empty;
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Email))]
        //[MaxLength(50)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Email { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Username))]
        //[MaxLength(30)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Username { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Password))]
        //[MaxLength(30)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Password { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Phone))]
        //[MaxLength(50)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Phone { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.Mobile))]
        //[MaxLength(50)]
        //[Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Data.Model.Resources.GeneralValidationResource))]
        public string Mobile { get; set; } = string.Empty;

        [Display(ResourceType = typeof(Data.Model.Resources.Tenants.UserDisplayResource), Name = nameof(Data.Model.Resources.Tenants.UserDisplayResource.AvailableInEventUsers))]
        public bool AvailableInEventUsers { get; set; } = true;

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = "DateCreated")]
        public DateTime DateCreatedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        public DateTime? DateCreatedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateCreatedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Data.Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime DateModifiedUtc { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        public DateTime? DateModifiedLocal
        {
            get
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "")
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    return TimeZoneInfo.ConvertTimeFromUtc(DateModifiedUtc, userTimeZone);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                if (UserTimeZoneId != null && UserTimeZoneId != "" && value != null)
                {
                    TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                    DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, userTimeZone);
                }
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; } = new byte[0];


        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        [NotMapped]
        public ObjectState ObjectState { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            //DateModifiedUtc = DateTime.UtcNow;

            //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
            //if (ObjectState == ObjectState.Unchanged)
            //{
            //    ObjectState = ObjectState.Modified;
            //}

            if (PropertyChanged != null)
            {
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
            }

        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Tenants.UserDisplayResource), Name = nameof(Model.Resources.Tenants.UserDisplayResource.FullName))]
        public string FullName
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }

        [NotMapped]
        public string Initials
        {
            get
            {
                return (FirstName.Substring(0, 1) ?? "") + (LastName.Substring(0, 1) ?? "");
            }
        }
        
        public static User CreateUser(Guid tenantId)
        {
            User user = new User();
            user.TenantId = tenantId;
            user.ObjectState = ObjectState.Added;

            return user;
        }
    }
}
