﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using FluentValidation;
using System.ComponentModel;
using System.Linq.Expressions;
using FluentBlue.Shared.Utilities;
using Microsoft.JSInterop;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.AspNetCore.Components.Forms;

namespace FluentBlue.UI.Main.Components
{
    public partial class EventStatesDialog
    {
        [Parameter] public List<EventState>? Content { get; set; } = new List<EventState>();
        private EditContext eventStatesContext = new EditContext(typeof(Data.Model.DBOs.Calendar.EventState));
        [CascadingParameter] public FluentDialog CurrentDialog { get; set; } = default!;
        private FluentValidationValidator? fluentValidationValidator = new FluentValidationValidator();
        private UserSetting? userSetting;
        private EventStateValidator eventStateValidator = new EventStateValidator();
        private bool isSaving = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (fluentValidationValidator != null)
                {
                    fluentValidationValidator.Validator = eventStateValidator;
                }

                // Subscribe to PropertyChanged event for each EventState in Content
                if (Content != null)
                {
                    foreach (var category in Content)
                    {
                        category.PropertyChanged += OnEventStatePropertyChanged;
                    }
                }

                await base.OnInitializedAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void OnEventStatePropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                // Handle property change
                if (sender is EventState category)
                {
                    if (category.ObjectState == ObjectState.Unchanged)
                    {
                        category.ObjectState = ObjectState.Modified;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    // Reads the UserSettings from cache or database
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    EventStatesWebApiClient eventStatesWebApiClient = new EventStatesWebApiClient(httpClient, eventStatesWebApiClientLogger);
                    Content = await eventStatesWebApiClient.GetAllEventStates(AuthenticatedUserData.TenantId);

                    // Set timezone for each category
                    foreach (var category in Content!)
                    {
                        category.UserTimeZoneId = this.userSetting!.TimeZone;
                    }

                    StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CancelBtnOnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtnOnClick(EventState category)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();

                if (result.Cancelled == false)
                {
                    if (category.ObjectState == ObjectState.Added)
                    {
                        Content!.Remove(category);
                    }
                    else
                    {
                        EventStatesWebApiClient eventStatesWebApiClient = new EventStatesWebApiClient(httpClient, eventStatesWebApiClientLogger);
                        await eventStatesWebApiClient.DeleteEventState(category.EventStateId);
                        Content!.Remove(category);
                    }
                    StateHasChanged();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task AddBtnOnClick()
        {
            try
            {
                var newState = new EventState
                {
                    EventStateId = Guid.CreateVersion7(),
                    TenantId = AuthenticatedUserData.TenantId,
                    Name = "",
                    BackColor = "",
                    SortIndex = Content!.Any() ? Content!.Max(x => x.SortIndex) + 1 : 0,
                    ObjectState = ObjectState.Added,
                    UserTimeZoneId = this.userSetting!.TimeZone
                };

                newState.PropertyChanged += OnEventStatePropertyChanged;
                Content!.Add(newState);
                StateHasChanged();
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                this.isSaving = true;

                if (await ValidateData())
                {
                    EventStatesWebApiClient eventStatesWebApiClient = new EventStatesWebApiClient(httpClient, eventStatesWebApiClientLogger);

                    // Update sort indexes
                    for (int i = 0; i < Content!.Count; i++)
                    {
                        Content[i].SortIndex = i;
                    }

                    // Save each category
                    foreach (var category in Content!)
                    {
                        if (category.ObjectState != ObjectState.Unchanged)
                        {
                            if (category.ObjectState == ObjectState.Added)
                            {
                                category.DateCreatedUtc = DateTime.UtcNow;
                            }
                            category.DateModifiedUtc = DateTime.UtcNow;

                            await eventStatesWebApiClient.CreateOrUpdateEventState(category);
                        }
                    }

                    // Clears the EventStates from cache
                    await cache.RemoveAsync(Keywords.EventStates);

                    // Reads the EventStates from the database
                    Content = await eventStatesWebApiClient.GetAllEventStates(AuthenticatedUserData.TenantId);
                    Content.Insert(0, new EventState() { EventStateId = Guid.Empty, Name = "", BackColor = "" });  //Προσθέτει το κενό EventState (αντιπροσωπεύει το null).

                    // Stores the EventStates in cache
                    var eventStatesCacheOptions = new HybridCacheEntryOptions
                    {
                        LocalCacheExpiration = TimeSpan.FromMinutes(60)
                    };
                    await cache.SetAsync(Keywords.EventStates, Content, eventStatesCacheOptions);

                    await this.CurrentDialog.CloseAsync(Content);
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {
                this.isSaving = false;
            }
        }

        private async Task<bool> ValidateData()
        {
            try
            {
                bool isValid = true;
                List<string> errorMessages = new List<string>();

                EventStateValidator validator = new EventStateValidator();
                this.fluentValidationValidator.Validator = validator;
                bool valid = this.fluentValidationValidator!.Validate();

                foreach (var category in Content!)
                {
                    var validationResult = await eventStateValidator.ValidateAsync(category);
                    if (!validationResult.IsValid)
                    {
                        isValid = false;
                        errorMessages.AddRange(validationResult.Errors.Select(e => e.ErrorMessage));
                    }
                }

                //Checks if the same EventState title exists 2 times.
                if (Content!.GroupBy(x => x.Name).Any(x => x.Count() > 1))
                {
                    isValid = false;
                    errorMessages.Add(Resources.EventStatesDialogResource.NameAlreadyExists);
                }

                if (!isValid)
                {
                    string errorMessage = GlobalResource.CorrectInvalidFields;
                    RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, errorMessages.Distinct().ToArray(), "");

                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                return isValid;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void HandleReorder(FluentSortableListEventArgs args)
        {
            try
            {
                if (args is null || args.OldIndex == args.NewIndex)
                {
                    return;
                }

                var oldIndex = args.OldIndex;
                var newIndex = args.NewIndex;

                var tempItems = this.Content;
                var itemToMove = this.Content![oldIndex];
                tempItems!.RemoveAt(oldIndex);

                if (newIndex < Content.Count)
                {
                    tempItems.Insert(newIndex, itemToMove);
                }
                else
                {
                    tempItems.Add(itemToMove);
                }

                // Update sort indexes
                for (int i = 0; i < tempItems!.Count; i++)
                {
                    tempItems[i].SortIndex = i;
                    if (tempItems[i].ObjectState == ObjectState.Unchanged)
                    {
                        tempItems[i].ObjectState = ObjectState.Modified;
                    }
                }

                this.Content = tempItems.OrderBy(x => x.SortIndex).ToList();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
    }
}
