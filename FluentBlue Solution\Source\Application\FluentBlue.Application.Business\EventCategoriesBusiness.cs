﻿using AutoMapper;
using FluentBlue.Application.Business.Request;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;

namespace FluentBlue.Application.Business
{
    public class EventCategoriesBusiness : IEventCategoriesBusiness
    {
        private FluentBlue.Data.Model.FluentBlueDbContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public EventCategoriesBusiness(FluentBlue.Data.Model.FluentBlueDbContext dbContext, IMapper mapper, ILogger<EventCategoriesBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<FluentBlue.Data.Model.DBOs.Calendar.EventCategory>> GetEventCategories(Guid tenantId)
        {
            try
            {
                //Validation

                //Query

                IQueryable<FluentBlue.Data.Model.DBOs.Calendar.EventCategory> query = this.dbContext.EventCategories.AsQueryable();

                query = query.AsNoTracking().Where(x => x.TenantId == tenantId).OrderBy(x => x.SortIndex);

                //Διαβάζει τα δεδομένα.
                List<FluentBlue.Data.Model.DBOs.Calendar.EventCategory> eventCategories = await query.AsNoTracking().ToListAsync();

                //Response
                return eventCategories;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public async Task<Data.Model.DBOs.Calendar.EventCategory?> GetEventCategory(Guid eventCategoryId)
        {
            try
            {
                //Query
                IQueryable<EventCategory> query = dbContext.EventCategories.AsQueryable();
                query = query.Where(c => c.EventCategoryId.ToString() == eventCategoryId.ToString());

                return await query.AsNoTracking().FirstOrDefaultAsync<EventCategory>();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "EventCategoryId=" + eventCategoryId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateEventCategory(EventCategory eventCategoryObj)
        {
            try
            {
                #region Validation
                if (eventCategoryObj == null)
                {
                    throw new Exception(Resources.GlobalResource.InvalidDataMessage);
                }

                List<ValidationResult> validationResults = new List<ValidationResult>();
                ValidationContext validationContext = new ValidationContext(eventCategoryObj);
                if (Validator.TryValidateObject(eventCategoryObj, validationContext, validationResults, true) == false)
                {
                    string validationErrors = string.Empty;
                    foreach (CompositeValidationResult compValidationResult in validationResults)
                    {
                        foreach (ValidationResult validationResult in compValidationResult.Results)
                        {
                            validationErrors += validationResult.ErrorMessage + ". ";
                        }
                    }
                    throw new ApplicationException(validationErrors);
                }

                //Checks in database if Name alread exists
                FluentBlue.Data.Model.DBOs.Calendar.EventCategory? eventCategory = await this.dbContext.EventCategories.Where(x => x.TenantId == eventCategoryObj.TenantId && x.Name == eventCategoryObj.Name && x.EventCategoryId != eventCategoryObj.EventCategoryId).FirstOrDefaultAsync();
                if (eventCategory != null)
                {
                    throw new ApplicationException(Resources.EventCategoriesResource.NameAlreadyExists);
                }

                // In case we are deleting the EventCategory
                if (eventCategoryObj.ObjectState == ObjectState.Deleted)
                {
                    // Checks in database if this EventCategory is used in any Event
                    FluentBlue.Data.Model.DBOs.Calendar.Event? evt = await this.dbContext.Events.Where(x => x.EventCategoryId == eventCategoryObj.EventCategoryId).FirstOrDefaultAsync();
                    if (evt != null)
                    {
                        throw new ApplicationException(Resources.EventCategoriesResource.CannotDeleteAlreadyUsedCategory);
                    }
                }
                #endregion

                //Query
                this.dbContext.Attach(eventCategoryObj);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { eventCategoryObj.EventCategoryId });
                throw;
            }
        }

        public async Task DeleteEventCategory(Guid eventCategoryId)
        {
            try
            {
                EventCategory? eventCagegoryObj = await this.dbContext.EventCategories.Where(x => x.EventCategoryId == eventCategoryId).FirstAsync();
                if (eventCagegoryObj != null)
                {
                    // Checks in database if this EventCategory is used in any Event
                    FluentBlue.Data.Model.DBOs.Calendar.Event? evt = await this.dbContext.Events.Where(x => x.EventCategoryId == eventCategoryId).FirstOrDefaultAsync();
                    if (evt != null)
                    {
                        throw new ApplicationException(Resources.EventCategoriesResource.CannotDeleteAlreadyUsedCategory);
                    }

                    eventCagegoryObj.ObjectState = ObjectState.Deleted;
                    this.dbContext.Attach(eventCagegoryObj);
                    this.dbContext.SaveChanges();
                }
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { "EventCategoryId=" + eventCategoryId.ToString() });
                throw;
            }
        }
    }
}
