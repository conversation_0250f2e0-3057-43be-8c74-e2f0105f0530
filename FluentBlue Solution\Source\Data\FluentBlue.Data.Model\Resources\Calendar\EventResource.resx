﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllDay" xml:space="preserve">
    <value>All Day</value>
  </data>
  <data name="Block" xml:space="preserve">
    <value>Block</value>
  </data>
  <data name="ContactId" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Created</value>
  </data>
  <data name="DateModified" xml:space="preserve">
    <value>Modified</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="DescriptionMaxLengthError" xml:space="preserve">
    <value>Too many characters (2000 characters limit)</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="EndTimeMustBeAfterStartTime" xml:space="preserve">
    <value>End must be after Start.</value>
  </data>
  <data name="EndTimeZone" xml:space="preserve">
    <value>End Time Zone</value>
  </data>
  <data name="EventCategoryId" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="EventId" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="EventStateId" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="EventUsers" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="FifteenMinReminder" xml:space="preserve">
    <value>15 minutes before</value>
  </data>
  <data name="FiveMinReminder" xml:space="preserve">
    <value>5 minutes before</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="NoReminder" xml:space="preserve">
    <value>No reminder</value>
  </data>
  <data name="OneHourReminder" xml:space="preserve">
    <value>1 hour before</value>
  </data>
  <data name="PlainDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ReadOnly" xml:space="preserve">
    <value>Read Only</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="StartTimeZone" xml:space="preserve">
    <value>Start Time Zone</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="SubjectMaxLengthError" xml:space="preserve">
    <value>Too many characters (300 characters limit)</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="TenMinReminder" xml:space="preserve">
    <value>10 minutes before</value>
  </data>
  <data name="ThirtyMinReminder" xml:space="preserve">
    <value>30 minutes before</value>
  </data>
  <data name="ZeroMinReminder" xml:space="preserve">
    <value>At event time</value>
  </data>
</root>